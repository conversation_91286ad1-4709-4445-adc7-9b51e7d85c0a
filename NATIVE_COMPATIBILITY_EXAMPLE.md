# 🔄 原生库兼容性示例

## 📋 完全对应原生库的使用方式

### Android原生库使用方式

```java
// 1. 初始化
public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        TagUtils.initNetLoader(this, new MyImageLoader(), true);
        
        TagAppearance appearance = TagAppearance.Builder()
            .tagHeight(15F)
            .tagTextSize(11F)
            .cornerSizeDp(2f)
            .build();
        TagUtils.setDefaultAppearance(appearance);
    }
}

// 2. 使用标签
public class ProductActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_product);
        
        TextView textView = findViewById(R.id.product_title);
        
        List<GoodsTag> tags = Arrays.asList(
            new GoodsTag(1, "新品", "#FFFFFF", "#FF0000", null, null, null),
            new GoodsTag(3, "包邮", "#4CAF50", null, null, "#4CAF50", null),
            new GoodsTag(2, null, null, null, null, null, "https://example.com/icon.png")
        );
        
        // 显示标签
        TagUtils.showRectStart(this, textView, tags, "iPhone 15 Pro");
        TagUtils.showRoundEnd(this, textView, tags, "商品描述");
    }
}
```

### KMP Compose版本（完全对应）

```kotlin
// 1. 初始化
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        AppTag.init(MyImageLoader(), debug = true) // 完全对应原生的initNetLoader
    }
}

class MyImageLoader : ComposeImageLoader {
    @Composable
    override fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter? {
        // 对应原生的INetPicLoader
        return rememberAsyncImagePainter(url)
    }
}

// 2. 使用标签
@Composable
fun ProductScreen() {
    val tags = listOf(
        GoodsTag(form = 1, name = "新品", color = "#FFFFFF", bgcolor = "#FF0000"),
        GoodsTag(form = 3, name = "包邮", color = "#4CAF50", bordercolor = "#4CAF50"),
        GoodsTag(form = 2, rlink = "https://example.com/icon.png")
    )
    
    Column {
        // 完全对应原生的TagUtils.showRectStart
        AppTag.ShowRectStart(tags, "iPhone 15 Pro")
        
        // 完全对应原生的TagUtils.showRoundEnd  
        AppTag.ShowRoundEnd(tags, "商品描述")
        
        // 或者使用便捷方法（与原生API一致）
        ShowRectStart(
            tags = convertToTagBeans(tags), // 如果需要转换
            content = "iPhone 15 Pro",
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}

// 数据转换辅助函数（可选）
fun convertToTagBeans(goodsTags: List<GoodsTag>): List<TagBean> {
    return goodsTags.mapNotNull { goodsTag ->
        val form = goodsTag.form ?: return@mapNotNull null
        val name = goodsTag.name ?: return@mapNotNull null
        
        TagBean(
            type = when (form) {
                1 -> TagType.FILL
                2 -> TagType.IMAGE
                3 -> TagType.STROKE
                4 -> TagType.DISCOUNT
                5 -> TagType.POINTS
                -1 -> TagType.FILL_AND_STROKE
                else -> return@mapNotNull null
            },
            text = name,
            textColor = TagUtils.parseColor(goodsTag.color, Color.Black),
            backgroundColor = TagUtils.parseColor(goodsTag.bgcolor, Color.Transparent),
            borderColor = TagUtils.parseColor(goodsTag.bordercolor, Color.Transparent),
            imageUrl = goodsTag.rlink
        )
    }
}
```

## 🎯 API对应关系

### 初始化对应

| 原生Android | KMP Compose | 说明 |
|------------|-------------|------|
| `TagUtils.initNetLoader(context, loader, debug)` | `AppTag.init(loader, debug)` | 初始化图片加载器 |
| `TagUtils.setDefaultAppearance(appearance)` | 自动设置 | 在init中自动设置默认样式 |
| `INetPicLoader` | `ComposeImageLoader` | 图片加载器接口 |

### 显示方法对应

| 原生Android | KMP Compose | 说明 |
|------------|-------------|------|
| `TagUtils.showRectStart(context, textView, tags, content)` | `AppTag.ShowRectStart(tags, content)` | 矩形标签在前 |
| `TagUtils.showRectEnd(context, textView, tags, content)` | `AppTag.ShowRectEnd(tags, content)` | 矩形标签在后 |
| `TagUtils.showRoundStart(context, textView, tags, content)` | `AppTag.ShowRoundStart(tags, content)` | 圆角标签在前 |
| `TagUtils.showRoundEnd(context, textView, tags, content)` | `AppTag.ShowRoundEnd(tags, content)` | 圆角标签在后 |

### 数据结构对应

| 原生Android | KMP Compose | 说明 |
|------------|-------------|------|
| `GoodsTag` | `GoodsTag` | 完全一致的数据结构 |
| `TagBean` | `TagBean` | 内部标签数据结构 |
| `TagAppearance` | `TagAppearance` | 样式配置类 |

### 标签类型对应

| 原生常量 | 数值 | KMP枚举 | 说明 |
|---------|------|---------|------|
| `FORM_FILL` | 1 | `TagType.FILL` | 填充背景 |
| `FORM_IMAGE` | 2 | `TagType.IMAGE` | 图片标签 |
| `FORM_STROKE` | 3 | `TagType.STROKE` | 镂空边框 |
| `FORM_ZS` | 4 | `TagType.DISCOUNT` | 折扣标签 |
| `FROM_JF` | 5 | `TagType.POINTS` | 积分标签 |
| `FORM_FILL_AND_STROKE` | -1 | `TagType.FILL_AND_STROKE` | 填充+边框 |

## 🔧 迁移步骤

### 1. 替换初始化代码

```kotlin
// 原生
TagUtils.initNetLoader(context, new MyImageLoader(), true);

// KMP
AppTag.init(MyImageLoader(), debug = true)
```

### 2. 替换显示代码

```kotlin
// 原生
TagUtils.showRectStart(context, textView, tags, "商品名称");

// KMP
AppTag.ShowRectStart(tags, "商品名称")
```

### 3. 数据结构保持不变

```kotlin
// 原生和KMP都使用相同的GoodsTag结构
val tags = listOf(
    GoodsTag(form = 1, name = "新品", color = "#FFFFFF", bgcolor = "#FF0000")
)
```

## 🎉 完全兼容的特性

### ✅ 保持一致的特性

1. **全局配置** - 一次初始化，全局使用
2. **数据结构** - GoodsTag完全一致
3. **API命名** - 方法名保持一致
4. **标签类型** - 类型常量完全对应
5. **样式系统** - 默认样式自动设置
6. **图片加载** - 全局imageLoader自动使用
7. **调试模式** - 支持调试开关

### ✅ 增强的特性

1. **类型安全** - Kotlin的类型系统
2. **Compose集成** - 原生Compose支持
3. **跨平台** - 支持iOS和其他平台
4. **响应式** - Compose的响应式特性
5. **现代化** - 使用最新的UI框架

## 🚀 使用建议

### 推荐的使用方式

```kotlin
// 1. 在Application中初始化一次
AppTag.init(MyImageLoader())

// 2. 在Compose中直接使用，无需传递imageLoader
@Composable
fun ProductList() {
    LazyColumn {
        items(products) { product ->
            AppTag.ShowRectStart(
                tags = product.tags,
                content = product.name
            )
        }
    }
}
```

这样就完全对应了原生库的使用模式：**全局配置，局部使用**！🎯
