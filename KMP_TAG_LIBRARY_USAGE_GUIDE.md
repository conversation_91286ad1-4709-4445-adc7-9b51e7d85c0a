# 🏷️ KMP标签库使用指南

## 📋 目录

1. [快速开始](#快速开始)
2. [基础使用](#基础使用)
3. [标签类型](#标签类型)
4. [API参考](#api参考)
5. [高级功能](#高级功能)
6. [最佳实践](#最佳实践)
7. [常见问题](#常见问题)

## 🚀 快速开始

### 1. 初始化

在应用启动时初始化标签库：

```kotlin
// 在Application或主Activity中
AppTag.init(
    loader = MyImageLoader(), // 可选：自定义图片加载器
    debug = true              // 可选：开启调试模式
)
```

### 2. 基础使用

```kotlin
@Composable
fun ProductCard() {
    val tags = listOf(
        TagBean(type = TagType.FILL, text = "新品", backgroundColor = Color.Red, textColor = Color.White),
        TagBean(type = TagType.STROKE, text = "包邮", borderColor = Color.Blue, textColor = Color.Blue)
    )

    // 方式1: 使用AppTag.showTag（推荐）
    AppTag.showTag(
        tags = tags,
        text = "商品名称",
        onTagClick = { tag ->
            println("点击了标签: ${tag.text}")
        }
    )

    // 方式2: 使用TagGroup
    TagGroup(
        tags = tags,
        text = "商品名称",
        onTagClick = { tag ->
            println("点击了标签: ${tag.text}")
        }
    )
}
```

## 📊 基础使用

### TagGroup - 核心组件

`TagGroup` 是标签库的核心组件，支持多个标签的显示和布局：

```kotlin
@Composable
fun TagGroup(
    tags: List<TagBean>,                    // 标签列表
    text: String = "",                      // 显示文字
    textStyle: TextStyle = MaterialTheme.typography.bodyMedium,
    showTagsAtStart: Boolean = true,        // 标签是否在文字前面
    onTagClick: ((TagBean) -> Unit)? = null, // 点击回调
    arrowIcon: ImageVector = Icons.Default.KeyboardArrowRight,
    imagePainters: Map<String, Painter> = emptyMap(),
    loadingContent: @Composable (() -> Unit)? = null,
    errorContent: @Composable (() -> Unit)? = null,
    forceTagHeight: Boolean = false,        // 是否强制标签高度
    maxLines: Int = Int.MAX_VALUE,          // 文字最大行数
    overflow: TextOverflow = TextOverflow.Clip,
    modifier: Modifier = Modifier
)
```

### TagBean - 标签数据

```kotlin
data class TagBean(
    val type: TagType = TagType.FILL,              // 标签类型
    val text: String = "",                         // 标签文字
    val textColor: Color = Color.Black,            // 文字颜色
    val backgroundColor: Color = Color.White,      // 背景颜色
    val backgroundEndColor: Color? = null,         // 渐变结束颜色
    val borderColor: Color = Color.Black,          // 边框颜色
    val imageUrl: String? = null,                  // 图片URL
    val tagIndex: Int = 0,                         // 标签索引
    val tagCount: Int = 1,                         // 标签总数
    val showAtStart: Boolean = true,               // 显示位置
    val useFixedHeight: Boolean = false,           // 是否使用固定高度
    val isClickable: Boolean = false,              // 是否可点击
    val clickToast: String? = null,                // 点击提示
    val appearance: TagAppearance = TagAppearance.Default // 样式配置
)
```

## 🎯 标签类型

### 1. 填充标签 (FILL)

```kotlin
TagBean(
    type = TagType.FILL,
    text = "新品",
    backgroundColor = Color.Red,
    textColor = Color.White
)
```

### 2. 描边标签 (STROKE)

```kotlin
TagBean(
    type = TagType.STROKE,
    text = "包邮",
    borderColor = Color.Blue,
    textColor = Color.Blue
)
```

### 3. 填充+描边标签 (FILL_AND_STROKE)

```kotlin
TagBean(
    type = TagType.FILL_AND_STROKE,
    text = "限时",
    backgroundColor = Color.Yellow,
    borderColor = Color.Orange,
    textColor = Color.Black
)
```

### 4. 折扣标签 (DISCOUNT)

```kotlin
TagBean(
    type = TagType.DISCOUNT,
    text = "8折",
    backgroundColor = Color.Red,
    textColor = Color.White
)
```

### 5. 积分标签 (POINTS)

```kotlin
TagBean(
    type = TagType.POINTS,
    text = "100积分",
    imageUrl = "points_icon",
    backgroundColor = Color.Green,
    textColor = Color.White
)
```

### 6. 图片标签 (IMAGE)

```kotlin
TagBean(
    type = TagType.IMAGE,
    imageUrl = "tag_image_url",
    text = "图片标签"
)
```

## 📚 API参考

### AppTag 核心方法

#### AppTag.showTag - 主要方法

```kotlin
@Composable
fun AppTag.showTag(
    tags: List<TagBean>,                    // 标签列表
    text: String = "",                      // 文字内容
    showTagsAtStart: Boolean = true,        // 标签是否显示在文字前面
    onTagClick: ((TagBean) -> Unit)? = null, // 标签点击回调
    textStyle: TextStyle = MaterialTheme.typography.bodyMedium, // 文字样式
    maxLines: Int = Int.MAX_VALUE,          // 文字最大行数
    overflow: TextOverflow = TextOverflow.Clip, // 文字溢出处理
    forceTagHeight: Boolean = false,        // 是否强制标签高度
    modifier: Modifier = Modifier           // 修饰符
)

// 使用示例
AppTag.showTag(
    tags = listOf(TagBean(type = TagType.FILL, text = "新品")),
    text = "商品名称",
    showTagsAtStart = true,
    maxLines = 1,
    overflow = TextOverflow.Ellipsis
)
```

#### AppTag.ShowTags - 原生数据格式

```kotlin
@Composable
fun AppTag.ShowTags(
    tags: List<GoodsTag?>?,                 // GoodsTag列表（原生格式）
    content: String = "",                   // 文字内容
    showTagsAtStart: Boolean = true,        // 标签是否显示在文字前面
    onTagClick: ((TagBean) -> Unit)? = null // 标签点击回调
)

// 使用示例
val goodsTags = listOf(
    GoodsTag(form = 1, name = "新品", color = "#FFFFFF", bgcolor = "#FF0000"),
    GoodsTag(form = 3, name = "包邮", color = "#4CAF50", bordercolor = "#4CAF50")
)

AppTag.ShowTags(
    tags = goodsTags,
    content = "商品名称",
    showTagsAtStart = true,
    onTagClick = { tag -> println("点击: ${tag.text}") }
)
```

### 便捷方法

#### ShowRectStart - 矩形标签在前

```kotlin
@Composable
fun ShowRectStart(
    tags: List<TagBean>,
    content: String,
    onTagClick: ((TagBean) -> Unit)? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip
)

// 使用示例
ShowRectStart(
    tags = listOf(TagBean(type = TagType.FILL, text = "新品")),
    content = "商品名称"
)
```

#### ShowRectEnd - 矩形标签在后

```kotlin
ShowRectEnd(
    tags = listOf(TagBean(type = TagType.STROKE, text = "包邮")),
    content = "商品名称"
)
```

#### ShowRoundStart - 圆角标签在前

```kotlin
ShowRoundStart(
    tags = listOf(TagBean(type = TagType.FILL, text = "热销")),
    content = "商品名称"
)
```

#### ShowRoundEnd - 圆角标签在后

```kotlin
ShowRoundEnd(
    tags = listOf(TagBean(type = TagType.STROKE, text = "推荐")),
    content = "商品名称"
)
```

### 扩展函数

```kotlin
// List<TagBean>的扩展函数
val tags = listOf(TagBean(type = TagType.FILL, text = "新品"))

tags.showRectStart("商品名称")
tags.showRectEnd("商品名称")
tags.showRoundStart("商品名称")
tags.showRoundEnd("商品名称")
```

### TagBean扩展函数

```kotlin
val tag = TagBean(type = TagType.IMAGE, imageUrl = "image.png")

// 数据验证
if (tag.isValid()) {
    // 标签数据有效
}

// 获取显示文字
val displayText = tag.getDisplayText()

// 检查是否需要加载图片
if (tag.needsImageLoading()) {
    // 需要加载图片
}

// 获取唯一标识
val uniqueId = tag.getUniqueId()

// 获取对比色
val contrastColor = tag.getContrastTextColor()
```

## ⚙️ 高级功能

### 1. 自定义样式

```kotlin
val customAppearance = TagAppearance(
    tagHeight = 28.dp,           // 标签高度
    textSize = 12.sp,            // 文字大小
    cornerRadius = 8.dp,         // 圆角半径
    borderWidth = 1.dp,          // 边框宽度
    horizontalPadding = 12.dp,   // 水平内边距
    verticalPadding = 4.dp,      // 垂直内边距
    tagSpacing = 6.dp,           // 标签间距
    textSpacing = 8.dp           // 文字间距
)

val customTag = TagBean(
    type = TagType.FILL,
    text = "自定义样式",
    appearance = customAppearance
)
```

### 2. 图片加载器

```kotlin
class MyImageLoader : ComposeImageLoader {
    @Composable
    override fun loadImage(
        url: String, 
        placeholder: Painter?, 
        error: Painter?
    ): Painter? {
        // 使用Coil、Ktor等库加载图片
        return AsyncImage(
            model = url,
            contentDescription = null,
            placeholder = placeholder,
            error = error
        )
    }
}

// 初始化时设置
AppTag.init(loader = MyImageLoader())
```

### 3. 强制标签高度

```kotlin
TagGroup(
    tags = tags,
    text = "商品名称",
    forceTagHeight = true  // 强制使用标签的固定高度
)
```

### 4. 文字溢出处理

```kotlin
TagGroup(
    tags = tags,
    text = "这是一个很长很长的商品名称",
    maxLines = 1,
    overflow = TextOverflow.Ellipsis
)
```

### 5. 点击处理

```kotlin
TagGroup(
    tags = tags,
    text = "商品名称",
    onTagClick = { tag ->
        when (tag.type) {
            TagType.DISCOUNT -> {
                // 处理折扣标签点击
                showDiscountDialog()
            }
            TagType.POINTS -> {
                // 处理积分标签点击
                showPointsDialog()
            }
            else -> {
                // 其他标签点击
                println("点击了标签: ${tag.text}")
            }
        }
    }
)
```

## 🎯 最佳实践

### 1. 初始化

```kotlin
// 在Application中初始化
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        AppTag.init(
            loader = CoilImageLoader(), // 使用Coil图片加载库
            debug = BuildConfig.DEBUG   // 只在Debug模式开启调试
        )
    }
}
```

### 2. 标签数据管理

```kotlin
// 使用数据类管理标签配置
data class ProductTag(
    val id: String,
    val name: String,
    val type: TagType,
    val color: Color,
    val textColor: Color = Color.White
) {
    fun toTagBean(): TagBean = TagBean(
        type = type,
        text = name,
        backgroundColor = color,
        textColor = textColor,
        isClickable = true
    )
}

// 预定义常用标签
object CommonTags {
    val NEW_PRODUCT = ProductTag("new", "新品", TagType.FILL, Color.Red)
    val FREE_SHIPPING = ProductTag("free_ship", "包邮", TagType.STROKE, Color.Blue)
    val HOT_SALE = ProductTag("hot", "热销", TagType.FILL, Color.Orange)
}
```

### 3. 性能优化

```kotlin
@Composable
fun ProductList(products: List<Product>) {
    LazyColumn {
        items(products) { product ->
            // 使用remember缓存标签列表
            val tags = remember(product.id) {
                product.createTagList()
            }
            
            ProductItem(
                product = product,
                tags = tags
            )
        }
    }
}
```

### 4. 主题适配

```kotlin
@Composable
fun ThemedTagGroup(tags: List<TagBean>, text: String) {
    val isDarkTheme = isSystemInDarkTheme()
    
    val themedTags = remember(tags, isDarkTheme) {
        tags.map { tag ->
            tag.copy(
                textColor = if (isDarkTheme) Color.White else Color.Black,
                backgroundColor = if (isDarkTheme) {
                    tag.backgroundColor.copy(alpha = 0.8f)
                } else {
                    tag.backgroundColor
                }
            )
        }
    }
    
    TagGroup(
        tags = themedTags,
        text = text
    )
}
```

## ❓ 常见问题

### Q1: 如何自定义图片加载器？

A: 实现`ComposeImageLoader`接口：

```kotlin
class MyImageLoader : ComposeImageLoader {
    @Composable
    override fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter? {
        // 你的图片加载逻辑
        return loadImageFromUrl(url) ?: error
    }
}
```

### Q2: 标签点击没有响应？

A: 确保设置了`isClickable = true`和`onTagClick`回调：

```kotlin
TagBean(
    type = TagType.FILL,
    text = "可点击",
    isClickable = true  // 必须设置为true
)

TagGroup(
    tags = tags,
    onTagClick = { tag -> /* 处理点击 */ }  // 必须设置回调
)
```

### Q3: 如何实现标签的渐变背景？

A: 使用`backgroundEndColor`属性：

```kotlin
TagBean(
    type = TagType.FILL,
    text = "渐变标签",
    backgroundColor = Color.Blue,
    backgroundEndColor = Color.Purple  // 设置渐变结束颜色
)
```

### Q4: 标签高度不一致怎么办？

A: 使用`forceTagHeight`参数：

```kotlin
TagGroup(
    tags = tags,
    text = "商品名称",
    forceTagHeight = true  // 强制使用固定高度
)
```

### Q5: 如何调试标签显示问题？

A: 开启调试模式：

```kotlin
AppTag.init(debug = true)

// 查看缓存统计
val cacheStats = TagUtils.getTextCacheStats()
println("缓存统计: $cacheStats")
```

## 📱 平台特定注意事项

### Android
- 支持所有功能
- 建议使用Coil作为图片加载库

### iOS
- 支持所有功能
- 图片加载需要适配iOS的图片加载库

### Desktop
- 支持所有功能
- 图片加载可以使用Ktor或其他JVM图片库

## 🔗 相关链接

- [GitHub仓库](https://github.com/your-repo/kmp-tag-library)
- [API文档](https://your-docs-site.com/api)
- [示例项目](https://github.com/your-repo/kmp-tag-examples)

## 📄 许可证

MIT License - 详见LICENSE文件
