# Kamel图片加载库集成指南

## 🎯 概述

Kamel是专为Kotlin Multiplatform设计的图片加载库，支持网络图片、本地资源等多种图片来源。本指南展示如何将Kamel集成到标签库中。

## 📦 添加依赖

在您的`build.gradle.kts`中添加Kamel依赖：

```kotlin
dependencies {
    implementation("media.kamel:kamel-image:0.9.5")
}
```

## 🔧 实现方案

### 方案1：混合实现（推荐）

由于Kamel的`asyncPainterResource`需要在`@Composable`函数中调用，而我们的`TagImageLoader`接口是非Composable的，我们采用混合实现：

#### DemoImageLoader实现

```kotlin
/**
 * 使用Kamel库的图片加载器实现
 * 混合方案：本地图片使用ColorPainter模拟，网络图片在组件中使用Kamel处理
 */
class DemoImageLoader : TagImageLoader {
    override fun loadImage(url: String): Painter? {
        return loadImage(url, null, null)
    }
    
    override fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter? {
        println("🖼️ DemoImageLoader: Loading image from URL: $url")
        
        return when {
            // 网络图片 - 返回占位符，实际加载在Composable中处理
            url.startsWith("http://") || url.startsWith("https://") -> {
                println("🌐 DemoImageLoader: Network image detected: $url")
                placeholder ?: ColorPainter(Color.LightGray)
            }
            // 本地模拟图片
            url.contains("star") -> {
                println("⭐ DemoImageLoader: Star icon")
                ColorPainter(Color.Yellow)
            }
            url.contains("discount") -> {
                println("💰 DemoImageLoader: Discount icon")
                ColorPainter(Color.Red)
            }
            url.contains("points") -> {
                println("🎯 DemoImageLoader: Points icon")
                ColorPainter(Color.Green)
            }
            url.contains("icon") -> {
                println("🔵 DemoImageLoader: Generic icon")
                ColorPainter(Color.Blue)
            }
            else -> {
                println("⚪ DemoImageLoader: Unknown URL pattern: $url")
                error ?: placeholder ?: ColorPainter(Color.Gray)
            }
        }
    }
}
```

#### Kamel网络图片组件

```kotlin
/**
 * 使用Kamel加载网络图片的组件
 */
@Composable
fun KamelNetworkImage(
    url: String,
    modifier: Modifier = Modifier
) {
    val painterResource = asyncPainterResource(url)
    
    when (val resource = painterResource) {
        is Resource.Loading -> {
            Box(
                modifier = modifier,
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    strokeWidth = 2.dp
                )
            }
        }
        is Resource.Success -> {
            Image(
                painter = resource.value,
                contentDescription = "Network image",
                modifier = modifier
            )
        }
        is Resource.Failure -> {
            Box(
                modifier = modifier.background(Color.LightGray),
                contentAlignment = Alignment.Center
            ) {
                Text(text = "❌", style = MaterialTheme.typography.bodySmall)
            }
        }
    }
}
```

## 🧪 使用示例

### 1. 基本使用

```kotlin
@Composable
fun TagWithKamelDemo() {
    // 初始化标签库
    LaunchedEffect(Unit) {
        AppTag.init(
            loader = DemoImageLoader(),
            debug = true
        )
    }
    
    // 使用本地模拟图片的标签
    val localTags = listOf(
        TagBean(
            type = TagType.POINTS,
            text = "积分图标",
            imageUrl = "points_icon",
            backgroundColor = Color(0xFFE8F5E8),
            textColor = Color(0xFF2E7D32)
        )
    )
    
    TagGroup(
        tags = localTags,
        text = "本地图片标签"
    )
}
```

### 2. 网络图片演示

```kotlin
@Composable
fun NetworkImageDemo() {
    val networkImageUrls = listOf(
        "https://picsum.photos/100/100?random=1",
        "https://picsum.photos/100/100?random=2"
    )
    
    // 直接使用Kamel加载网络图片
    Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
        networkImageUrls.forEach { url ->
            KamelNetworkImage(
                url = url,
                modifier = Modifier.size(60.dp)
            )
        }
    }
    
    // 结合标签库使用（当前会显示占位符）
    val networkTags = listOf(
        TagBean(
            type = TagType.IMAGE,
            imageUrl = networkImageUrls[0],
            text = "网络图片"
        )
    )
    
    TagGroup(
        tags = networkTags,
        text = "网络图片标签"
    )
}
```

## 🔄 方案2：完全Kamel实现（高级）

如果您需要完全使用Kamel处理所有图片，可以修改标签库的架构：

### 修改TagImageLoader接口

```kotlin
interface TagImageLoader {
    @Composable
    fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter?
}
```

### 完全Kamel实现

```kotlin
class KamelImageLoader : TagImageLoader {
    @Composable
    override fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter? {
        return when {
            url.startsWith("http://") || url.startsWith("https://") -> {
                val painterResource = asyncPainterResource(url)
                when (val resource = painterResource) {
                    is Resource.Loading -> placeholder
                    is Resource.Success -> resource.value
                    is Resource.Failure -> error ?: placeholder
                }
            }
            // 本地资源
            else -> {
                try {
                    asyncPainterResource("drawable/$url.png").let { resource ->
                        when (resource) {
                            is Resource.Success -> resource.value
                            else -> error ?: placeholder
                        }
                    }
                } catch (e: Exception) {
                    error ?: placeholder
                }
            }
        }
    }
}
```

## 📊 方案对比

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| **混合实现** | 简单、稳定、向后兼容 | 网络图片需要额外处理 | 大多数项目 |
| **完全Kamel** | 统一处理、功能完整 | 需要修改接口、复杂度高 | 高级定制需求 |

## 🎯 最佳实践

### 1. 图片URL规范

```kotlin
// 网络图片
"https://example.com/image.jpg"

// 本地资源
"drawable/icon_star.png"

// 模拟图片（开发阶段）
"points_icon"  // 会被DemoImageLoader处理
```

### 2. 错误处理

```kotlin
class RobustImageLoader : TagImageLoader {
    override fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter? {
        return try {
            when {
                url.isBlank() -> error ?: placeholder
                url.startsWith("http") -> loadNetworkImage(url, placeholder, error)
                else -> loadLocalImage(url, placeholder, error)
            }
        } catch (e: Exception) {
            println("⚠️ ImageLoader: Error loading $url: ${e.message}")
            error ?: placeholder ?: ColorPainter(Color.Gray)
        }
    }
}
```

### 3. 性能优化

```kotlin
// 使用缓存
val imageCache = mutableMapOf<String, Painter>()

override fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter? {
    // 检查缓存
    imageCache[url]?.let { return it }
    
    // 加载图片
    val painter = actualLoadImage(url, placeholder, error)
    
    // 缓存结果
    painter?.let { imageCache[url] = it }
    
    return painter
}
```

## 🚀 扩展功能

### 1. 图片预处理

```kotlin
@Composable
fun ProcessedKamelImage(
    url: String,
    modifier: Modifier = Modifier,
    contentScale: ContentScale = ContentScale.Crop
) {
    val painterResource = asyncPainterResource(url)
    
    when (val resource = painterResource) {
        is Resource.Success -> {
            Image(
                painter = resource.value,
                contentDescription = null,
                modifier = modifier.clip(CircleShape), // 圆形裁剪
                contentScale = contentScale
            )
        }
        // ... 其他状态处理
    }
}
```

### 2. 自定义加载状态

```kotlin
@Composable
fun CustomLoadingKamelImage(url: String) {
    val painterResource = asyncPainterResource(url)
    
    when (val resource = painterResource) {
        is Resource.Loading -> {
            Box(contentAlignment = Alignment.Center) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    CircularProgressIndicator()
                    Text("加载中...", style = MaterialTheme.typography.bodySmall)
                }
            }
        }
        // ... 其他状态
    }
}
```

## 🎉 总结

### 当前实现的优势

1. **简单易用** - 保持了原有的接口设计
2. **向后兼容** - 不破坏现有代码
3. **灵活扩展** - 可以轻松添加新的图片来源
4. **调试友好** - 提供详细的日志输出

### 使用建议

1. **开发阶段** - 使用模拟图片（当前实现）
2. **生产环境** - 根据需要选择混合实现或完全Kamel实现
3. **性能要求高** - 考虑添加图片缓存机制
4. **多平台项目** - Kamel是最佳选择，支持所有KMP平台

现在您可以在标签库中使用Kamel加载真实的网络图片了！🎯
