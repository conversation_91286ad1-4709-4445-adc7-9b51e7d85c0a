# 标签文字显示问题排查指南

## 🚨 问题现象

运行Demo后发现标签上的文字都不见了。

## 🔍 问题分析

### 可能的原因

1. **Layout计算错误**: 自定义Layout中的位置计算有误
2. **字体度量计算错误**: FontMetrics计算导致文字位置异常
3. **容器尺寸问题**: 容器高度或宽度计算错误
4. **Y坐标越界**: 文字被放置在可见区域之外

### 之前的复杂实现问题

```kotlin
// 问题代码 - 过于复杂的基线调整
val textVisualCenter = (tagFontMetrics.descent - tagFontMetrics.ascent) / 2f - tagFontMetrics.descent
val textTopY = containerCenterY - textVisualCenter - tagFontMetrics.ascent
```

这种复杂的计算容易出错，导致文字位置异常。

## ✅ 解决方案

### 方案1: 简化实现（已采用）

使用最简单的Box + Alignment.Center实现：

```kotlin
@Composable
fun VerticalCenterText(
    text: String,
    fontSize: TextUnit,
    color: Color = Color.Black,
    fontWeight: FontWeight? = null,
    containerHeight: Dp? = null,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.then(
            if (containerHeight != null) {
                Modifier.height(containerHeight)
            } else {
                Modifier.wrapContentHeight()
            }
        ),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            fontSize = fontSize,
            color = color,
            fontWeight = fontWeight,
            maxLines = 1,
            textAlign = TextAlign.Center
        )
    }
}
```

**优点**：
- ✅ 简单可靠，不会出错
- ✅ 文字一定能显示
- ✅ 基本的垂直居中效果

**缺点**：
- ❌ 可能无法完美解决下沉字符截断问题
- ❌ 不如原生精确

### 方案2: 渐进式优化

在确保文字能显示的基础上，逐步添加基线调整：

```kotlin
@Composable
fun ImprovedVerticalCenterText(
    text: String,
    fontSize: TextUnit,
    color: Color = Color.Black,
    fontWeight: FontWeight? = null,
    containerHeight: Dp? = null,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    
    Box(
        modifier = modifier.then(
            if (containerHeight != null) {
                Modifier.height(containerHeight)
            } else {
                Modifier.wrapContentHeight()
            }
        ),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            fontSize = fontSize,
            color = color,
            fontWeight = fontWeight,
            maxLines = 1,
            textAlign = TextAlign.Center,
            modifier = Modifier.offset(
                y = with(density) {
                    // 轻微向上偏移，补偿下沉字符
                    val fontSizePx = fontSize.toPx()
                    val offsetPx = fontSizePx * 0.1f // 字体大小的10%
                    (-offsetPx).toDp()
                }
            )
        )
    }
}
```

### 方案3: 回退到普通Text

如果VerticalCenterText仍有问题，可以直接在标签组件中使用普通Text：

```kotlin
// 在FillTag.kt等文件中
Text(
    text = tagBean.text,
    color = tagBean.textColor,
    fontSize = tagBean.getOptimizedTextSize(),
    fontWeight = appearance.fontWeight,
    textAlign = TextAlign.Center,
    maxLines = 1,
    modifier = Modifier.padding(vertical = 2.dp) // 添加一点垂直padding
)
```

## 🧪 测试验证

### 1. 快速测试

运行`QuickTextTest.kt`，检查：
- ✅ 基础标签是否显示文字
- ✅ 不同类型标签是否都正常
- ✅ 下沉字符是否可见

### 2. 逐步排查

如果文字仍不显示：

1. **检查标签背景**: 确认标签容器是否可见
2. **检查文字颜色**: 确认文字颜色与背景有对比度
3. **检查字体大小**: 确认字体大小不为0
4. **检查容器尺寸**: 确认容器有足够的宽高

### 3. Debug信息

在VerticalCenterText中添加调试信息：

```kotlin
@Composable
fun DebugVerticalCenterText(
    text: String,
    fontSize: TextUnit,
    // ... 其他参数
) {
    val density = LocalDensity.current
    
    // 打印调试信息
    LaunchedEffect(text, fontSize) {
        println("DebugVerticalCenterText: text='$text', fontSize=$fontSize")
        println("fontSizePx=${with(density) { fontSize.toPx() }}")
    }
    
    Box(
        modifier = modifier
            .background(Color.Red.copy(alpha = 0.3f)) // 临时背景，便于调试
            .then(
                if (containerHeight != null) {
                    Modifier.height(containerHeight)
                } else {
                    Modifier.wrapContentHeight()
                }
            ),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            fontSize = fontSize,
            color = color,
            fontWeight = fontWeight,
            maxLines = 1,
            textAlign = TextAlign.Center
        )
    }
}
```

## 📋 检查清单

运行Demo时，请检查：

- [ ] 标签容器是否可见（有背景色或边框）
- [ ] 标签尺寸是否正常（不是0x0）
- [ ] 文字是否在标签内部（不是在外面）
- [ ] 文字颜色是否与背景有对比度
- [ ] 控制台是否有错误信息

## 🎯 当前状态

已采用最简单的Box + Alignment.Center实现，确保文字能正常显示。

### 预期效果

- ✅ 所有标签都应该显示文字
- ✅ 文字应该在标签中心位置
- ⚠️ 下沉字符可能仍有轻微截断（需要后续优化）

### 下一步计划

1. **确认文字显示**: 先确保所有文字都能正常显示
2. **测试下沉字符**: 检查g,j,p,q,y等字符是否被截断
3. **渐进式优化**: 在不影响显示的前提下，逐步改进垂直居中效果

## 💡 重要提醒

**优先级**: 文字显示 > 完美居中

先确保文字能正常显示，再考虑完美的垂直居中效果。一个能显示文字但居中稍有偏差的标签，比一个完全看不到文字的标签要好得多。
