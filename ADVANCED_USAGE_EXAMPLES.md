# 🚀 KMP标签库高级用法示例

## 📋 目录

1. [动态标签生成](#动态标签生成)
2. [标签状态管理](#标签状态管理)
3. [动画效果](#动画效果)
4. [性能优化](#性能优化)
5. [自定义组件](#自定义组件)
6. [主题适配](#主题适配)
7. [调试和监控](#调试和监控)

## 🎯 动态标签生成

### 1. 基于数据动态生成标签

```kotlin
@Composable
fun DynamicProductTags(product: Product) {
    val tags = remember(product) {
        buildList {
            // 新品标签
            if (product.isNew) {
                add(TagBean(
                    type = TagType.FILL,
                    text = "新品",
                    backgroundColor = Color.Red,
                    textColor = Color.White
                ))
            }
            
            // 折扣标签
            if (product.discountPercent > 0) {
                add(TagBean(
                    type = TagType.DISCOUNT,
                    text = "${product.discountPercent}折",
                    backgroundColor = Color.Orange,
                    textColor = Color.White
                ))
            }
            
            // 包邮标签
            if (product.freeShipping) {
                add(TagBean(
                    type = TagType.STROKE,
                    text = "包邮",
                    borderColor = Color.Green,
                    textColor = Color.Green
                ))
            }
            
            // 积分标签
            if (product.points > 0) {
                add(TagBean(
                    type = TagType.POINTS,
                    text = "${product.points}积分",
                    imageUrl = "points_icon",
                    backgroundColor = Color.Blue,
                    textColor = Color.White
                ))
            }
        }
    }
    
    TagGroup(
        tags = tags,
        text = product.name,
        onTagClick = { tag ->
            when (tag.type) {
                TagType.DISCOUNT -> showDiscountDetails(product)
                TagType.POINTS -> showPointsInfo(product)
                else -> showTagInfo(tag)
            }
        }
    )
}
```

### 2. 条件标签显示

```kotlin
@Composable
fun ConditionalTags(
    showNewTag: Boolean,
    showHotTag: Boolean,
    showDiscountTag: Boolean
) {
    val tags = remember(showNewTag, showHotTag, showDiscountTag) {
        buildList {
            if (showNewTag) {
                add(TagBean(type = TagType.FILL, text = "新品", backgroundColor = Color.Red))
            }
            if (showHotTag) {
                add(TagBean(type = TagType.STROKE, text = "热销", borderColor = Color.Blue))
            }
            if (showDiscountTag) {
                add(TagBean(type = TagType.DISCOUNT, text = "特价", backgroundColor = Color.Orange))
            }
        }
    }
    
    if (tags.isNotEmpty()) {
        TagGroup(tags = tags, text = "商品名称")
    } else {
        Text("商品名称") // 无标签时只显示文字
    }
}
```

## 🎮 标签状态管理

### 1. 可选择标签

```kotlin
@Composable
fun SelectableTagDemo() {
    var selectedTags by remember { mutableStateOf(setOf<String>()) }
    
    val allTags = listOf(
        TagBean(type = TagType.FILL, text = "新品"),
        TagBean(type = TagType.STROKE, text = "热销"),
        TagBean(type = TagType.FILL_AND_STROKE, text = "推荐"),
        TagBean(type = TagType.DISCOUNT, text = "特价")
    )
    
    val styledTags = allTags.map { tag ->
        val isSelected = selectedTags.contains(tag.text)
        tag.copy(
            backgroundColor = if (isSelected) Color.Blue else tag.backgroundColor,
            textColor = if (isSelected) Color.White else tag.textColor,
            borderColor = if (isSelected) Color.Blue else tag.borderColor,
            isClickable = true
        )
    }
    
    Column {
        Text("选择标签 (已选: ${selectedTags.size})")
        
        TagGroup(
            tags = styledTags,
            text = "可选择的标签",
            onTagClick = { tag ->
                selectedTags = if (selectedTags.contains(tag.text)) {
                    selectedTags - tag.text
                } else {
                    selectedTags + tag.text
                }
            }
        )
        
        if (selectedTags.isNotEmpty()) {
            Text("已选择: ${selectedTags.joinToString(", ")}")
        }
    }
}
```

### 2. 标签计数器

```kotlin
@Composable
fun TagCounterDemo() {
    var clickCounts by remember { mutableStateOf(mapOf<String, Int>()) }
    
    val tags = listOf(
        TagBean(type = TagType.FILL, text = "点击我", isClickable = true),
        TagBean(type = TagType.STROKE, text = "我也是", isClickable = true)
    )
    
    val styledTags = tags.map { tag ->
        val count = clickCounts[tag.text] ?: 0
        tag.copy(
            text = "${tag.text} ($count)",
            backgroundColor = when {
                count == 0 -> tag.backgroundColor
                count < 5 -> Color.Yellow
                else -> Color.Green
            }
        )
    }
    
    TagGroup(
        tags = styledTags,
        text = "点击计数器",
        onTagClick = { tag ->
            val originalText = tag.text.substringBefore(" (")
            val currentCount = clickCounts[originalText] ?: 0
            clickCounts = clickCounts + (originalText to currentCount + 1)
        }
    )
}
```

## 🎨 动画效果

### 1. 标签淡入淡出

```kotlin
@Composable
fun FadeInOutTagDemo() {
    var isVisible by remember { mutableStateOf(true) }
    
    val animatedAlpha by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0f,
        animationSpec = tween(durationMillis = 500)
    )
    
    Column {
        Button(onClick = { isVisible = !isVisible }) {
            Text(if (isVisible) "隐藏标签" else "显示标签")
        }
        
        Box(modifier = Modifier.alpha(animatedAlpha)) {
            TagGroup(
                tags = listOf(
                    TagBean(type = TagType.FILL, text = "动画标签", backgroundColor = Color.Blue)
                ),
                text = "带动画效果的标签"
            )
        }
    }
}
```

### 2. 标签滑动进入

```kotlin
@Composable
fun SlideInTagDemo() {
    var isVisible by remember { mutableStateOf(false) }
    
    val animatedOffset by animateIntOffsetAsState(
        targetValue = if (isVisible) IntOffset.Zero else IntOffset(-300, 0),
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy)
    )
    
    LaunchedEffect(Unit) {
        delay(500)
        isVisible = true
    }
    
    Box(modifier = Modifier.offset { animatedOffset }) {
        TagGroup(
            tags = listOf(
                TagBean(type = TagType.FILL, text = "滑入", backgroundColor = Color.Green)
            ),
            text = "滑动进入的标签"
        )
    }
}
```

### 3. 标签颜色渐变动画

```kotlin
@Composable
fun ColorAnimationTagDemo() {
    val infiniteTransition = rememberInfiniteTransition()
    
    val animatedColor by infiniteTransition.animateColor(
        initialValue = Color.Red,
        targetValue = Color.Blue,
        animationSpec = infiniteRepeatable(
            animation = tween(2000),
            repeatMode = RepeatMode.Reverse
        )
    )
    
    TagGroup(
        tags = listOf(
            TagBean(
                type = TagType.FILL,
                text = "彩虹标签",
                backgroundColor = animatedColor,
                textColor = Color.White
            )
        ),
        text = "颜色动画标签"
    )
}
```

## ⚡ 性能优化

### 1. 标签列表虚拟化

```kotlin
@Composable
fun VirtualizedTagList(products: List<Product>) {
    LazyColumn {
        items(
            items = products,
            key = { it.id } // 重要：提供稳定的key
        ) { product ->
            // 使用remember缓存标签计算
            val tags = remember(product.id, product.lastModified) {
                product.generateTags()
            }
            
            ProductTagItem(
                product = product,
                tags = tags
            )
        }
    }
}

@Composable
private fun ProductTagItem(
    product: Product,
    tags: List<TagBean>
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp)
    ) {
        TagGroup(
            tags = tags,
            text = product.name,
            modifier = Modifier.padding(16.dp)
        )
    }
}
```

### 2. 标签预计算

```kotlin
class TagPreprocessor {
    private val tagCache = mutableMapOf<String, List<TagBean>>()
    
    fun precomputeTags(products: List<Product>) {
        products.forEach { product ->
            val cacheKey = "${product.id}_${product.lastModified}"
            if (!tagCache.containsKey(cacheKey)) {
                tagCache[cacheKey] = product.generateTags()
            }
        }
    }
    
    fun getTags(product: Product): List<TagBean> {
        val cacheKey = "${product.id}_${product.lastModified}"
        return tagCache[cacheKey] ?: product.generateTags()
    }
}

@Composable
fun OptimizedProductList(products: List<Product>) {
    val tagPreprocessor = remember { TagPreprocessor() }
    
    LaunchedEffect(products) {
        tagPreprocessor.precomputeTags(products)
    }
    
    LazyColumn {
        items(products, key = { it.id }) { product ->
            val tags = remember(product.id, product.lastModified) {
                tagPreprocessor.getTags(product)
            }
            
            ProductTagItem(product = product, tags = tags)
        }
    }
}
```

### 3. 图片预加载

```kotlin
@Composable
fun PreloadedImageTags() {
    val commonIcons = listOf("star", "discount", "points", "new", "hot")
    
    // 预加载常用图标
    LaunchedEffect(Unit) {
        IconCache.preloadIconsAsync(
            iconKeys = commonIcons,
            imageLoader = ImageLoaderManager.getComposeImageLoader()
        )
    }
    
    val tags = listOf(
        TagBean(type = TagType.POINTS, text = "积分", imageUrl = "points"),
        TagBean(type = TagType.IMAGE, imageUrl = "star"),
        TagBean(type = TagType.DISCOUNT, text = "折扣", imageUrl = "discount")
    )
    
    TagGroup(tags = tags, text = "预加载图标的标签")
}
```

## 🎨 自定义组件

### 1. 自定义标签样式

```kotlin
@Composable
fun CustomStyledTag(
    text: String,
    style: TagStyle = TagStyle.Default
) {
    val appearance = when (style) {
        TagStyle.Small -> TagAppearance(
            tagHeight = 18.dp,
            textSize = 10.sp,
            cornerRadius = 4.dp,
            horizontalPadding = 8.dp
        )
        TagStyle.Large -> TagAppearance(
            tagHeight = 32.dp,
            textSize = 14.sp,
            cornerRadius = 8.dp,
            horizontalPadding = 16.dp,
            elevation = 2.dp
        )
        TagStyle.Pill -> TagAppearance(
            tagHeight = 24.dp,
            textSize = 12.sp,
            cornerRadius = 12.dp,
            horizontalPadding = 12.dp,
            shape = RoundedCornerShape(50)
        )
        else -> TagAppearance.Default
    }
    
    TagGroup(
        tags = listOf(
            TagBean(
                type = TagType.FILL,
                text = text,
                appearance = appearance,
                backgroundColor = Color.Blue,
                textColor = Color.White
            )
        ),
        text = ""
    )
}

enum class TagStyle {
    Default, Small, Large, Pill
}
```

### 2. 标签组合器

```kotlin
@Composable
fun TagComposer(
    modifier: Modifier = Modifier,
    content: TagComposerScope.() -> Unit
) {
    val scope = remember { TagComposerScope() }
    scope.content()
    
    TagGroup(
        tags = scope.tags,
        text = scope.text,
        modifier = modifier
    )
}

class TagComposerScope {
    internal val tags = mutableListOf<TagBean>()
    internal var text: String = ""
    
    fun text(value: String) {
        text = value
    }
    
    fun fillTag(text: String, color: Color = Color.Blue) {
        tags.add(TagBean(
            type = TagType.FILL,
            text = text,
            backgroundColor = color,
            textColor = Color.White
        ))
    }
    
    fun strokeTag(text: String, color: Color = Color.Blue) {
        tags.add(TagBean(
            type = TagType.STROKE,
            text = text,
            borderColor = color,
            textColor = color
        ))
    }
    
    fun discountTag(percent: Int) {
        tags.add(TagBean(
            type = TagType.DISCOUNT,
            text = "${percent}折",
            backgroundColor = Color.Red,
            textColor = Color.White
        ))
    }
}

// 使用示例
@Composable
fun TagComposerExample() {
    TagComposer {
        text("商品名称")
        fillTag("新品", Color.Red)
        strokeTag("包邮", Color.Green)
        discountTag(8)
    }
}
```

### 3. 响应式标签布局

```kotlin
@Composable
fun ResponsiveTagLayout(
    tags: List<TagBean>,
    text: String,
    maxWidth: Dp
) {
    BoxWithConstraints {
        val availableWidth = maxWidth.coerceAtMost(maxWidth)
        
        if (availableWidth < 300.dp) {
            // 小屏幕：垂直布局
            Column {
                Text(text)
                Spacer(modifier = Modifier.height(4.dp))
                FlowRow {
                    tags.forEach { tag ->
                        SingleTag(tag)
                        Spacer(modifier = Modifier.width(4.dp))
                    }
                }
            }
        } else {
            // 大屏幕：水平布局
            TagGroup(
                tags = tags,
                text = text,
                showTagsAtStart = true
            )
        }
    }
}

@Composable
private fun FlowRow(
    content: @Composable () -> Unit
) {
    // 简化的流式布局实现
    // 实际项目中建议使用accompanist-flowlayout
    Row {
        content()
    }
}
```

## 🌙 主题适配

### 1. 深色主题适配

```kotlin
@Composable
fun ThemedTagGroup(
    tags: List<TagBean>,
    text: String
) {
    val isDarkTheme = isSystemInDarkTheme()
    val colorScheme = MaterialTheme.colorScheme
    
    val themedTags = remember(tags, isDarkTheme) {
        tags.map { tag ->
            tag.copy(
                backgroundColor = if (isDarkTheme) {
                    tag.backgroundColor.copy(alpha = 0.8f)
                } else {
                    tag.backgroundColor
                },
                textColor = if (isDarkTheme) {
                    colorScheme.onSurface
                } else {
                    tag.textColor
                },
                borderColor = if (isDarkTheme) {
                    tag.borderColor.copy(alpha = 0.8f)
                } else {
                    tag.borderColor
                }
            )
        }
    }
    
    TagGroup(
        tags = themedTags,
        text = text,
        textStyle = MaterialTheme.typography.bodyMedium.copy(
            color = colorScheme.onSurface
        )
    )
}
```

### 2. 动态主题色

```kotlin
@Composable
fun DynamicThemedTags(
    tags: List<TagBean>,
    text: String
) {
    val colorScheme = MaterialTheme.colorScheme
    
    val themedTags = remember(tags, colorScheme) {
        tags.mapIndexed { index, tag ->
            val themeColor = when (index % 3) {
                0 -> colorScheme.primary
                1 -> colorScheme.secondary
                else -> colorScheme.tertiary
            }
            
            tag.copy(
                backgroundColor = themeColor,
                textColor = when (index % 3) {
                    0 -> colorScheme.onPrimary
                    1 -> colorScheme.onSecondary
                    else -> colorScheme.onTertiary
                }
            )
        }
    }
    
    TagGroup(tags = themedTags, text = text)
}
```

## 🔍 调试和监控

### 1. 性能监控

```kotlin
@Composable
fun PerformanceMonitor() {
    var renderCount by remember { mutableStateOf(0) }
    var lastRenderTime by remember { mutableStateOf(0L) }
    
    LaunchedEffect(Unit) {
        while (true) {
            renderCount++
            lastRenderTime = System.currentTimeMillis()
            delay(16) // 60 FPS
        }
    }
    
    Column {
        Text("渲染次数: $renderCount")
        Text("最后渲染: ${Date(lastRenderTime)}")
        
        val cacheStats = TagUtils.getTextCacheStats()
        Text("缓存大小: ${cacheStats["cacheSize"]}")
        Text("缓存使用率: ${cacheStats["usageRatio"]}")
    }
}
```

### 2. 标签调试信息

```kotlin
@Composable
fun DebugTagInfo(tag: TagBean) {
    if (TagUtils.isDebugMode) {
        Column(
            modifier = Modifier
                .background(Color.Yellow.copy(alpha = 0.3f))
                .padding(4.dp)
        ) {
            Text("类型: ${tag.type}", fontSize = 10.sp)
            Text("文字: ${tag.text}", fontSize = 10.sp)
            Text("有效: ${tag.isValid()}", fontSize = 10.sp)
            Text("ID: ${tag.getUniqueId()}", fontSize = 10.sp)
        }
    }
}
```

### 3. 内存使用监控

```kotlin
@Composable
fun MemoryMonitor() {
    var memoryInfo by remember { mutableStateOf("") }
    
    LaunchedEffect(Unit) {
        while (true) {
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            
            memoryInfo = "内存使用: ${usedMemory / 1024 / 1024}MB / ${maxMemory / 1024 / 1024}MB"
            
            delay(1000)
        }
    }
    
    Text(memoryInfo, fontSize = 12.sp, color = Color.Gray)
}
```

这些高级用法示例展示了KMP标签库的强大功能和灵活性，可以满足各种复杂的业务需求。
