# Android原生 vs Compose 基线对齐深度分析

## 🔍 问题根源分析

经过仔细分析Android原生代码，发现文字截断问题的真正原因是**基线计算算法的差异**。

### Android原生的关键算法

#### 1. adjustBaseLine方法 (TagUtils.java:361-365)

```java
static float adjustBaseLine(int y, Paint.FontMetrics rawFm, Paint.FontMetrics tagFm) {
    float offset1 = (rawFm.descent - rawFm.ascent) / 2F - rawFm.descent;
    float offset2 = -(tagFm.ascent + tagFm.descent) / 2F;
    return y - offset1 + offset2;
}
```

**算法解析**：
- `rawFm`: 外部TextView的字体度量
- `tagFm`: 标签内文字的字体度量
- `offset1`: 外部文字的视觉中心相对于基线的偏移
- `offset2`: 标签文字的视觉中心相对于基线的偏移
- 最终返回调整后的基线位置

#### 2. 文字绘制 (FillBgSpan.java:163-166)

```java
canvas.drawText(text, start, end,
    (2F * frameLeft + frameWidth - arrowWidth - arrowMargin) / 2F,  // x坐标
    TagUtils.adjustBaseLine(y, rawFM, tagPaint.getFontMetrics()),   // y坐标(关键!)
    tagPaint);
```

#### 3. 高度计算 (FillBgSpan.java:96-97)

```java
Paint.FontMetrics tagFM = tagPaint.getFontMetrics();
frameHeight = tagFM.descent - tagFM.ascent + 2 * paddingV;
```

### Compose版本的问题

当前的`VerticalCenterText`实现过于简化：

```kotlin
// 当前实现 - 问题代码
val yOffset = (height - textPlaceable.height) / 2
textPlaceable.placeRelative(0, yOffset)
```

**问题**：
1. 没有考虑字体的ascent/descent
2. 没有模拟原生的基线调整算法
3. 简单的几何居中不等于视觉居中

## 🔧 正确的解决方案

需要在Compose中精确模拟Android原生的基线调整算法。

### 关键技术点

1. **获取字体度量**: 需要计算ascent、descent、leading等
2. **基线调整**: 实现adjustBaseLine的等价算法
3. **视觉居中**: 基于字体度量而非几何尺寸

### FontMetrics对应关系

| Android原生 | Compose等价 | 说明 |
|-------------|-------------|------|
| `Paint.FontMetrics.ascent` | `fontSizePx * -0.8f` | 基线上方距离(负值) |
| `Paint.FontMetrics.descent` | `fontSizePx * 0.2f` | 基线下方距离(正值) |
| `Paint.FontMetrics.leading` | `fontSizePx * 0.1f` | 行间距 |

### 精确的基线调整算法

```kotlin
// 模拟Android原生的adjustBaseLine算法
fun adjustBaseLine(
    y: Float,
    rawAscent: Float, rawDescent: Float,  // 外部文字的字体度量
    tagAscent: Float, tagDescent: Float   // 标签文字的字体度量
): Float {
    // offset1: 外部文字的视觉中心相对于基线的偏移
    val offset1 = (rawDescent - rawAscent) / 2f - rawDescent
    
    // offset2: 标签文字的视觉中心相对于基线的偏移
    val offset2 = -(tagAscent + tagDescent) / 2f
    
    return y - offset1 + offset2
}
```

## 🎯 实现策略

### 方案1: 精确模拟原生算法

创建一个新的`PreciseVerticalCenterText`组件，完全模拟原生的基线调整。

### 方案2: 使用Canvas绘制

直接使用Compose的Canvas API，完全控制文字绘制位置。

### 方案3: 字体度量计算

通过TextMeasurer获取精确的字体度量信息。

## 📊 问题验证

### 测试用例

1. **不同字体大小**: 10sp, 14sp, 18sp, 24sp
2. **不同容器高度**: 20dp, 28dp, 36dp, 44dp
3. **不同字体**: 默认字体、粗体、斜体
4. **特殊字符**: 包含下沉字符(g, j, p, q, y)

### 预期效果

- ✅ 文字在标签中完美垂直居中
- ✅ 不同大小文字的基线对齐
- ✅ 与Android原生效果一致
- ✅ 支持所有字体和字符

## 🚨 当前问题总结

1. **算法缺失**: 缺少adjustBaseLine的等价实现
2. **度量不准**: 没有获取真实的字体度量
3. **居中错误**: 几何居中≠视觉居中
4. **基线忽略**: 没有考虑字体基线的影响

这就是为什么文字下半部分被截断的根本原因！
