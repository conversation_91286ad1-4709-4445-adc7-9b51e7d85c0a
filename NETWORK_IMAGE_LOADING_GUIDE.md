# 网络图片加载完整指南

## 🎯 概述

我们已经实现了真正的网络图片下载功能，使用Ktor Client直接从网络下载图片并在标签中显示。

## 📦 依赖配置

### 1. 添加Ktor依赖

在您的`build.gradle.kts`中添加：

```kotlin
dependencies {
    // Ktor Client核心
    implementation("io.ktor:ktor-client-core:2.3.7")
    implementation("io.ktor:ktor-client-cio:2.3.7") // CIO引擎
    
    // Ktor插件
    implementation("io.ktor:ktor-client-logging:2.3.7") // 日志
    
    // 协程支持
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
    
    // Compose图片支持
    implementation("org.jetbrains.compose.ui:ui-graphics:1.5.4")
}
```

### 2. 平台特定依赖

根据目标平台添加相应的Ktor引擎：

```kotlin
// Android
implementation("io.ktor:ktor-client-android:2.3.7")

// iOS
implementation("io.ktor:ktor-client-darwin:2.3.7")

// Desktop
implementation("io.ktor:ktor-client-cio:2.3.7")
```

## 🔧 实现架构

### 1. NetworkImageLoader类

```kotlin
class NetworkImageLoader : TagImageLoader {
    private val httpClient by lazy { createHttpClient() }
    
    override fun loadImage(url: String, imageCallback: ImageCallback?) {
        when {
            url.startsWith("http://") || url.startsWith("https://") -> {
                downloadNetworkImage(url, imageCallback)
            }
            else -> {
                imageCallback?.onBitmapReady(ColorPainter(Color.Gray))
            }
        }
    }
    
    private fun downloadNetworkImage(url: String, imageCallback: ImageCallback?) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val imageData = downloadImageData(url)
                if (imageData != null) {
                    val painter = createPainterFromBytes(imageData)
                    withContext(Dispatchers.Main) {
                        if (painter != null) {
                            imageCallback?.onBitmapReady(painter)
                        } else {
                            imageCallback?.onFail(ColorPainter(Color.Red))
                        }
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    imageCallback?.onFail(ColorPainter(Color.Red))
                }
            }
        }
    }
}
```

### 2. 图片转换策略

我们实现了多层级的图片转换策略：

```kotlin
private fun createPainterFromBytes(imageData: ByteArray): Painter? {
    return try {
        // 🎯 方法1：使用Skia Image（推荐）
        val skiaImage = Image.makeFromEncoded(imageData)
        val imageBitmap = skiaImage.toComposeImageBitmap()
        BitmapPainter(imageBitmap)
    } catch (e: Exception) {
        try {
            // 🎯 方法2：直接使用toImageBitmap
            val imageBitmap = imageData.toImageBitmap()
            BitmapPainter(imageBitmap)
        } catch (e2: Exception) {
            // 🎯 方法3：模拟Painter（兜底方案）
            createSimulatedPainter(imageData)
        }
    }
}
```

## 🚀 使用方法

### 1. 初始化图片加载器

```kotlin
@Composable
fun MyApp() {
    LaunchedEffect(Unit) {
        AppTag.init(
            loader = NetworkImageLoader(), // 使用网络图片加载器
            debug = true
        )
    }
    
    // 使用标签...
}
```

### 2. 创建网络图片标签

```kotlin
val networkImageTag = TagBean(
    type = TagType.IMAGE,
    imageUrl = "https://picsum.photos/100/100?random=1",
    text = "网络图片"
)

TagGroup(
    tags = listOf(networkImageTag),
    text = "带网络图片的标签"
)
```

### 3. 切换图片加载器

```kotlin
@Composable
fun TagLibraryDemo() {
    var useNetworkLoader by remember { mutableStateOf(false) }
    
    LaunchedEffect(useNetworkLoader) {
        val loader = if (useNetworkLoader) {
            NetworkImageLoader() // 真实网络下载
        } else {
            DemoImageLoader()     // 模拟颜色
        }
        
        AppTag.init(loader = loader, debug = true)
    }
    
    // UI组件...
}
```

## 🔍 加载流程

### 1. 完整的加载流程

```
用户请求 → NetworkImageLoader.loadImage()
    ↓
检查URL类型 → 网络URL？
    ↓
启动协程 → CoroutineScope(Dispatchers.IO)
    ↓
HTTP请求 → httpClient.get(url)
    ↓
下载数据 → response.body<ByteArray>()
    ↓
转换图片 → createPainterFromBytes()
    ↓
回调主线程 → withContext(Dispatchers.Main)
    ↓
更新UI → imageCallback.onBitmapReady()
```

### 2. 错误处理流程

```
网络错误 → HTTP状态码检查
    ↓
转换错误 → 多种转换方法尝试
    ↓
兜底方案 → 基于数据生成颜色Painter
    ↓
失败回调 → imageCallback.onFail()
```

## 📊 性能特性

### 1. 异步加载
- 使用协程进行异步下载
- 不阻塞UI线程
- 支持并发加载多个图片

### 2. 内存管理
- 自动释放HTTP连接
- 图片数据及时转换
- 支持手动清理资源

### 3. 错误恢复
- 多层级转换策略
- 优雅的错误降级
- 详细的日志输出

## 🧪 测试用例

### 1. 基本网络图片

```kotlin
TagBean(
    type = TagType.IMAGE,
    imageUrl = "https://picsum.photos/100/100?random=1"
)
```

### 2. 不同格式图片

```kotlin
val testUrls = listOf(
    "https://httpbin.org/image/png",    // PNG格式
    "https://httpbin.org/image/jpeg",   // JPEG格式
    "https://httpbin.org/image/webp"    // WebP格式
)
```

### 3. 错误处理测试

```kotlin
val errorUrls = listOf(
    "https://httpbin.org/status/404",   // 404错误
    "https://invalid-url.com/image.jpg", // 无效URL
    "not-a-url"                         // 非URL格式
)
```

## 🔧 配置选项

### 1. HTTP客户端配置

```kotlin
private fun createHttpClient(): HttpClient {
    return HttpClient(CIO) {
        // 超时配置
        install(HttpTimeout) {
            requestTimeoutMillis = 30000
            connectTimeoutMillis = 10000
            socketTimeoutMillis = 30000
        }
        
        // 用户代理
        install(UserAgent) {
            agent = "KMP-TagLibrary/1.0"
        }
        
        // 重定向支持
        followRedirects = true
        
        // 日志配置
        install(Logging) {
            level = LogLevel.INFO
        }
    }
}
```

### 2. 图片转换配置

```kotlin
// 支持的图片格式
val supportedFormats = listOf("jpg", "jpeg", "png", "webp", "gif")

// 最大图片大小（字节）
val maxImageSize = 5 * 1024 * 1024 // 5MB

// 转换超时时间
val conversionTimeout = 10000L // 10秒
```

## 🎯 最佳实践

### 1. 资源管理

```kotlin
class MyImageLoader : TagImageLoader {
    private val httpClient = createHttpClient()
    
    // 在应用退出时清理
    fun cleanup() {
        httpClient.close()
    }
}
```

### 2. 缓存策略

```kotlin
// 可以添加内存缓存
private val imageCache = mutableMapOf<String, Painter>()

override fun loadImage(url: String, imageCallback: ImageCallback?) {
    // 检查缓存
    imageCache[url]?.let { cachedPainter ->
        imageCallback?.onBitmapReady(cachedPainter)
        return
    }
    
    // 下载并缓存
    downloadAndCache(url, imageCallback)
}
```

### 3. 错误监控

```kotlin
// 添加错误统计
private var successCount = 0
private var errorCount = 0

private fun logResult(url: String, success: Boolean) {
    if (success) {
        successCount++
        println("✅ Success: $url (Total: $successCount)")
    } else {
        errorCount++
        println("❌ Error: $url (Total: $errorCount)")
    }
}
```

## 🎉 总结

现在您的标签库支持：

1. **真实网络图片下载** - 使用Ktor Client
2. **多格式支持** - PNG、JPEG、WebP等
3. **异步加载** - 不阻塞UI
4. **错误处理** - 多层级降级策略
5. **性能优化** - 协程并发加载
6. **跨平台兼容** - 支持所有KMP平台

您可以在Demo中切换"网络图片加载器"来测试真实的网络图片下载功能！🚀
