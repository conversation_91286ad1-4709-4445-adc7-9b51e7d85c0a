# 📚 KMP标签库使用示例

## 🚀 基础使用

### 1. 简单标签

```kotlin
@Composable
fun SimpleTagExample() {
    val tag = TagBean(
        type = TagType.FILL,
        text = "新品",
        textColor = Color.White,
        backgroundColor = Color.Red
    )
    
    TagGroup(
        tags = listOf(tag),
        text = "商品名称"
    )
}
```

### 2. 多标签组合

```kotlin
@Composable
fun MultipleTagsExample() {
    val tags = listOf(
        TagBean(type = TagType.FILL, text = "新品", textColor = Color.White, backgroundColor = Color.Red),
        TagBean(type = TagType.STROKE, text = "包邮", textColor = Color.Green, borderColor = Color.Green),
        TagBean(type = TagType.DISCOUNT, text = "5折", textColor = Color.White, backgroundColor = Color.Orange, backgroundEndColor = Color.Red)
    )
    
    TagGroup(
        tags = tags,
        text = "Apple iPhone 15 Pro Max",
        onTagClick = { tag -> println("点击了: ${tag.text}") }
    )
}
```

## 🎨 图片标签使用

### 1. 初始化图片加载器

```kotlin
// 在Application中初始化
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        AppTag.init(MyImageLoader()) // 全局设置图片加载器
    }
}

class MyImageLoader : ComposeImageLoader {
    @Composable
    override fun loadImage(url: String): Painter? {
        // 使用你喜欢的图片加载库，如Coil、Glide等
        return rememberAsyncImagePainter(url)
    }
}
```

### 2. 使用图片标签

```kotlin
@Composable
fun ImageTagExample() {
    val tags = listOf(
        TagBean(
            type = TagType.IMAGE,
            imageUrl = "https://example.com/brand-logo.png"
        ),
        TagBean(
            type = TagType.POINTS,
            text = "100积分",
            textColor = Color.White,
            backgroundColor = Color.Purple,
            imageUrl = "https://example.com/points-icon.png"
        )
    )

    // 不需要传imageLoader，自动使用全局配置
    TagGroup(
        tags = tags,
        text = "商品名称"
    )
}
```

## 📱 单行/多行控制

### 1. 商品列表（单行）

```kotlin
@Composable
fun ProductListItem(product: Product) {
    val tags = listOf(
        TagBean(type = TagType.FILL, text = "新品", textColor = Color.White, backgroundColor = Color.Red),
        TagBean(type = TagType.STROKE, text = "包邮", textColor = Color.Green, borderColor = Color.Green)
    )
    
    Card {
        Column(modifier = Modifier.padding(16.dp)) {
            TagGroup(
                tags = tags,
                text = product.name,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text("价格: ${product.price}")
        }
    }
}
```

### 2. 商品详情（多行）

```kotlin
@Composable
fun ProductDetail(product: Product) {
    val tags = product.tags.map { tagData ->
        TagBean(
            type = TagType.FILL,
            text = tagData.name,
            textColor = Color.White,
            backgroundColor = Color(tagData.color)
        )
    }
    
    TagGroup(
        tags = tags,
        text = product.description,
        maxLines = 3,
        overflow = TextOverflow.Ellipsis
    )
}
```

## 🔧 便捷方法使用

### 1. 函数式API

```kotlin
@Composable
fun ConvenienceExample() {
    val tags = createProductTags()

    Column {
        // 矩形标签在前
        ShowRectStart(
            tags = tags,
            content = "商品名称",
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )

        // 圆角标签在后
        ShowRoundEnd(
            tags = tags,
            content = "商品描述"
        )
    }
}
```

### 2. 扩展函数API

```kotlin
@Composable
fun ExtensionExample() {
    val tags = createProductTags()

    Column {
        // 更简洁的调用方式
        tags.showRectStart(
            content = "商品名称",
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )

        tags.showRoundEnd(
            content = "商品描述"
        )
    }
}
```

## 🎭 自定义样式

### 1. 预定义样式

```kotlin
@Composable
fun PredefinedStyleExample() {
    val tags = listOf(
        TagBean(
            type = TagType.FILL,
            text = "圆角标签",
            textColor = Color.White,
            backgroundColor = Color.Blue,
            appearance = TagAppearance.Round
        ),
        TagBean(
            type = TagType.FILL,
            text = "胶囊标签",
            textColor = Color.White,
            backgroundColor = Color.Green,
            appearance = TagAppearance.Capsule
        )
    )
    
    TagGroup(tags = tags, text = "使用预定义样式")
}
```

### 2. 完全自定义

```kotlin
@Composable
fun CustomStyleExample() {
    val customAppearance = TagAppearance(
        textSize = 16.sp,
        cornerRadius = 20.dp,
        horizontalPadding = 20.dp,
        verticalPadding = 10.dp,
        fontWeight = FontWeight.Bold,
        shape = RoundedCornerShape(20.dp)
    )
    
    val tag = TagBean(
        type = TagType.FILL,
        text = "自定义样式",
        textColor = Color.White,
        backgroundColor = Color.Magenta,
        appearance = customAppearance
    )
    
    TagGroup(tags = listOf(tag), text = "完全自定义的标签")
}
```

## 🔄 从原生Android迁移

### 原生Android代码

```java
// 原生Android方式
TextView textView = findViewById(R.id.textView);
textView.setMaxLines(1);
textView.setEllipsize(TextUtils.TruncateAt.END);

List<TagBean> tags = Arrays.asList(
    new TagBean(1, "新品", "#FFFFFF", "#FF0000"),
    new TagBean(3, "包邮", "#4CAF50", "", "#4CAF50")
);

TagUtils.showRectStart(context, textView, tags, "商品名称");
```

### Compose版本

```kotlin
// Compose版本
@Composable
fun MigratedExample() {
    val tags = listOf(
        TagBean(type = TagType.FILL, text = "新品", textColor = Color.White, backgroundColor = Color.Red),
        TagBean(type = TagType.STROKE, text = "包邮", textColor = Color.Green, borderColor = Color.Green)
    )
    
    ShowRectStart(
        tags = tags,
        content = "商品名称",
        maxLines = 1,
        overflow = TextOverflow.Ellipsis
    )
}
```

## 🎯 实际应用场景

### 1. 电商应用

```kotlin
@Composable
fun ECommerceExample() {
    val productTags = listOf(
        TagBean(type = TagType.FILL, text = "限时特价", textColor = Color.White, backgroundColor = Color.Red),
        TagBean(type = TagType.STROKE, text = "正品保证", textColor = Color.Blue, borderColor = Color.Blue),
        TagBean(type = TagType.DISCOUNT, text = "满减", textColor = Color.White, backgroundColor = Color.Orange, backgroundEndColor = Color.Red)
    )
    
    LazyColumn {
        items(products) { product ->
            ProductCard(product, productTags)
        }
    }
}

@Composable
fun ProductCard(product: Product, tags: List<TagBean>) {
    Card(modifier = Modifier.padding(8.dp)) {
        Column(modifier = Modifier.padding(16.dp)) {
            TagGroup(
                tags = tags,
                text = product.name,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            Text("价格: ¥${product.price}")
            Text("销量: ${product.sales}")
        }
    }
}
```

### 2. 新闻应用

```kotlin
@Composable
fun NewsExample() {
    val newsTags = listOf(
        TagBean(type = TagType.FILL, text = "热点", textColor = Color.White, backgroundColor = Color.Red),
        TagBean(type = TagType.STROKE, text = "科技", textColor = Color.Blue, borderColor = Color.Blue)
    )
    
    LazyColumn {
        items(newsItems) { news ->
            NewsCard(news, newsTags)
        }
    }
}

@Composable
fun NewsCard(news: News, tags: List<TagBean>) {
    Card(modifier = Modifier.padding(8.dp)) {
        Column(modifier = Modifier.padding(16.dp)) {
            TagGroup(
                tags = tags,
                text = news.title,
                maxLines = 3,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = news.summary,
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
        }
    }
}
```

## 🔍 调试和测试

### 启用调试模式

```kotlin
@Composable
fun DebugExample() {
    // 启用调试模式
    LaunchedEffect(Unit) {
        TagUtils.isDebugMode = true
    }
    
    // 故意创建有问题的标签来测试调试功能
    val problematicTags = listOf(
        TagBean(type = TagType.FILL, text = "", textColor = Color.White, backgroundColor = Color.Red), // 空文字
        TagBean(type = TagType.IMAGE, imageUrl = "") // 空URL
    )
    
    TagGroup(tags = problematicTags, text = "调试测试")
    // 检查控制台输出的调试信息
}
```

## 💡 最佳实践

### 1. 性能优化

```kotlin
@Composable
fun OptimizedExample(products: List<Product>) {
    // ✅ 缓存标签列表
    val commonTags = remember {
        listOf(
            TagBean(type = TagType.FILL, text = "新品", textColor = Color.White, backgroundColor = Color.Red),
            TagBean(type = TagType.STROKE, text = "包邮", textColor = Color.Green, borderColor = Color.Green)
        )
    }
    
    // ✅ 缓存图片加载器
    val imageLoader = remember { MyImageLoader() }
    
    LazyColumn {
        items(products, key = { it.id }) { product ->
            TagGroup(
                tags = commonTags,
                text = product.name,
                imageLoader = imageLoader,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}
```

### 2. 主题适配

```kotlin
@Composable
fun ThemedExample() {
    val isDarkTheme = isSystemInDarkTheme()
    
    val tags = listOf(
        TagBean(
            type = TagType.FILL,
            text = "主题适配",
            textColor = if (isDarkTheme) Color.Black else Color.White,
            backgroundColor = if (isDarkTheme) Color.White else Color.Black
        )
    )
    
    TagGroup(tags = tags, text = "自动适配主题")
}
```

这些示例展示了KMP标签库的各种使用方式，从基础用法到高级特性，帮助你快速上手并充分利用库的功能！🚀
