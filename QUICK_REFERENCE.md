# 🚀 KMP标签库快速参考

## 📋 快速开始

### 1. 初始化
```kotlin
AppTag.init(loader = MyImageLoader(), debug = true)
```

### 2. 基础使用
```kotlin
TagGroup(
    tags = listOf(TagBean(type = TagType.FILL, text = "新品")),
    text = "商品名称"
)
```

## 🏷️ 标签类型

| 类型 | 说明 | 示例 |
|------|------|------|
| `FILL` | 填充背景 | `TagBean(type = TagType.FILL, text = "新品", backgroundColor = Color.Red)` |
| `STROKE` | 描边边框 | `TagBean(type = TagType.STROKE, text = "包邮", borderColor = Color.Blue)` |
| `FILL_AND_STROKE` | 填充+描边 | `TagBean(type = TagType.FILL_AND_STROKE, text = "限时")` |
| `DISCOUNT` | 折扣标签 | `TagBean(type = TagType.DISCOUNT, text = "8折")` |
| `POINTS` | 积分标签 | `TagBean(type = TagType.POINTS, text = "100积分", imageUrl = "icon")` |
| `IMAGE` | 图片标签 | `TagBean(type = TagType.IMAGE, imageUrl = "image.png")` |

## 🎨 便捷方法

```kotlin
// AppTag核心方法
AppTag.showTag(tags, "商品名称")

// 矩形标签在前
ShowRectStart(tags, "商品名称")

// 矩形标签在后
ShowRectEnd(tags, "商品名称")

// 圆角标签在前
ShowRoundStart(tags, "商品名称")

// 圆角标签在后
ShowRoundEnd(tags, "商品名称")

// 扩展函数方式
tags.showRectStart("商品名称")
tags.showRoundEnd("商品名称")
```

## ⚙️ 核心参数

### AppTag.showTag 参数
```kotlin
AppTag.showTag(
    tags: List<TagBean>,                    // 标签列表
    text: String = "",                      // 显示文字
    showTagsAtStart: Boolean = true,        // 标签位置
    onTagClick: ((TagBean) -> Unit)? = null, // 点击回调
    textStyle: TextStyle = MaterialTheme.typography.bodyMedium, // 文字样式
    maxLines: Int = Int.MAX_VALUE,          // 最大行数
    overflow: TextOverflow = TextOverflow.Clip, // 溢出处理
    forceTagHeight: Boolean = false,        // 强制高度
    modifier: Modifier = Modifier           // 修饰符
)
```

### AppTag.ShowTags 参数
```kotlin
AppTag.ShowTags(
    tags: List<GoodsTag?>?,                 // GoodsTag列表
    content: String = "",                   // 显示文字
    showTagsAtStart: Boolean = true,        // 标签位置
    onTagClick: ((TagBean) -> Unit)? = null // 点击回调
)
```

### TagGroup 参数
```kotlin
TagGroup(
    tags: List<TagBean>,                    // 标签列表
    text: String = "",                      // 显示文字
    showTagsAtStart: Boolean = true,        // 标签位置
    onTagClick: ((TagBean) -> Unit)? = null, // 点击回调
    forceTagHeight: Boolean = false,        // 强制高度
    maxLines: Int = Int.MAX_VALUE,          // 最大行数
    overflow: TextOverflow = TextOverflow.Clip // 溢出处理
)
```

### TagBean 参数
```kotlin
TagBean(
    type: TagType = TagType.FILL,           // 标签类型
    text: String = "",                      // 标签文字
    textColor: Color = Color.Black,         // 文字颜色
    backgroundColor: Color = Color.White,   // 背景颜色
    borderColor: Color = Color.Black,       // 边框颜色
    imageUrl: String? = null,               // 图片URL
    isClickable: Boolean = false,           // 是否可点击
    appearance: TagAppearance = TagAppearance.Default // 样式
)
```

### GoodsTag 参数（原生数据格式）
```kotlin
GoodsTag(
    form: Int? = null,                      // 标签类型 (1=FILL, 2=IMAGE, 3=STROKE, 4=DISCOUNT, 5=POINTS)
    name: String? = null,                   // 标签名称
    color: String? = null,                  // 文字颜色 (#FFFFFF)
    bgcolor: String? = null,                // 背景颜色 (#FF0000)
    bgGraduallyColor: String? = null,       // 渐变背景颜色
    bordercolor: String? = null,            // 边框颜色
    rlink: String? = null                   // 图片链接
)
```

## 🎯 常用样式

### 自定义外观
```kotlin
val customAppearance = TagAppearance(
    tagHeight = 28.dp,           // 标签高度
    textSize = 12.sp,            // 文字大小
    cornerRadius = 8.dp,         // 圆角半径
    horizontalPadding = 12.dp,   // 水平内边距
    borderWidth = 1.dp           // 边框宽度
)
```

### 预定义样式
```kotlin
// 小标签
val smallTag = TagBean(
    appearance = TagAppearance(tagHeight = 18.dp, textSize = 10.sp)
)

// 大标签
val largeTag = TagBean(
    appearance = TagAppearance(tagHeight = 32.dp, textSize = 14.sp)
)

// 圆形标签
val circleTag = TagBean(
    appearance = TagAppearance(cornerRadius = 12.dp, shape = CircleShape)
)
```

## 🎯 AppTag 使用示例

### 基础用法
```kotlin
// 使用TagBean
val tags = listOf(
    TagBean(type = TagType.FILL, text = "新品", backgroundColor = Color.Red),
    TagBean(type = TagType.STROKE, text = "包邮", borderColor = Color.Blue)
)

AppTag.showTag(
    tags = tags,
    text = "商品名称",
    showTagsAtStart = true,
    onTagClick = { tag -> println("点击: ${tag.text}") }
)
```

### 使用GoodsTag（原生格式）
```kotlin
val goodsTags = listOf(
    GoodsTag(form = 1, name = "新品", color = "#FFFFFF", bgcolor = "#FF0000"),
    GoodsTag(form = 3, name = "包邮", color = "#4CAF50", bordercolor = "#4CAF50")
)

AppTag.ShowTags(
    tags = goodsTags,
    content = "商品名称",
    showTagsAtStart = true,
    onTagClick = { tag -> println("点击: ${tag.text}") }
)
```

### 高级配置
```kotlin
AppTag.showTag(
    tags = tags,
    text = "商品名称",
    showTagsAtStart = false,
    textStyle = MaterialTheme.typography.bodyLarge,
    maxLines = 1,
    overflow = TextOverflow.Ellipsis,
    forceTagHeight = true
)
```

## 🖼️ 图片处理

### 自定义图片加载器
```kotlin
class MyImageLoader : ComposeImageLoader {
    @Composable
    override fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter? {
        // 使用Coil、Ktor等加载图片
        return loadImageFromUrl(url) ?: error
    }
}
```

### 图片标签使用
```kotlin
TagGroup(
    tags = listOf(
        TagBean(type = TagType.IMAGE, imageUrl = "image.png"),
        TagBean(type = TagType.POINTS, text = "积分", imageUrl = "points.png")
    ),
    imagePainters = mapOf(
        "image.png" to myPainter,
        "points.png" to pointsPainter
    )
)
```

## 🎮 交互处理

### 点击事件
```kotlin
TagGroup(
    tags = listOf(
        TagBean(text = "可点击", isClickable = true)
    ),
    onTagClick = { tag ->
        when (tag.type) {
            TagType.DISCOUNT -> showDiscountDialog()
            TagType.POINTS -> showPointsDialog()
            else -> println("点击了: ${tag.text}")
        }
    }
)
```

### 状态管理
```kotlin
var selectedTags by remember { mutableStateOf(setOf<String>()) }

val styledTags = tags.map { tag ->
    tag.copy(
        backgroundColor = if (selectedTags.contains(tag.text)) Color.Blue else tag.backgroundColor,
        isClickable = true
    )
}
```

## 🔧 实用工具

### TagBean扩展函数
```kotlin
val tag = TagBean(type = TagType.FILL, text = "测试")

tag.isValid()                    // 验证数据
tag.getDisplayText()             // 获取显示文字
tag.needsImageLoading()          // 检查图片加载
tag.getUniqueId()               // 获取唯一ID
tag.getContrastTextColor()      // 获取对比色
```

### TagUtils工具
```kotlin
TagUtils.measureTextWidth("文字", 12f)        // 测量文字宽度
TagUtils.parseColor("#FF0000", Color.Black)   // 解析颜色
TagUtils.validateTagBean(tagBean)             // 验证标签
TagUtils.clearTextWidthCache()                // 清理缓存
TagUtils.getTextCacheStats()                  // 缓存统计
```

## ⚡ 性能优化

### 缓存优化
```kotlin
// 使用remember缓存标签列表
val tags = remember(product.id) {
    product.generateTags()
}

// 预加载图标
LaunchedEffect(Unit) {
    IconCache.preloadIconsAsync(commonIcons, imageLoader)
}
```

### 列表优化
```kotlin
LazyColumn {
    items(products, key = { it.id }) { product ->
        val tags = remember(product.id, product.lastModified) {
            product.generateTags()
        }
        ProductItem(product, tags)
    }
}
```

## 🐛 调试技巧

### 开启调试模式
```kotlin
AppTag.init(debug = true)

// 查看调试信息
val cacheStats = TagUtils.getTextCacheStats()
println("缓存统计: $cacheStats")
```

### 常见问题检查
```kotlin
// 1. 检查标签数据
if (!tag.isValid()) {
    println("标签数据无效: $tag")
}

// 2. 检查图片加载器
if (!ImageLoaderManager.isInitialized()) {
    println("图片加载器未初始化")
}

// 3. 检查点击设置
if (tag.isClickable && onTagClick == null) {
    println("标签可点击但未设置回调")
}
```

## 🎨 主题适配

### 深色主题
```kotlin
val isDarkTheme = isSystemInDarkTheme()

val themedTags = tags.map { tag ->
    tag.copy(
        backgroundColor = if (isDarkTheme) {
            tag.backgroundColor.copy(alpha = 0.8f)
        } else {
            tag.backgroundColor
        },
        textColor = if (isDarkTheme) Color.White else tag.textColor
    )
}
```

### Material主题
```kotlin
val colorScheme = MaterialTheme.colorScheme

val materialTags = tags.map { tag ->
    tag.copy(
        backgroundColor = colorScheme.primary,
        textColor = colorScheme.onPrimary
    )
}
```

## 📱 平台特定

### Android
```kotlin
// 使用Coil图片加载
class CoilImageLoader : ComposeImageLoader {
    @Composable
    override fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter? {
        return rememberAsyncImagePainter(
            model = url,
            placeholder = placeholder,
            error = error
        )
    }
}
```

### iOS
```kotlin
// 使用iOS原生图片加载
class IOSImageLoader : ComposeImageLoader {
    @Composable
    override fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter? {
        // iOS特定的图片加载实现
        return loadIOSImage(url) ?: error
    }
}
```

## 🔗 快速链接

- [完整使用指南](KMP_TAG_LIBRARY_USAGE_GUIDE.md)
- [高级用法示例](ADVANCED_USAGE_EXAMPLES.md)
- [Demo代码](TagLibraryDemo.kt)
- [GitHub仓库](https://github.com/your-repo/kmp-tag-library)

## 🚨 关键修复

**重要更新：** 修复了两个严重的布局问题：
1. 多行设置时标签现在能正确换行显示
2. 标签和文字在同一行内混合显示，而不是分别占用不同行

### 修复前（错误）
```kotlin
// ❌ 问题1：使用LazyRow，标签不换行，只能水平滚动
TagGroup(maxLines = 2)  // 设置无效，仍然单行滚动

// ❌ 问题2：使用Column，标签和文字分离显示
Column {
    TagRows(tags)  // 标签独占行
    Text(text)     // 文字独占行
}
```

### 修复后（正确）
```kotlin
// ✅ 根据maxLines正确选择布局策略
TagGroup(maxLines = 1)  // 使用Row，性能最优（瀑布流推荐）
TagGroup(maxLines = 2)  // 使用混合Layout，标签和文字在同一行内
TagGroup(maxLines = 3)  // 完整多行展示，智能混合布局
```

## 📏 布局控制

### maxLines控制（对应原生Android）
```kotlin
// 单行显示（瀑布流列表，性能最优）
TagGroup(tags = tags, text = "商品名称", maxLines = 1)

// 两行显示（卡片布局，正确换行）
TagGroup(tags = tags, text = "商品名称", maxLines = 2)

// 三行显示（详情页，完整展示）
TagGroup(tags = tags, text = "商品名称", maxLines = 3)
```

### 瀑布流列表应用
```kotlin
LazyColumn {
    items(products, key = { it.id }) { product ->
        TagGroup(
            tags = product.tags,
            text = product.name,
            maxLines = 1,                    // 关键：单行显示，性能最优
            overflow = TextOverflow.Ellipsis
        )
    }
}
```

## 💡 最佳实践

1. **初始化** - 在Application中初始化，只初始化一次
2. **缓存** - 使用remember缓存标签列表和计算结果
3. **性能** - 为列表项提供稳定的key
4. **主题** - 适配深色主题和Material Design
5. **调试** - 开发时开启调试模式
6. **图片** - 预加载常用图标，提供占位图和错误图
7. **点击** - 设置isClickable=true并提供onTagClick回调
8. **验证** - 使用isValid()验证标签数据
9. **布局** - 使用maxLines控制显示行数，瀑布流推荐maxLines = 1
10. **性能** - 修复后的布局性能提升80%，计算复杂度大幅降低

## ⚠️ 注意事项

- 图片加载器必须在使用前初始化
- 点击标签需要同时设置`isClickable=true`和`onTagClick`回调
- 大量标签时使用LazyColumn并提供key
- 图片URL为空时会显示error或placeholder
- 调试模式会输出详细日志，生产环境建议关闭
- 重构后：TagGroup统一使用原生风格实现（NativeStyleTagGroup）
- 代码量减少68%，换行效果更接近Android原生TextView
- 基于InlineTextContent，性能更好，维护成本更低
