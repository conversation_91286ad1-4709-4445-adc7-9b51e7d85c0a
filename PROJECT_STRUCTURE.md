# 📁 KMP标签库项目结构

## 🏗️ 项目概览

```
kmp-tag-library/
├── 📁 src/
│   ├── 📁 commonMain/kotlin/com/taglib/
│   │   ├── 📄 AppTag.kt                    # 外部配置入口
│   │   ├── 📄 TagBean.kt                   # 标签数据模型
│   │   ├── 📄 TagType.kt                   # 标签类型枚举
│   │   ├── 📄 TagAppearance.kt             # 样式配置
│   │   ├── 📄 TagCompose.kt                # 核心Compose组件
│   │   ├── 📄 TagConvenience.kt            # 便捷方法
│   │   ├── 📄 TagUtils.kt                  # 工具函数
│   │   ├── 📄 ImageLoader.kt               # 图片加载接口
│   │   ├── 📄 ImageLoadingValidator.kt     # 图片加载验证
│   │   ├── 📄 IconCache.kt                 # 图标缓存
│   │   ├── 📄 PlatformTime.kt              # 跨平台时间
│   │   └── 📁 components/
│   │       ├── 📄 FillTag.kt               # 填充标签组件
│   │       ├── 📄 StrokeTag.kt             # 描边标签组件
│   │       ├── 📄 ImageTag.kt              # 图片标签组件
│   │       ├── 📄 DiscountTag.kt           # 折扣标签组件
│   │       └── 📄 PointsTag.kt             # 积分标签组件
│   ├── 📁 androidMain/kotlin/com/taglib/
│   │   └── 📄 PlatformTime.kt              # Android平台时间实现
│   ├── 📁 iosMain/kotlin/com/taglib/
│   │   └── 📄 PlatformTime.kt              # iOS平台时间实现
│   └── 📁 jsMain/kotlin/com/taglib/        # JS平台实现（可选）
├── 📄 build.gradle.kts                     # 构建配置
├── 📄 TagLibraryDemo.kt                    # 完整Demo
├── 📄 KMP_TAG_LIBRARY_USAGE_GUIDE.md      # 使用指南
├── 📄 ADVANCED_USAGE_EXAMPLES.md          # 高级用法
├── 📄 QUICK_REFERENCE.md                  # 快速参考
└── 📄 README.md                           # 项目说明
```

## 📋 核心文件说明

### 🎯 外部接口层

#### AppTag.kt
```kotlin
// 外部配置入口，对应原生的TagUtils初始化
object AppTag {
    fun init(loader: ComposeImageLoader?, debug: Boolean = false)
    fun getImageLoader(): ComposeImageLoader?
    fun isInitialized(): Boolean
}
```

#### TagCompose.kt
```kotlin
// 核心Compose组件，对应原生的showTag方法
@Composable
fun TagGroup(
    tags: List<TagBean>,
    text: String,
    // ... 其他参数
)
```

#### TagConvenience.kt
```kotlin
// 便捷方法，对应原生的showRectStart等方法
@Composable fun ShowRectStart(...)
@Composable fun ShowRectEnd(...)
@Composable fun ShowRoundStart(...)
@Composable fun ShowRoundEnd(...)

// 扩展函数API
@Composable fun List<TagBean>.showRectStart(...)
```

### 📊 数据模型层

#### TagBean.kt
```kotlin
// 标签数据模型，对应原生的TagBean类
data class TagBean(
    val type: TagType,
    val text: String,
    val textColor: Color,
    val backgroundColor: Color,
    // ... 其他属性
) {
    // 扩展方法
    fun isValid(): Boolean
    fun getDisplayText(): String
    fun needsImageLoading(): Boolean
    // ...
}
```

#### TagType.kt
```kotlin
// 标签类型枚举，对应原生的常量定义
enum class TagType {
    FILL,           // FORM_FILL = 1
    IMAGE,          // FORM_IMAGE = 2
    STROKE,         // FORM_STROKE = 3
    DISCOUNT,       // FORM_ZS = 4
    POINTS,         // FROM_JF = 5
    FILL_AND_STROKE // FORM_FILL_AND_STROKE = -1
}
```

#### TagAppearance.kt
```kotlin
// 样式配置，对应原生的TagAppearance类
data class TagAppearance(
    val tagHeight: Dp,
    val textSize: TextUnit,
    val cornerRadius: Dp,
    // ... 其他样式属性
)
```

### 🔧 工具函数层

#### TagUtils.kt
```kotlin
// 工具函数，对应原生的TagUtils类
object TagUtils {
    fun measureTextWidth(text: String, textSize: Float): Float
    fun parseColor(colorString: String?, defaultColor: Color): Color
    fun validateTagBean(tagBean: TagBean): Boolean
    // ... 其他工具方法
}
```

#### ImageLoader.kt
```kotlin
// 图片加载接口定义
interface ComposeImageLoader {
    @Composable
    fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter?
}

interface ImageLoader {
    fun loadImage(url: String, callback: ImageCallback)
}

// 空实现，避免空指针
class EmptyComposeImageLoader : ComposeImageLoader
```

### 🖼️ 图片处理层

#### ImageLoadingValidator.kt
```kotlin
// 图片加载验证，对应原生的poseSetImgText机制
object ImageLoadingValidator {
    @Composable
    fun ValidatedImageLoader(...): LoadingState
    
    @Composable
    fun loadValidatedImage(...): Painter?
    
    // Token验证机制
    private fun isTokenValid(token: LoadingToken, currentUrl: String): Boolean
}
```

#### IconCache.kt
```kotlin
// 图标缓存，对应原生的getJFBitmap机制
object IconCache {
    @Composable
    fun getPointsIcon(...): Painter?
    
    suspend fun preloadIconsAsync(...)
    fun getCacheStats(): CacheStats
    fun cleanupExpiredCache()
}
```

### 🎨 组件实现层

#### components/FillTag.kt
```kotlin
// 填充标签组件，对应原生的FillBgSpan
@Composable
internal fun FillTag(
    tagBean: TagBean,
    modifier: Modifier = Modifier
)
```

#### components/StrokeTag.kt
```kotlin
// 描边标签组件，对应原生的StrokeBgSpan
@Composable
internal fun StrokeTag(
    tagBean: TagBean,
    modifier: Modifier = Modifier
)
```

#### components/ImageTag.kt
```kotlin
// 图片标签组件，对应原生的TagImageSpan
@Composable
internal fun ImageTag(
    tagBean: TagBean,
    imagePainter: Painter?,
    modifier: Modifier = Modifier
)
```

#### components/DiscountTag.kt
```kotlin
// 折扣标签组件，对应原生的ZSBgTag
@Composable
internal fun DiscountTag(
    tagBean: TagBean,
    modifier: Modifier = Modifier
)
```

#### components/PointsTag.kt
```kotlin
// 积分标签组件，对应原生的JFSpan
@Composable
internal fun PointsTag(
    tagBean: TagBean,
    iconPainter: Painter?,
    modifier: Modifier = Modifier
)
```

### 🌍 平台特定层

#### PlatformTime.kt (commonMain)
```kotlin
// 跨平台时间接口
expect fun getCurrentTimeMillis(): Long
```

#### PlatformTime.kt (androidMain)
```kotlin
// Android平台实现
actual fun getCurrentTimeMillis(): Long = System.currentTimeMillis()
```

#### PlatformTime.kt (iosMain)
```kotlin
// iOS平台实现
actual fun getCurrentTimeMillis(): Long = (NSDate().timeIntervalSince1970 * 1000).toLong()
```

## 🔄 数据流向

```
外部应用
    ↓ AppTag.init()
ImageLoaderManager (内部状态管理)
    ↓ 提供配置
TagGroup (核心组件)
    ↓ 渲染标签
具体标签组件 (FillTag, StrokeTag等)
    ↓ 图片加载
ImageLoadingValidator + IconCache
    ↓ 验证和缓存
ComposeImageLoader (具体实现)
```

## 🎯 架构设计原则

### 1. 分层架构
- **外部接口层** - 提供简洁的API
- **数据模型层** - 类型安全的数据结构
- **工具函数层** - 可复用的工具方法
- **组件实现层** - 具体的UI组件
- **平台特定层** - 跨平台适配

### 2. 职责分离
- **AppTag** - 外部配置管理
- **ImageLoaderManager** - 内部状态管理
- **TagGroup** - 组件组合和布局
- **具体组件** - 单一标签渲染
- **工具类** - 纯函数工具

### 3. 依赖方向
```
外部应用 → AppTag → ImageLoaderManager → TagGroup → 具体组件
                                            ↓
                                      工具函数 ← 平台特定
```

## 📦 模块依赖

### Gradle配置
```kotlin
kotlin {
    sourceSets {
        val commonMain by getting {
            dependencies {
                implementation(compose.runtime)
                implementation(compose.foundation)
                implementation(compose.material3)
                implementation(compose.ui)
                implementation(compose.components.resources)
                implementation(compose.components.uiToolingPreview)
            }
        }
        
        val androidMain by getting {
            dependencies {
                // Android特定依赖
            }
        }
        
        val iosMain by getting {
            dependencies {
                // iOS特定依赖
            }
        }
    }
}
```

## 🔧 扩展点

### 1. 自定义标签类型
```kotlin
// 在TagType中添加新类型
enum class TagType {
    // 现有类型...
    CUSTOM_NEW_TYPE  // 新增类型
}

// 在components/中添加对应组件
@Composable
internal fun CustomNewTypeTag(...)
```

### 2. 自定义图片加载器
```kotlin
class MyImageLoader : ComposeImageLoader {
    @Composable
    override fun loadImage(...): Painter? {
        // 自定义实现
    }
}
```

### 3. 自定义样式
```kotlin
val customAppearance = TagAppearance(
    // 自定义样式参数
)
```

## 📊 性能考虑

### 1. 缓存策略
- **文字测量缓存** - TagUtils.measureTextWidth
- **图标缓存** - IconCache.getPointsIcon
- **组件缓存** - remember在组件中的使用

### 2. 内存管理
- **自动清理** - 过期缓存自动清理
- **大小限制** - 缓存大小限制防止内存泄漏
- **弱引用** - 适当使用弱引用

### 3. 渲染优化
- **组件复用** - 相同类型标签复用组件
- **状态最小化** - 只在必要时触发重组
- **懒加载** - 图片和图标懒加载

## 🧪 测试策略

### 1. 单元测试
- TagBean数据验证
- TagUtils工具函数
- 颜色解析和文字测量

### 2. 组件测试
- 各种标签类型渲染
- 点击事件处理
- 样式应用

### 3. 集成测试
- 完整的标签组合
- 图片加载流程
- 缓存机制

### 4. 性能测试
- 大量标签渲染性能
- 内存使用情况
- 缓存命中率

这个项目结构设计确保了代码的可维护性、可扩展性和高性能，同时保持了与原生Android库的完全兼容性。
