# 🏗️ 架构修复：正确的依赖关系

## ❌ **之前的错误设计**

### 问题：组件库依赖外部配置类
```kotlin
// ❌ 错误：组件库内部导入外部配置类
import com.taglib.AppTag  // AppTag是给外部项目使用的！

@Composable
fun ImageTag(...) {
    val validatedPainter = tagBean.loadValidatedImage(
        imageLoader = AppTag.getImageLoader(), // ❌ 组件库不应该依赖外部配置
        placeholder = imagePainter,
        error = imagePainter
    )
}
```

### 为什么这样是错误的？
1. **AppTag是外部配置类** - 专门给使用方项目配置用的
2. **组件库应该独立** - 不应该依赖外部配置
3. **循环依赖风险** - 可能导致编译问题
4. **违反设计原则** - 内部组件不应该知道外部配置

## ✅ **正确的架构设计**

### 架构层次
```
┌─────────────────────────────────────┐
│           外部项目层                  │
│  ┌─────────────────────────────────┐ │
│  │          AppTag.kt              │ │ ← 外部配置类
│  │   (外部项目使用的配置接口)        │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
                    │
                    │ 通过ImageLoaderManager设置
                    ▼
┌─────────────────────────────────────┐
│           组件库层                   │
│  ┌─────────────────────────────────┐ │
│  │     ImageLoaderManager.kt       │ │ ← 全局状态管理
│  │      (组件库内部管理器)          │ │
│  └─────────────────────────────────┘ │
│                    │                 │
│                    │ 组件获取配置      │
│                    ▼                 │
│  ┌─────────────────────────────────┐ │
│  │    ImageTag.kt / PointsTag.kt   │ │ ← 具体组件
│  │       (组件库内部组件)           │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 正确的依赖关系
```kotlin
// ✅ 正确：组件库内部使用内部管理器
import com.taglib.ImageLoaderManager  // 组件库内部的管理器

@Composable
fun ImageTag(...) {
    val validatedPainter = tagBean.loadValidatedImage(
        imageLoader = ImageLoaderManager.getComposeImageLoader(), // ✅ 使用内部管理器
        placeholder = imagePainter,
        error = imagePainter
    )
}
```

## 🔧 **修复内容**

### 1. **ImageTag.kt** ✅
```kotlin
// 修复前
import com.taglib.AppTag                    // ❌ 错误导入

// 修复后
import com.taglib.ImageLoaderManager        // ✅ 正确导入
import com.taglib.loadValidatedImage

// 使用方式
val validatedPainter = tagBean.loadValidatedImage(
    imageLoader = ImageLoaderManager.getComposeImageLoader(), // ✅ 通过内部管理器获取
    placeholder = imagePainter,
    error = imagePainter
)
```

### 2. **PointsTag.kt** ✅
```kotlin
// 修复前
import com.taglib.AppTag                    // ❌ 错误导入

// 修复后
import com.taglib.ImageLoaderManager        // ✅ 正确导入
import com.taglib.getCachedPointsIcon

// 使用方式
val cachedIcon = tagBean.getCachedPointsIcon(
    imageLoader = ImageLoaderManager.getComposeImageLoader(), // ✅ 通过内部管理器获取
    defaultIcon = iconPainter
)
```

### 3. **AppTag.kt** ✅
```kotlin
// AppTag作为外部配置类，通过ImageLoaderManager设置全局状态
object AppTag {
    fun init(loader: ComposeImageLoader? = null, debug: Boolean = false) {
        // 设置到组件库的内部管理器
        ImageLoaderManager.initializeCompose(loader ?: DefaultComposeImageLoader())
        
        // 其他配置...
        TagUtils.isDebugMode = debug
        // ...
    }
    
    // 外部项目可以获取当前配置
    fun getImageLoader(): ComposeImageLoader? = imageLoader
}
```

## 📊 **数据流向**

### 初始化流程
```
外部项目
    │
    │ AppTag.init(myImageLoader)
    ▼
ImageLoaderManager.initializeCompose(myImageLoader)
    │
    │ 存储到内部状态
    ▼
组件库内部状态已设置
```

### 使用流程
```
组件渲染
    │
    │ ImageLoaderManager.getComposeImageLoader()
    ▼
获取全局配置的imageLoader
    │
    │ 传递给扩展函数
    ▼
执行图片加载逻辑
```

## 🎯 **架构优势**

### ✅ **清晰的职责分离**
- **AppTag** - 外部配置接口，给使用方项目用
- **ImageLoaderManager** - 组件库内部状态管理
- **组件** - 专注于UI渲染，通过内部管理器获取配置

### ✅ **无循环依赖**
- 外部配置类 → 内部管理器 → 内部组件
- 单向依赖，不会产生循环

### ✅ **易于测试**
- 组件可以独立测试
- 可以Mock ImageLoaderManager
- 不依赖外部配置

### ✅ **符合设计原则**
- **单一职责** - 每个类职责明确
- **依赖倒置** - 组件依赖抽象，不依赖具体配置
- **开闭原则** - 易于扩展，无需修改组件

## 🚀 **使用方式**

### 外部项目使用
```kotlin
// 1. 初始化（在Application中）
AppTag.init(MyImageLoader(), debug = true)

// 2. 使用组件（无需传递imageLoader）
@Composable
fun ProductScreen() {
    TagGroup(
        tags = tags,
        text = "商品名称"
        // ✅ 无需传递imageLoader，自动使用全局配置
    )
}
```

### 组件库内部
```kotlin
// 组件内部自动获取全局配置
@Composable
fun ImageTag(...) {
    val validatedPainter = tagBean.loadValidatedImage(
        imageLoader = ImageLoaderManager.getComposeImageLoader(), // 自动获取
        placeholder = imagePainter,
        error = imagePainter
    )
}
```

## 📝 **总结**

### 修复前的问题
- ❌ 组件库依赖外部配置类
- ❌ 违反架构设计原则
- ❌ 可能导致循环依赖

### 修复后的优势
- ✅ 清晰的分层架构
- ✅ 正确的依赖方向
- ✅ 易于维护和测试
- ✅ 符合设计原则

### 最终效果
- 🎯 **外部使用简单** - `AppTag.init()` 一次配置
- 🎯 **内部实现清晰** - 通过 `ImageLoaderManager` 管理状态
- 🎯 **架构合理** - 单向依赖，职责分离
- 🎯 **完全对应原生库** - 使用方式与原生库一致

现在的架构是正确的、清晰的、可维护的！🎉
