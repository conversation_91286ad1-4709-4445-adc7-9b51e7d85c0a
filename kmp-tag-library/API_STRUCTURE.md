# 📚 KMP标签库API结构说明

## 🏗️ 代码组织结构

### 核心文件组织

```
com.taglib/
├── TagBean.kt              # 标签数据模型
├── TagType.kt              # 标签类型枚举
├── TagAppearance.kt        # 样式配置类
├── TagUtils.kt             # 工具方法（非Composable）
├── TagCompose.kt           # 核心组件（TagGroup）
├── TagConvenience.kt       # 便捷方法（Composable）
├── AppTag.kt               # 库初始化配置
├── components/             # 标签组件实现
└── imageloader/            # 图片加载相关
```

## 🎯 API设计原则

### 1. **职责分离**
- **TagUtils** - 纯工具函数，不包含Composable
- **TagConvenience** - 便捷Composable函数
- **TagCompose** - 核心组件实现

### 2. **命名规范**
- **函数式API**: `ShowRectStart()`, `ShowRoundEnd()`
- **扩展函数API**: `tags.showRectStart()`, `tags.showRoundEnd()`
- **核心组件**: `TagGroup()`

## 📋 API分类详解

### 1. 核心组件 (TagCompose.kt)

```kotlin
@Composable
fun TagGroup(
    tags: List<TagBean>,
    text: String = "",
    showTagsAtStart: Boolean = true,
    onTagClick: ((TagBean) -> Unit)? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip,
    forceTagHeight: Boolean = false,
    // ... 其他参数
)
```

**用途**: 最灵活的标签显示组件，支持所有功能

### 2. 便捷方法 (TagConvenience.kt)

#### 函数式API
```kotlin
@Composable fun ShowRectStart(tags, content, ...)
@Composable fun ShowRectEnd(tags, content, ...)
@Composable fun ShowRoundStart(tags, content, ...)
@Composable fun ShowRoundEnd(tags, content, ...)
@Composable fun ShowTag(tags, content, ...)
```

#### 扩展函数API
```kotlin
@Composable fun List<TagBean>.showRectStart(content, ...)
@Composable fun List<TagBean>.showRectEnd(content, ...)
@Composable fun List<TagBean>.showRoundStart(content, ...)
@Composable fun List<TagBean>.showRoundEnd(content, ...)
```

**用途**: 对应原生Android API，提供简化的调用方式

### 3. 工具方法 (TagUtils.kt)

```kotlin
object TagUtils {
    // 颜色处理
    fun parseColor(colorStr: String, defaultColor: Color): Color
    fun parseEndColor(bgColorEnd: String?, defaultColor: Color): Color?
    fun isDarkColor(color: Color): Boolean
    fun getContrastColor(backgroundColor: Color): Color
    
    // 数据处理
    fun validateTagBean(tagBean: TagBean): Boolean
    fun processTagList(tags: List<TagBean>): List<TagBean>
    fun getDefaultAppearanceForType(type: TagType): TagAppearance
    
    // 文字测量
    fun measureTextWidth(text: String, textSize: Float): Float
    fun getTextHeight(textSize: Float): Float
    fun adjustBaseLine(originalY: Float, containerHeight: Float, textHeight: Float): Float
    
    // 样式管理
    fun setDefaultAppearance(appearance: TagAppearance)
    fun getDefaultAppearance(): TagAppearance
    
    // 调试功能
    var isDebugMode: Boolean
    fun debugValidateTagConfiguration(tags: List<TagBean>, text: String)
}
```

**用途**: 纯工具函数，不涉及UI组件

### 4. 初始化配置 (AppTag.kt)

```kotlin
object AppTag {
    fun init(imageLoader: ComposeImageLoader? = null)
    fun ShowTags(tags: List<GoodsTag>, content: String, ...)
}
```

**用途**: 库的初始化和兼容性API

## 🔄 使用方式对比

### 原生Android方式
```java
// 分两步：设置TextView + 调用标签库
TextView textView = findViewById(R.id.textView);
textView.setMaxLines(1);
textView.setEllipsize(TextUtils.TruncateAt.END);
TagUtils.showRectStart(context, textView, tags, "商品名称");
```

### Compose方式 - 核心组件
```kotlin
TagGroup(
    tags = tags,
    text = "商品名称",
    showTagsAtStart = true,
    maxLines = 1,
    overflow = TextOverflow.Ellipsis
)
```

### Compose方式 - 便捷方法
```kotlin
// 函数式API
ShowRectStart(
    tags = tags,
    content = "商品名称",
    maxLines = 1,
    overflow = TextOverflow.Ellipsis
)

// 扩展函数API（最简洁）
tags.showRectStart(
    content = "商品名称",
    maxLines = 1,
    overflow = TextOverflow.Ellipsis
)
```

## 🎨 API选择指南

### 何时使用TagGroup？
- ✅ 需要完全自定义配置
- ✅ 复杂的布局需求
- ✅ 特殊的交互逻辑

### 何时使用便捷方法？
- ✅ 从原生Android迁移
- ✅ 简单快速的实现
- ✅ 标准的使用场景

### 何时使用扩展函数？
- ✅ 追求代码简洁性
- ✅ 函数式编程风格
- ✅ 链式调用场景

## 📊 API复杂度对比

| API类型 | 代码行数 | 学习成本 | 灵活性 | 推荐场景 |
|---------|----------|----------|--------|----------|
| TagGroup | 5-10行 | 中等 | 最高 | 复杂需求 |
| 便捷方法 | 3-5行 | 低 | 中等 | 标准需求 |
| 扩展函数 | 1-3行 | 最低 | 中等 | 简单需求 |

## 🔧 最佳实践

### 1. 导入建议
```kotlin
// 推荐的导入方式
import com.taglib.*  // 导入所有核心API

// 或者按需导入
import com.taglib.TagGroup
import com.taglib.ShowRectStart
import com.taglib.showRectStart  // 扩展函数
```

### 2. 使用建议
```kotlin
// ✅ 推荐：根据场景选择合适的API
@Composable
fun ProductCard(product: Product) {
    when {
        // 简单场景：使用扩展函数
        product.tags.size <= 2 -> {
            product.tags.showRectStart(product.name)
        }
        
        // 标准场景：使用便捷方法
        product.isStandardLayout -> {
            ShowRectStart(
                tags = product.tags,
                content = product.name,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
        
        // 复杂场景：使用核心组件
        else -> {
            TagGroup(
                tags = product.tags,
                text = product.name,
                showTagsAtStart = product.showTagsFirst,
                onTagClick = { tag -> handleTagClick(tag) },
                maxLines = if (product.isListView) 1 else 3,
                overflow = TextOverflow.Ellipsis,
                forceTagHeight = product.needsUniformHeight
            )
        }
    }
}
```

### 3. 性能建议
```kotlin
// ✅ 缓存标签列表
val tags = remember(product.id) { 
    createTagsForProduct(product) 
}

// ✅ 使用合适的key
LazyColumn {
    items(products, key = { it.id }) { product ->
        tags.showRectStart(product.name)
    }
}
```

## 🎯 总结

新的API结构具有以下优势：

1. **职责清晰** - 工具函数与UI组件分离
2. **使用灵活** - 提供多种调用方式
3. **迁移友好** - 完全兼容原生API
4. **代码简洁** - 扩展函数提供最简调用
5. **类型安全** - 编译时检查，减少错误

选择合适的API可以让你的代码更加清晰、简洁和易维护！🚀
