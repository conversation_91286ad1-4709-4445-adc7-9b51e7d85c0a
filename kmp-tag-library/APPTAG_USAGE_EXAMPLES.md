# 🎯 AppTag 使用示例

## 📋 概述

`AppTag` 是KMP标签库的核心入口类，提供了两个主要方法：
- `AppTag.showTag()` - 使用 `TagBean` 数据结构
- `AppTag.ShowTags()` - 使用 `GoodsTag` 数据结构（原生Android格式）

## 🚀 AppTag.showTag() 使用示例

### 1. 基础用法

```kotlin
@Composable
fun BasicAppTagExample() {
    val tags = listOf(
        TagBean(
            type = TagType.FILL,
            text = "新品",
            backgroundColor = Color.Red,
            textColor = Color.White
        ),
        TagBean(
            type = TagType.STROKE,
            text = "包邮",
            borderColor = Color.Blue,
            textColor = Color.Blue
        )
    )
    
    AppTag.showTag(
        tags = tags,
        text = "商品名称",
        showTagsAtStart = true
    )
}
```

### 2. 高级配置

```kotlin
@Composable
fun AdvancedAppTagExample() {
    val tags = listOf(
        TagBean(
            type = TagType.FILL,
            text = "限时特价",
            backgroundColor = Color(0xFFFF5722),
            textColor = Color.White,
            isClickable = true,
            clickToast = "点击查看特价详情"
        ),
        TagBean(
            type = TagType.POINTS,
            text = "赚积分",
            imageUrl = "points_icon",
            backgroundColor = Color(0xFF4CAF50),
            textColor = Color.White,
            isClickable = true
        )
    )
    
    AppTag.showTag(
        tags = tags,
        text = "高端商品名称",
        showTagsAtStart = false, // 标签在后面
        textStyle = MaterialTheme.typography.bodyLarge.copy(
            fontWeight = FontWeight.Medium
        ),
        maxLines = 1,
        overflow = TextOverflow.Ellipsis,
        forceTagHeight = true,
        onTagClick = { tag ->
            println("AppTag点击: ${tag.text}")
            tag.clickToast?.let { toast ->
                // 显示Toast或其他提示
                println("Toast: $toast")
            }
        }
    )
}
```

### 3. 自定义样式

```kotlin
@Composable
fun CustomStyleAppTagExample() {
    val customTags = listOf(
        TagBean(
            type = TagType.FILL_AND_STROKE,
            text = "自定义样式",
            backgroundColor = Color(0xFFE1BEE7),
            borderColor = Color(0xFF9C27B0),
            textColor = Color(0xFF4A148C),
            appearance = TagAppearance(
                tagHeight = 28.dp,
                textSize = 13.sp,
                cornerRadius = 14.dp,
                horizontalPadding = 14.dp,
                borderWidth = 1.5.dp
            )
        )
    )
    
    AppTag.showTag(
        tags = customTags,
        text = "自定义样式商品",
        textStyle = MaterialTheme.typography.bodyLarge.copy(
            fontWeight = FontWeight.Medium,
            color = Color(0xFF1976D2)
        ),
        forceTagHeight = true
    )
}
```

### 4. 多标签组合

```kotlin
@Composable
fun MultiTagAppTagExample() {
    val multiTags = listOf(
        TagBean(type = TagType.FILL, text = "热销", backgroundColor = Color(0xFFFF9800), textColor = Color.White),
        TagBean(type = TagType.STROKE, text = "包邮", borderColor = Color(0xFF4CAF50), textColor = Color(0xFF4CAF50)),
        TagBean(type = TagType.DISCOUNT, text = "8折", backgroundColor = Color(0xFFF44336), textColor = Color.White),
        TagBean(type = TagType.POINTS, text = "送积分", imageUrl = "points_icon", backgroundColor = Color(0xFF9C27B0), textColor = Color.White)
    )
    
    AppTag.showTag(
        tags = multiTags,
        text = "多标签组合商品展示",
        showTagsAtStart = true,
        onTagClick = { tag ->
            when (tag.type) {
                TagType.DISCOUNT -> {
                    // 处理折扣标签点击
                    println("查看折扣详情")
                }
                TagType.POINTS -> {
                    // 处理积分标签点击
                    println("查看积分规则")
                }
                else -> {
                    println("标签点击: ${tag.text}")
                }
            }
        }
    )
}
```

## 🏷️ AppTag.ShowTags() 使用示例

### 1. 基础用法（原生数据格式）

```kotlin
@Composable
fun BasicShowTagsExample() {
    val goodsTags = listOf(
        GoodsTag(
            form = 1, // FILL
            name = "新品",
            color = "#FFFFFF",
            bgcolor = "#E91E63"
        ),
        GoodsTag(
            form = 3, // STROKE
            name = "包邮",
            color = "#4CAF50",
            bordercolor = "#4CAF50"
        )
    )
    
    AppTag.ShowTags(
        tags = goodsTags,
        content = "商品名称",
        showTagsAtStart = true
    )
}
```

### 2. 完整配置示例

```kotlin
@Composable
fun CompleteShowTagsExample() {
    val goodsTags = listOf(
        GoodsTag(
            form = 1, // FILL
            name = "新品",
            color = "#FFFFFF",
            bgcolor = "#E91E63"
        ),
        GoodsTag(
            form = 3, // STROKE
            name = "包邮",
            color = "#4CAF50",
            bordercolor = "#4CAF50"
        ),
        GoodsTag(
            form = 4, // DISCOUNT
            name = "限时8折",
            color = "#FFFFFF",
            bgcolor = "#FF5722",
            bgGraduallyColor = "#FF9800" // 渐变色
        ),
        GoodsTag(
            form = 5, // POINTS
            name = "赚积分",
            color = "#FFFFFF",
            bgcolor = "#9C27B0",
            rlink = "points_icon" // 图片链接
        )
    )
    
    AppTag.ShowTags(
        tags = goodsTags,
        content = "使用GoodsTag数据结构的商品",
        showTagsAtStart = true,
        onTagClick = { tag ->
            println("GoodsTag点击: ${tag.text} (${tag.type})")
        }
    )
}
```

### 3. 数据转换示例

```kotlin
// 从服务器API获取的数据格式
data class ApiTagData(
    val type: Int,
    val title: String,
    val textColor: String?,
    val bgColor: String?,
    val borderColor: String?,
    val imageUrl: String?
)

@Composable
fun ApiDataExample(apiTags: List<ApiTagData>) {
    // 转换为GoodsTag格式
    val goodsTags = apiTags.map { apiTag ->
        GoodsTag(
            form = apiTag.type,
            name = apiTag.title,
            color = apiTag.textColor,
            bgcolor = apiTag.bgColor,
            bordercolor = apiTag.borderColor,
            rlink = apiTag.imageUrl
        )
    }
    
    AppTag.ShowTags(
        tags = goodsTags,
        content = "API数据商品",
        showTagsAtStart = true,
        onTagClick = { tag ->
            println("API标签点击: ${tag.text}")
        }
    )
}
```

## 🔄 方法对比

| 特性 | AppTag.showTag() | AppTag.ShowTags() |
|------|------------------|-------------------|
| **数据格式** | TagBean | GoodsTag |
| **类型安全** | 强类型 | 弱类型（Int形式） |
| **参数丰富度** | 完整参数 | 基础参数 |
| **样式控制** | 完全控制 | 基础控制 |
| **适用场景** | 新项目，完全控制 | 迁移项目，兼容原生 |
| **推荐程度** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

## 🎯 使用建议

### 选择AppTag.showTag()的情况：
- ✅ 新项目开发
- ✅ 需要完整的样式控制
- ✅ 需要强类型安全
- ✅ 需要高级功能（如文字溢出处理）

### 选择AppTag.ShowTags()的情况：
- ✅ 从原生Android迁移
- ✅ 已有GoodsTag数据格式
- ✅ 简单的标签显示需求
- ✅ 快速兼容现有API

## 💡 最佳实践

### 1. 初始化
```kotlin
// 在Application中初始化
AppTag.init(
    loader = MyImageLoader(),
    debug = BuildConfig.DEBUG
)
```

### 2. 错误处理
```kotlin
@Composable
fun SafeAppTagUsage() {
    try {
        AppTag.showTag(
            tags = tags,
            text = "商品名称"
        )
    } catch (e: Exception) {
        // 降级显示
        Text("商品名称")
    }
}
```

### 3. 性能优化
```kotlin
@Composable
fun OptimizedAppTagUsage(product: Product) {
    // 使用remember缓存标签列表
    val tags = remember(product.id, product.lastModified) {
        product.generateTags()
    }
    
    AppTag.showTag(
        tags = tags,
        text = product.name
    )
}
```

### 4. 主题适配
```kotlin
@Composable
fun ThemedAppTagUsage() {
    val isDarkTheme = isSystemInDarkTheme()
    
    val themedTags = remember(tags, isDarkTheme) {
        tags.map { tag ->
            tag.copy(
                textColor = if (isDarkTheme) Color.White else tag.textColor,
                backgroundColor = if (isDarkTheme) {
                    tag.backgroundColor.copy(alpha = 0.8f)
                } else {
                    tag.backgroundColor
                }
            )
        }
    }
    
    AppTag.showTag(
        tags = themedTags,
        text = "商品名称"
    )
}
```

## 🔗 相关链接

- [完整Demo](demo/TagLibraryDemo.kt) - 查看AppTag的完整使用示例
- [快速参考](QUICK_REFERENCE.md) - AppTag API速查
- [使用指南](KMP_TAG_LIBRARY_USAGE_GUIDE.md) - 详细使用说明
