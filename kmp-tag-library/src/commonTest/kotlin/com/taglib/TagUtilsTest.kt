package com.taglib

import androidx.compose.ui.graphics.Color
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class TagUtilsTest {
    
    @Test
    fun testParseColor() {
        // 测试正常的颜色解析
        val red = TagUtils.parseColor("#FF0000")
        assertEquals(Color.Red, red)
        
        val blue = TagUtils.parseColor("#0000FF")
        assertEquals(Color.Blue, blue)
        
        // 测试带透明度的颜色
        val transparentRed = TagUtils.parseColor("#80FF0000")
        assertEquals(0.5f, transparentRed.alpha, 0.01f)
        assertEquals(1f, transparentRed.red, 0.01f)
        
        // 测试无效颜色返回默认值
        val defaultColor = TagUtils.parseColor("invalid", Color.Green)
        assertEquals(Color.Green, defaultColor)
        
        // 测试空字符串
        val emptyColor = TagUtils.parseColor("", Color.Yellow)
        assertEquals(Color.Yellow, emptyColor)
    }
    
    @Test
    fun testIsDarkColor() {
        assertTrue(TagUtils.isDarkColor(Color.Black))
        assertTrue(TagUtils.isDarkColor(Color.Blue))
        assertFalse(TagUtils.isDarkColor(Color.White))
        assertFalse(TagUtils.isDarkColor(Color.Yellow))
    }
    
    @Test
    fun testGetContrastColor() {
        assertEquals(Color.White, TagUtils.getContrastColor(Color.Black))
        assertEquals(Color.Black, TagUtils.getContrastColor(Color.White))
    }
    
    @Test
    fun testValidateTagBean() {
        // 测试有效的填充标签
        val validFillTag = TagBean(
            type = TagType.FILL,
            text = "测试标签"
        )
        assertTrue(TagUtils.validateTagBean(validFillTag))
        
        // 测试无效的填充标签（空文字）
        val invalidFillTag = TagBean(
            type = TagType.FILL,
            text = ""
        )
        assertFalse(TagUtils.validateTagBean(invalidFillTag))
        
        // 测试有效的图片标签
        val validImageTag = TagBean(
            type = TagType.IMAGE,
            imageUrl = "https://example.com/image.png"
        )
        assertTrue(TagUtils.validateTagBean(validImageTag))
        
        // 测试无效的图片标签（空URL）
        val invalidImageTag = TagBean(
            type = TagType.IMAGE,
            imageUrl = ""
        )
        assertFalse(TagUtils.validateTagBean(invalidImageTag))
    }
    
    @Test
    fun testProcessTagList() {
        val tags = listOf(
            TagBean(type = TagType.FILL, text = "标签1"),
            TagBean(type = TagType.STROKE, text = "标签2"),
            TagBean(type = TagType.FILL, text = "标签3")
        )
        
        val processedTags = TagUtils.processTagList(tags)
        
        assertEquals(3, processedTags.size)
        assertEquals(0, processedTags[0].tagIndex)
        assertEquals(1, processedTags[1].tagIndex)
        assertEquals(2, processedTags[2].tagIndex)
        
        processedTags.forEach { tag ->
            assertEquals(3, tag.tagCount)
        }
        
        assertTrue(processedTags[0].isFirstTag())
        assertFalse(processedTags[1].isFirstTag())
        assertFalse(processedTags[2].isFirstTag())
        
        assertFalse(processedTags[0].isLastTag())
        assertFalse(processedTags[1].isLastTag())
        assertTrue(processedTags[2].isLastTag())
    }
    
    @Test
    fun testTagBeanGradient() {
        val gradientTag = TagBean(
            type = TagType.FILL,
            text = "渐变标签",
            backgroundColor = Color.Red,
            backgroundEndColor = Color.Yellow
        )
        
        assertTrue(gradientTag.hasGradientBackground())
        
        val solidTag = TagBean(
            type = TagType.FILL,
            text = "纯色标签",
            backgroundColor = Color.Red
        )
        
        assertFalse(solidTag.hasGradientBackground())
    }
}
