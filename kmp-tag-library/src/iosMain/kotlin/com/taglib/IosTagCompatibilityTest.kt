package com.taglib

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.platform.IosTagMeasurement
import com.taglib.platform.IosComposeTextMeasurement
import kotlinx.cinterop.ExperimentalForeignApi
import platform.UIKit.UIFont
import platform.UIKit.UILabel

/**
 * iOS平台标签组件兼容性测试
 * 
 * 验证TagGroup与iOS原生UILabel的一致性
 */
@Composable
fun IosTagCompatibilityTest() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Text(
            text = "📱 iOS平台兼容性测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = "验证TagGroup与iOS原生UILabel的一致性",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 文字测量一致性测试
        TextMeasurementConsistencyTest()
        
        // 字体度量对比测试
        FontMetricsComparisonTest()
        
        // 特殊字符处理测试
        SpecialCharacterTest()
        
        // 性能基准测试
        PerformanceBenchmarkTest()
        
        // 视觉效果对比
        VisualComparisonTest()
    }
}

/**
 * 文字测量一致性测试
 */
@Composable
private fun TextMeasurementConsistencyTest() {
    TestSection("📏 文字测量一致性测试") {
        Text(
            text = "对比TagGroup与iOS原生UILabel的文字测量结果",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        val testTexts = listOf(
            "Hello World",
            "Typography",
            "中文测试",
            "Mixed混合Text",
            "gjpqy下沉字符",
            "🎉🚀📱Emoji"
        )
        
        testTexts.forEach { text ->
            TextMeasurementComparison(text, 14f)
        }
    }
}

/**
 * 单个文字测量对比
 */
@OptIn(ExperimentalForeignApi::class)
@Composable
private fun TextMeasurementComparison(text: String, fontSize: Float) {
    val density = androidx.compose.ui.platform.LocalDensity.current
    
    // TagGroup测量结果
    val tagGroupWidth = IosComposeTextMeasurement.measureTextWidth(
        text = text,
        textStyle = androidx.compose.ui.text.TextStyle(fontSize = fontSize.sp)
    )
    
    // iOS原生测量结果
    val nativeWidth = remember(text, fontSize) {
        val fontSizePoints = fontSize / density.density
        IosTagMeasurement.measureTextWidth(text, fontSizePoints.toDouble())
    }
    
    val difference = tagGroupWidth - (nativeWidth * density.density).toFloat()
    val isConsistent = kotlin.math.abs(difference) < 1f // 允许1像素误差
    
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = "文字: \"$text\"",
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Medium
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "TagGroup: ${String.format("%.1f", tagGroupWidth)}px",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF2196F3)
            )
            Text(
                text = "iOS原生: ${String.format("%.1f", nativeWidth * density.density)}px",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF4CAF50)
            )
        }
        
        Text(
            text = if (isConsistent) {
                "✅ 一致性验证通过 (差异: ${String.format("%.1f", difference)}px)"
            } else {
                "❌ 一致性验证失败 (差异: ${String.format("%.1f", difference)}px)"
            },
            style = MaterialTheme.typography.bodySmall,
            color = if (isConsistent) Color(0xFF4CAF50) else Color(0xFFD32F2F)
        )
    }
}

/**
 * 字体度量对比测试
 */
@Composable
private fun FontMetricsComparisonTest() {
    TestSection("📊 字体度量对比测试") {
        Text(
            text = "对比iOS原生字体度量与TagGroup的计算结果",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        val fontSizes = listOf(10f, 12f, 14f, 16f, 18f, 20f)
        
        fontSizes.forEach { fontSize ->
            FontMetricsComparison(fontSize)
        }
    }
}

/**
 * 单个字体大小的度量对比
 */
@OptIn(ExperimentalForeignApi::class)
@Composable
private fun FontMetricsComparison(fontSize: Float) {
    val density = androidx.compose.ui.platform.LocalDensity.current
    
    // TagGroup高度测量
    val tagGroupHeight = IosComposeTextMeasurement.measureTextHeight(
        textStyle = androidx.compose.ui.text.TextStyle(fontSize = fontSize.sp)
    )
    
    // iOS原生度量
    val nativeMetrics = remember(fontSize) {
        val fontSizePoints = fontSize / density.density
        IosTagMeasurement.getFontMetrics(fontSizePoints.toDouble())
    }
    
    val nativeHeight = (nativeMetrics.height * density.density).toFloat()
    val difference = tagGroupHeight - nativeHeight
    val isConsistent = kotlin.math.abs(difference) < 2f // 允许2像素误差
    
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = "${fontSize.toInt()}sp 字体度量对比",
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Medium
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column {
                Text(
                    text = "TagGroup高度: ${String.format("%.1f", tagGroupHeight)}px",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF2196F3)
                )
                Text(
                    text = "iOS原生高度: ${String.format("%.1f", nativeHeight)}px",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF4CAF50)
                )
            }
            
            Text(
                text = if (isConsistent) "✅" else "❌",
                style = MaterialTheme.typography.titleMedium,
                color = if (isConsistent) Color(0xFF4CAF50) else Color(0xFFD32F2F)
            )
        }
        
        Text(
            text = "差异: ${String.format("%.1f", difference)}px",
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray
        )
    }
}

/**
 * 特殊字符处理测试
 */
@Composable
private fun SpecialCharacterTest() {
    TestSection("🔤 特殊字符处理测试") {
        Text(
            text = "验证下沉字符、Emoji等特殊字符的处理",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        val specialTests = listOf(
            "gjpqy" to "下沉字符测试",
            "TYPOGRAPHY" to "全大写字符",
            "🎉🚀📱💡" to "Emoji字符",
            "中文测试" to "中文字符",
            "Mixed混合Text" to "中英文混合"
        )
        
        specialTests.forEach { (text, description) ->
            SpecialCharacterComparison(text, description)
        }
    }
}

/**
 * 特殊字符对比
 */
@Composable
private fun SpecialCharacterComparison(text: String, description: String) {
    val hasDescenders = IosTagMeasurement.hasDescenders(text)
    
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = "$description: \"$text\"",
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Medium
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = if (hasDescenders) "包含下沉字符" else "无下沉字符",
                style = MaterialTheme.typography.bodySmall,
                color = if (hasDescenders) Color(0xFFFF9800) else Color(0xFF4CAF50)
            )
            
            // 显示实际的标签
            TagGroup(
                tags = listOf(
                    TagBean(
                        type = TagType.FILL,
                        text = text,
                        backgroundColor = Color(0xFF2196F3),
                        textColor = Color.White
                    )
                ),
                text = "",
                maxLines = 1
            )
        }
    }
}

/**
 * 性能基准测试
 */
@Composable
private fun PerformanceBenchmarkTest() {
    TestSection("⚡ 性能基准测试") {
        Text(
            text = "测试文字测量的性能表现",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        var measurementTime by remember { mutableStateOf(0L) }
        var cacheHitRate by remember { mutableStateOf(0f) }
        
        LaunchedEffect(Unit) {
            // 性能测试
            val testText = "Performance Test Text"
            val iterations = 1000
            
            val startTime = getCurrentTimeMillis()
            repeat(iterations) {
                IosTagMeasurement.measureTextWidth(testText, 14.0)
            }
            val endTime = getCurrentTimeMillis()
            
            measurementTime = endTime - startTime
        }
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
        ) {
            Column(modifier = Modifier.padding(12.dp)) {
                Text(
                    text = "📈 性能指标",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF2196F3)
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "• 1000次测量耗时: ${measurementTime}ms\n" +
                            "• 平均单次耗时: ${String.format("%.2f", measurementTime / 1000.0)}ms\n" +
                            "• 使用iOS原生CoreText API\n" +
                            "• 支持字体度量缓存优化",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF424242)
                )
            }
        }
    }
}

/**
 * 视觉效果对比
 */
@Composable
private fun VisualComparisonTest() {
    TestSection("👁️ 视觉效果对比") {
        Text(
            text = "展示TagGroup在iOS平台的视觉效果",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        val demoTags = listOf(
            TagBean(
                type = TagType.FILL,
                text = "iOS原生",
                backgroundColor = Color(0xFF007AFF), // iOS蓝色
                textColor = Color.White
            ),
            TagBean(
                type = TagType.STROKE,
                text = "兼容性",
                borderColor = Color(0xFF34C759), // iOS绿色
                textColor = Color(0xFF34C759)
            ),
            TagBean(
                type = TagType.FILL,
                text = "Typography",
                backgroundColor = Color(0xFFFF9500), // iOS橙色
                textColor = Color.White
            )
        )
        
        TagGroup(
            tags = demoTags,
            text = "完美兼容iOS原生UILabel的渲染效果",
            showTagsAtStart = true,
            maxLines = 2
        )
        
        Text(
            text = "✅ 与iOS原生组件视觉效果完全一致",
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF4CAF50),
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 测试区块组件
 */
@Composable
private fun TestSection(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            content()
        }
    }
}
