package com.taglib.platform

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalContext
// import coil.compose.AsyncImagePainter
// import coil.compose.rememberAsyncImagePainter
// import coil.request.ImageRequest

/**
 * Android平台特定的标签工具类
 */
object AndroidTagUtils {
    
    /**
     * 从Bitmap创建Painter
     */
    fun bitmapToPainter(bitmap: Bitmap): Painter {
        return BitmapPainter(bitmap.asImageBitmap())
    }
    
    /**
     * 从资源ID创建Painter
     * TODO: 实现资源到Painter的转换
     */
    @Composable
    fun resourceToPainter(resourceId: Int): Painter? {
        // val context = LocalContext.current
        // return rememberAsyncImagePainter(
        //     ImageRequest.Builder(context)
        //         .data(resourceId)
        //         .build()
        // )
        return null
    }

    /**
     * 从网络URL创建Painter
     * TODO: 实现网络图片加载
     */
    @Composable
    fun urlToPainter(
        url: String,
        placeholder: Painter? = null,
        error: Painter? = null
    ): Painter? {
        // val context = LocalContext.current
        // return rememberAsyncImagePainter(
        //     ImageRequest.Builder(context)
        //         .data(url)
        //         .apply {
        //             if (placeholder != null) {
        //                 placeholder(placeholder)
        //             }
        //             if (error != null) {
        //                 error(error)
        //             }
        //         }
        //         .build()
        // )
        return null
    }
    
    /**
     * 获取屏幕密度
     */
    fun getScreenDensity(context: Context): Float {
        return context.resources.displayMetrics.density
    }
    
    /**
     * dp转px
     */
    fun dpToPx(context: Context, dp: Float): Float {
        return dp * getScreenDensity(context)
    }
    
    /**
     * px转dp
     */
    fun pxToDp(context: Context, px: Float): Float {
        return px / getScreenDensity(context)
    }
}
