package com.taglib.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.taglib.TagBean
import com.taglib.TagType

/**
 * 填充背景标签组件
 * 支持纯色和渐变背景
 */
@Composable
fun FillTag(
    tagBean: TagBean,
    onClick: ((TagBean) -> Unit)? = null,
    arrowIcon: ImageVector? = null,
    modifier: Modifier = Modifier
) {
    require(tagBean.type == TagType.FILL || tagBean.type == TagType.FILL_AND_STROKE) {
        "FillTag only supports FILL and FILL_AND_STROKE types"
    }
    
    val appearance = tagBean.appearance
    
    // 创建背景修饰符
    val backgroundModifier = if (tagBean.hasGradientBackground()) {
        // 渐变背景 - 使用处理后的结束颜色
        val endColor = tagBean.getProcessedBackgroundEndColor() ?: tagBean.backgroundEndColor!!
        val gradient = Brush.horizontalGradient(
            colors = listOf(tagBean.backgroundColor, endColor)
        )
        Modifier.background(gradient, appearance.shape)
    } else {
        // 纯色背景
        Modifier.background(tagBean.backgroundColor, appearance.shape)
    }
    
    // 边框修饰符（用于FILL_AND_STROKE类型）
    val borderModifier = if (tagBean.type == TagType.FILL_AND_STROKE) {
        Modifier.border(
            width = appearance.borderWidth,
            color = tagBean.borderColor,
            shape = appearance.shape
        )
    } else {
        Modifier
    }
    
    // 点击修饰符
    val clickModifier = if (tagBean.isClickable && onClick != null) {
        Modifier.clickable { onClick(tagBean) }
    } else {
        Modifier
    }

    // 高度修饰符 - 实现原生版本的固定高度逻辑
    val heightModifier = if (tagBean.useFixedHeight && appearance.tagHeight.value > 0) {
        Modifier.height(appearance.tagHeight)
    } else {
        Modifier
    }

    Row(
        modifier = modifier
            .then(heightModifier)
            .then(backgroundModifier)
            .then(borderModifier)
            .then(clickModifier)
            .clip(appearance.shape)
            .padding(
                horizontal = appearance.horizontalPadding,
                vertical = appearance.verticalPadding
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // 标签文字 - 使用优化后的文字大小
        Text(
            text = tagBean.text,
            color = tagBean.textColor,
            fontSize = tagBean.getOptimizedTextSize(),
            fontWeight = appearance.fontWeight,
            textAlign = TextAlign.Center,
            maxLines = 1
        )
        
        // 可点击箭头
        if (tagBean.isClickable && arrowIcon != null) {
            Spacer(modifier = Modifier.width(appearance.arrowSpacing))
            Icon(
                imageVector = arrowIcon,
                contentDescription = "Click arrow",
                tint = tagBean.textColor,
                modifier = Modifier.size(appearance.arrowWidth)
            )
        }
    }
}
