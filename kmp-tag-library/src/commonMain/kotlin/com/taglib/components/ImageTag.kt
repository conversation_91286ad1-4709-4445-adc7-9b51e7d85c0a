package com.taglib.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.BrokenImage
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.taglib.TagBean
import com.taglib.TagType
import com.taglib.ImageLoaderManager
import com.taglib.loadValidatedImage

/**
 * 图片标签组件
 * 支持网络图片和本地图片
 * 对应原生库的FORM_IMAGE类型标签
 */
@Composable
fun ImageTag(
    tagBean: TagBean,
    imagePainter: Painter? = null,
    loadingContent: @Composable (() -> Unit)? = null,
    errorContent: @Composable (() -> Unit)? = null,
    onClick: ((TagBean) -> Unit)? = null,
    arrowIcon: ImageVector? = null,
    modifier: Modifier = Modifier
) {
    require(tagBean.type == TagType.IMAGE) {
        "ImageTag only supports IMAGE type"
    }
    
    val appearance = tagBean.appearance
    
    // 点击修饰符
    val clickModifier = if (tagBean.isClickable && onClick != null) {
        Modifier.clickable { onClick(tagBean) }
    } else {
        Modifier
    }
    
    Row(
        modifier = modifier
            .then(clickModifier)
            .clip(appearance.shape),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // 图片内容 - 使用验证加载机制
        Box(
            modifier = Modifier
                .height(appearance.textSize.value.dp * appearance.imageHeightRatio)
                .aspectRatio(1f)
                .clip(appearance.shape),
            contentAlignment = Alignment.Center
        ) {
            // 使用验证图片加载，通过ImageLoaderManager获取全局imageLoader
            val validatedPainter = tagBean.loadValidatedImage(
                imageLoader = ImageLoaderManager.getComposeImageLoader(),
                placeholder = imagePainter,
                error = imagePainter
            )

            when {
                validatedPainter != null -> {
                    Image(
                        painter = validatedPainter,
                        contentDescription = tagBean.text.ifBlank { "Tag image" },
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop
                    )
                }
                imagePainter != null -> {
                    // 回退到传入的painter
                    Image(
                        painter = imagePainter,
                        contentDescription = tagBean.text.ifBlank { "Tag image" },
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop
                    )
                }
                loadingContent != null -> {
                    loadingContent()
                }
                errorContent != null -> {
                    errorContent()
                }
                else -> {
                    // 默认加载指示器
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
        
        // 可点击箭头
        if (tagBean.isClickable && arrowIcon != null) {
            Spacer(modifier = Modifier.width(appearance.arrowSpacing))
            Icon(
                imageVector = arrowIcon,
                contentDescription = "Click arrow",
                tint = tagBean.textColor,
                modifier = Modifier.size(appearance.arrowWidth)
            )
        }
    }
}

/**
 * 默认加载中内容
 */
@Composable
fun DefaultLoadingContent() {
    CircularProgressIndicator(
        modifier = Modifier.size(16.dp),
        strokeWidth = 2.dp,
        color = MaterialTheme.colorScheme.primary
    )
}

/**
 * 默认错误内容
 */
@Composable
fun DefaultErrorContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .clip(RoundedCornerShape(4.dp)),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = Icons.Default.BrokenImage,
            contentDescription = "Image load error",
            tint = Color.Gray,
            modifier = Modifier.size(16.dp)
        )
    }
}
