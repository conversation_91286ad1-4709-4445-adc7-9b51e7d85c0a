package com.taglib.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.BrokenImage
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.compose.ui.platform.LocalDensity
import com.taglib.TagBean
import com.taglib.TagType
import com.taglib.ImageLoaderManager
import com.taglib.TagUtils
import com.taglib.loadValidatedImage

/**
 * 图片标签组件
 * 完全模拟原生库的FORM_IMAGE类型标签
 * 图片加载通过ImageLoaderManager处理，与原生保持一致
 */
@Composable
fun ImageTag(
    tagBean: TagBean,
    onClick: ((TagBean) -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    require(tagBean.type == TagType.IMAGE) {
        "ImageTag only supports IMAGE type"
    }
    
    val appearance = tagBean.appearance
    
    // 点击修饰符
    val clickModifier = if (tagBean.isClickable && onClick != null) {
        Modifier.clickable { onClick(tagBean) }
    } else {
        Modifier
    }
    
    Row(
        modifier = modifier
            .then(clickModifier)
            .clip(appearance.shape),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // 🎯 完全模拟原生：图片内容通过ImageLoaderManager的回调机制处理
        // 计算图片显示尺寸，与TagUtils.calculateImageTagWidth保持一致
        val imageSize = calculateImageDisplaySize(tagBean)

        Box(
            modifier = Modifier
                .size(imageSize.dp)
                .clip(appearance.shape),
            contentAlignment = Alignment.Center
        ) {
            // 🎯 使用状态管理图片加载
            var imagePainter by remember { mutableStateOf<Painter?>(null) }
            var isLoading by remember { mutableStateOf(true) }
            var hasError by remember { mutableStateOf(false) }

            // 🎯 模拟原生逻辑：通过回调机制加载图片
            LaunchedEffect(tagBean.imageUrl) {
                tagBean.imageUrl?.let { url ->
                    isLoading = true
                    hasError = false

                    ImageLoaderManager.loadImageCompose(
                        url = url,
                        onSuccess = { painter ->
                            imagePainter = painter
                            isLoading = false
                            hasError = false
                        },
                        onFailure = { failPainter ->
                            imagePainter = failPainter
                            isLoading = false
                            hasError = true
                        }
                    )
                } ?: run {
                    // 没有URL，直接设置为失败状态
                    isLoading = false
                    hasError = true
                }
            }

            when {
                isLoading -> {
                    // 加载中状态 - 显示空白（模拟原生）
                    Box(modifier = Modifier.fillMaxSize())
                }
                imagePainter != null -> {
                    // 加载成功 - 显示图片
                    Image(
                        painter = imagePainter!!,
                        contentDescription = tagBean.text.ifBlank { "Tag image" },
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop
                    )
                }
                else -> {
                    // 加载失败 - 显示空白（模拟原生的continue逻辑）
                    Box(modifier = Modifier.fillMaxSize())
                }
            }
        }
        
        // 🎯 图片标签通常不显示箭头，与原生保持一致
        // 原生TagImageSpan中没有箭头处理逻辑
    }
}

/**
 * 默认加载中内容
 */
@Composable
fun DefaultLoadingContent() {
    CircularProgressIndicator(
        modifier = Modifier.size(16.dp),
        strokeWidth = 2.dp,
        color = MaterialTheme.colorScheme.primary
    )
}

/**
 * 默认错误内容
 */
@Composable
fun DefaultErrorContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .clip(RoundedCornerShape(4.dp)),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = Icons.Default.BrokenImage,
            contentDescription = "Image load error",
            tint = Color.Gray,
            modifier = Modifier.size(16.dp)
        )
    }
}

/**
 * 计算图片显示尺寸
 * 完全模拟原生TagImageSpan中的图片尺寸计算逻辑
 *
 * @param tagBean 标签数据
 * @return 图片显示尺寸（dp值）
 */
@Composable
private fun calculateImageDisplaySize(tagBean: TagBean): Float {
    val appearance = tagBean.appearance
    val density = LocalDensity.current

    // 🎯 模拟原生：获取图片的原始尺寸
    val originalSize = 24f  // dp值，模拟原始图片尺寸（正方形）

    // 🎯 获取文字高度（px值）
    val textHeightPx = TagUtils.getTextHeight(appearance.textSize.value)

    return if (appearance.tagHeight.value > 0 && tagBean.useFixedHeight) {
        // 情况1：固定标签高度
        // 模拟原生：TagUtils.dpToPx(context, data.appearance.tagHeightDp)
        appearance.tagHeight.value
    } else {
        // 情况2：自适应高度
        // 模拟原生：data.appearance.imgHeightLimit * textHeight
        val targetHeightDp = appearance.imageHeightRatio * with(density) { textHeightPx.toDp().value }

        // 如果计算出的高度与原始尺寸不同，需要按比例缩放
        if (originalSize != targetHeightDp) {
            val scale = targetHeightDp / originalSize
            originalSize * scale
        } else {
            originalSize
        }
    }
}
