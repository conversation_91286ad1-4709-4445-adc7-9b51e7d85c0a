package com.taglib.components

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.TagBean
import com.taglib.TagType
import com.taglib.TagUtils

/**
 * 镂空边框标签组件
 * 只有边框，背景透明
 */
@Composable
fun StrokeTag(
    tagBean: TagBean,
    onClick: ((TagBean) -> Unit)? = null,
    arrowIcon: ImageVector? = null,
    modifier: Modifier = Modifier
) {
    require(tagBean.type == TagType.STROKE) {
        "StrokeTag only supports STROKE type"
    }
    
    val appearance = tagBean.appearance
    
    // 点击修饰符
    val clickModifier = if (tagBean.isClickable && onClick != null) {
        Modifier.clickable { onClick(tagBean) }
    } else {
        Modifier
    }

    // 🎯 统一的高度计算逻辑 - TagUtils已处理所有判断
    val calculatedHeight = TagUtils.calculateTagHeight(tagBean)

    // 高度修饰符 - 直接使用计算结果，无需重复判断
    val heightModifier = Modifier.height(calculatedHeight.value.dp)

    Row(
        modifier = modifier
            .then(heightModifier)
            .border(
                width = appearance.borderWidth,
                color = tagBean.borderColor,
                shape = appearance.shape
            )
            .then(clickModifier)
            .clip(appearance.shape)
            .padding(
                horizontal = appearance.horizontalPadding,
                vertical = appearance.verticalPadding
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // 标签文字 - 使用专用的TagText组件消除上下间距
        // 🎯 使用与原生库完全一致的文字大小计算逻辑
        val realTagTextSize = TagUtils.getTagTextSize(appearance, tagBean.useFixedHeight)
        TagText(
            text = tagBean.text,
            fontSize = realTagTextSize.sp,
            color = tagBean.textColor,
            fontWeight = appearance.fontWeight
        )
        
        // 可点击箭头
        if (tagBean.isClickable && arrowIcon != null) {
            Spacer(modifier = Modifier.width(appearance.arrowSpacing))
            Icon(
                imageVector = arrowIcon,
                contentDescription = "Click arrow",
                tint = tagBean.textColor,
                modifier = Modifier.size(appearance.arrowWidth)
            )
        }
    }
}


