package com.taglib.components

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.TagBean
import com.taglib.TagType
import com.taglib.TagUtils

/**
 * 镂空边框标签组件
 * 只有边框，背景透明
 */
@Composable
fun StrokeTag(
    tagBean: TagBean,
    onClick: ((TagBean) -> Unit)? = null,
    arrowIcon: ImageVector? = null,
    modifier: Modifier = Modifier
) {
    require(tagBean.type == TagType.STROKE) {
        "StrokeTag only supports STROKE type"
    }
    
    val appearance = tagBean.appearance
    
    // 点击修饰符
    val clickModifier = if (tagBean.isClickable && onClick != null) {
        Modifier.clickable { onClick(tagBean) }
    } else {
        Modifier
    }

    // 🎯 统一的高度计算逻辑 - 基于Android原生算法
    val calculatedHeight = calculateTagHeight(tagBean)

    // 高度修饰符 - 使用统一计算的高度
    val heightModifier = if (tagBean.useFixedHeight && appearance.tagHeight.value > 0) {
        // 固定高度模式：使用设定的高度
        Modifier.height(appearance.tagHeight)
    } else {
        // 自适应高度模式：使用计算出的高度
        Modifier.height(calculatedHeight.value.dp)
    }

    Row(
        modifier = modifier
            .then(heightModifier)
            .border(
                width = appearance.borderWidth,
                color = tagBean.borderColor,
                shape = appearance.shape
            )
            .then(clickModifier)
            .clip(appearance.shape)
            .padding(
                horizontal = appearance.horizontalPadding,
                vertical = appearance.verticalPadding
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // 标签文字 - 使用普通Text组件
        Text(
            text = tagBean.text,
            color = tagBean.textColor,
            fontSize = if (tagBean.useFixedHeight) appearance.fixedTextSize else appearance.textSize,
            fontWeight = appearance.fontWeight,
            textAlign = TextAlign.Center,
            maxLines = 1,
            modifier = Modifier.padding(vertical = 2.dp) // 添加垂直padding防止截断
        )
        
        // 可点击箭头
        if (tagBean.isClickable && arrowIcon != null) {
            Spacer(modifier = Modifier.width(appearance.arrowSpacing))
            Icon(
                imageVector = arrowIcon,
                contentDescription = "Click arrow",
                tint = tagBean.textColor,
                modifier = Modifier.size(appearance.arrowWidth)
            )
        }
    }
}

/**
 * 计算标签高度
 * 基于Android原生逻辑：frameHeight = tagFM.descent - tagFM.ascent + 2 * paddingV
 * 使用真实的字体度量，不再依赖经验值
 */
@Composable
private fun calculateTagHeight(tagBean: TagBean): androidx.compose.ui.unit.TextUnit {
    val appearance = tagBean.appearance
    val density = LocalDensity.current

    return if (appearance.tagHeight.value > 0) {
        // 使用设定的标签高度
        appearance.tagHeight.value.sp
    } else {
        // 🎯 使用真实字体度量计算精确高度
        val fontSize = if (tagBean.useFixedHeight) appearance.fixedTextSize else appearance.textSize
        val fontSizePx = with(density) { fontSize.toPx() }

        // 使用TagUtils的真实字体度量方法
        val textRealHeight = TagUtils.getTextHeight(fontSizePx)
        val totalHeightPx = textRealHeight + with(density) { appearance.verticalPadding.toPx() } * 2

        with(density) { totalHeightPx.toDp() }.value.sp
    }
}
