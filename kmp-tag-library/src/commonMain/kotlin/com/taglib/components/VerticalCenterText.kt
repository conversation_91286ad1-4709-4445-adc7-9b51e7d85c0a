package com.taglib.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.layout.Placeable
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import kotlin.math.max
import kotlin.math.roundToInt

/**
 * 垂直居中文字组件
 *
 * 精确模拟Android原生TagUtils.adjustBaseLine算法，解决文字下半部分被截断的问题。
 *
 * 原生算法分析：
 * 1. adjustBaseLine(y, rawFm, tagFm) 计算精确的基线位置
 * 2. offset1 = (rawFm.descent - rawFm.ascent) / 2F - rawFm.descent (外部文字视觉中心偏移)
 * 3. offset2 = -(tagFm.ascent + tagFm.descent) / 2F (标签文字视觉中心偏移)
 * 4. return y - offset1 + offset2 (调整后的基线位置)
 *
 * Compose实现：
 * 使用Canvas直接绘制文字，完全控制基线位置，确保与原生效果一致。
 *
 * <AUTHOR> Tag Library Team
 * @since 1.0.0
 */

/**
 * 字体度量数据类
 * 模拟Android Paint.FontMetrics
 */
data class FontMetrics(
    val ascent: Float,   // 基线上方距离(负值)
    val descent: Float,  // 基线下方距离(正值)
    val leading: Float   // 行间距
) {
    val height: Float get() = descent - ascent
}

/**
 * 根据字体大小计算字体度量
 * 模拟Android Paint.getFontMetrics()
 */
fun calculateFontMetrics(fontSizePx: Float): FontMetrics {
    // 基于Android字体度量的经验值
    return FontMetrics(
        ascent = fontSizePx * -0.8f,   // 通常是字体大小的80%，负值
        descent = fontSizePx * 0.2f,   // 通常是字体大小的20%，正值
        leading = fontSizePx * 0.1f    // 通常是字体大小的10%
    )
}

/**
 * 精确的基线调整算法
 * 完全模拟Android原生的TagUtils.adjustBaseLine方法
 */
fun adjustBaseLine(
    y: Float,
    rawFontMetrics: FontMetrics,  // 外部文字的字体度量
    tagFontMetrics: FontMetrics   // 标签文字的字体度量
): Float {
    // offset1: 外部文字的视觉中心相对于基线的偏移
    val offset1 = (rawFontMetrics.descent - rawFontMetrics.ascent) / 2f - rawFontMetrics.descent

    // offset2: 标签文字的视觉中心相对于基线的偏移
    val offset2 = -(tagFontMetrics.ascent + tagFontMetrics.descent) / 2f

    return y - offset1 + offset2
}

/**
 * 垂直居中文字组件
 *
 * 临时简化版本，确保文字能正常显示
 *
 * @param text 文字内容
 * @param fontSize 文字大小
 * @param color 文字颜色
 * @param fontWeight 文字粗细
 * @param containerHeight 容器高度，如果指定则文字在此高度内垂直居中
 * @param modifier 修饰符
 */
@Composable
fun VerticalCenterText(
    text: String,
    fontSize: TextUnit,
    color: Color = Color.Black,
    fontWeight: FontWeight? = null,
    containerHeight: Dp? = null,
    modifier: Modifier = Modifier
) {
    // 临时使用最简单的实现，确保文字能显示
    Box(
        modifier = modifier.then(
            if (containerHeight != null) {
                Modifier.height(containerHeight)
            } else {
                Modifier.wrapContentHeight()
            }
        ),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            fontSize = fontSize,
            color = color,
            fontWeight = fontWeight,
            maxLines = 1,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 简化版垂直居中文字（向后兼容）
 *
 * 如果上面的精确版本有问题，可以使用这个简化版本
 */
@Composable
fun SimpleVerticalCenterText(
    text: String,
    fontSize: TextUnit,
    color: Color = Color.Black,
    fontWeight: FontWeight? = null,
    containerHeight: Dp? = null,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current

    Layout(
        content = {
            Text(
                text = text,
                fontSize = fontSize,
                color = color,
                fontWeight = fontWeight,
                maxLines = 1
            )
        },
        modifier = modifier
    ) { measurables, constraints ->
        val textPlaceable = measurables[0].measure(constraints)

        val containerHeightPx = containerHeight?.let {
            with(density) { it.toPx().roundToInt() }
        } ?: textPlaceable.height

        val width = textPlaceable.width
        val height = max(containerHeightPx, textPlaceable.height)

        layout(width, height) {
            // 简单的几何居中，但添加字体大小相关的偏移调整
            val fontSizePx = with(density) { fontSize.toPx() }
            val baselineOffset = fontSizePx * 0.1f // 经验值：字体大小的10%作为基线偏移

            val yOffset = (height - textPlaceable.height) / 2 - baselineOffset.roundToInt()
            textPlaceable.placeRelative(0, yOffset)
        }
    }
}

/**
 * 混合大小文字的垂直居中行
 * 
 * 当需要在同一行显示不同大小的文字时，使用此组件确保它们都垂直居中对齐
 * 
 * @param modifier 修饰符
 * @param horizontalArrangement 水平排列方式
 * @param content 内容，应该包含多个VerticalCenterText
 */
@Composable
fun VerticalCenterRow(
    modifier: Modifier = Modifier,
    horizontalArrangement: Arrangement.Horizontal = Arrangement.Start,
    content: @Composable RowScope.() -> Unit
) {
    Layout(
        content = { Row(content = content) },
        modifier = modifier
    ) { measurables, constraints ->
        // 测量所有子组件
        val placeables = measurables.map { it.measure(constraints) }
        
        // 找到最大高度
        val maxHeight = placeables.maxOfOrNull { it.height } ?: 0
        val totalWidth = placeables.sumOf { it.width }
        
        layout(totalWidth, maxHeight) {
            var xOffset = 0
            placeables.forEach { placeable ->
                // 计算垂直居中位置
                val yOffset = (maxHeight - placeable.height) / 2
                placeable.placeRelative(xOffset, yOffset)
                xOffset += placeable.width
            }
        }
    }
}

/**
 * 标签与文字的垂直居中组合
 * 
 * 专门用于标签和文字混排的场景，确保标签和文字垂直居中对齐
 * 
 * @param tagContent 标签内容
 * @param textContent 文字内容
 * @param spacing 标签和文字之间的间距
 * @param modifier 修饰符
 */
@Composable
fun TagTextVerticalCenter(
    tagContent: @Composable () -> Unit,
    textContent: @Composable () -> Unit,
    spacing: Dp = 4.dp,
    modifier: Modifier = Modifier
) {
    Layout(
        content = {
            tagContent()
            Spacer(modifier = Modifier.width(spacing))
            textContent()
        },
        modifier = modifier
    ) { measurables, constraints ->
        // 测量标签
        val tagPlaceable = measurables[0].measure(constraints)
        
        // 测量间距
        val spacerPlaceable = measurables[1].measure(constraints)
        
        // 测量文字
        val textPlaceable = measurables[2].measure(constraints)
        
        // 计算总尺寸
        val totalWidth = tagPlaceable.width + spacerPlaceable.width + textPlaceable.width
        val maxHeight = max(tagPlaceable.height, textPlaceable.height)
        
        layout(totalWidth, maxHeight) {
            // 标签垂直居中
            val tagYOffset = (maxHeight - tagPlaceable.height) / 2
            tagPlaceable.placeRelative(0, tagYOffset)
            
            // 间距
            val spacerXOffset = tagPlaceable.width
            spacerPlaceable.placeRelative(spacerXOffset, 0)
            
            // 文字垂直居中
            val textXOffset = tagPlaceable.width + spacerPlaceable.width
            val textYOffset = (maxHeight - textPlaceable.height) / 2
            textPlaceable.placeRelative(textXOffset, textYOffset)
        }
    }
}

/**
 * 自适应高度的垂直居中文字
 * 
 * 根据指定的目标高度自动调整文字大小，确保文字在目标高度内垂直居中
 * 
 * @param text 文字内容
 * @param targetHeight 目标高度
 * @param maxFontSize 最大文字大小
 * @param minFontSize 最小文字大小
 * @param color 文字颜色
 * @param fontWeight 文字粗细
 * @param modifier 修饰符
 */
@Composable
fun AdaptiveVerticalCenterText(
    text: String,
    targetHeight: Dp,
    maxFontSize: TextUnit,
    minFontSize: TextUnit,
    color: Color = Color.Black,
    fontWeight: FontWeight? = null,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    
    // 计算合适的文字大小
    val fontSize = remember(text, targetHeight, maxFontSize, minFontSize) {
        with(density) {
            val targetHeightPx = targetHeight.toPx()
            val maxFontSizePx = maxFontSize.toPx()
            val minFontSizePx = minFontSize.toPx()
            
            // 简单的二分查找来找到合适的文字大小
            var low = minFontSizePx
            var high = maxFontSizePx
            var bestSize = minFontSizePx
            
            while (low <= high) {
                val mid = (low + high) / 2
                // 估算文字高度（简化计算）
                val estimatedHeight = mid * 1.2f // 文字高度通常是字体大小的1.2倍
                
                if (estimatedHeight <= targetHeightPx) {
                    bestSize = mid
                    low = mid + 1
                } else {
                    high = mid - 1
                }
            }
            
            bestSize.toSp()
        }
    }
    
    VerticalCenterText(
        text = text,
        fontSize = fontSize,
        color = color,
        fontWeight = fontWeight,
        containerHeight = targetHeight,
        modifier = modifier
    )
}

/**
 * 扩展函数：为标签组件提供垂直居中文字
 */
@Composable
fun TagBean.toVerticalCenterText(
    fontSize: TextUnit,
    containerHeight: Dp? = null,
    modifier: Modifier = Modifier
): Unit {
    VerticalCenterText(
        text = text,
        fontSize = fontSize,
        color = textColor,
        fontWeight = appearance.fontWeight,
        containerHeight = containerHeight,
        modifier = modifier
    )
}
