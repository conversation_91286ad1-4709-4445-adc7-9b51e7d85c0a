package com.taglib.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.layout.Placeable
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import kotlin.math.max
import kotlin.math.roundToInt

/**
 * 垂直居中文字组件
 * 
 * 对应原生库的VerticalCenterSpan，用于在混合不同大小文字时保持垂直居中对齐。
 * 
 * 原生库中的问题：
 * 当TextView中包含不同大小的文字时，默认的基线对齐会导致视觉上不够居中。
 * VerticalCenterSpan通过重新计算y坐标来实现真正的垂直居中。
 * 
 * Compose解决方案：
 * 使用自定义Layout来精确控制文字的垂直位置，确保不同大小的文字都能
 * 相对于容器垂直居中。
 * 
 * <AUTHOR> Tag Library Team
 * @since 1.0.0
 */

/**
 * 垂直居中的文字组件
 * 
 * @param text 文字内容
 * @param fontSize 文字大小
 * @param color 文字颜色
 * @param fontWeight 文字粗细
 * @param textAlign 文字对齐方式
 * @param containerHeight 容器高度，如果指定则文字在此高度内垂直居中
 * @param modifier 修饰符
 */
@Composable
fun VerticalCenterText(
    text: String,
    fontSize: TextUnit,
    color: Color = Color.Black,
    fontWeight: FontWeight? = null,
    textAlign: TextAlign? = null,
    containerHeight: Dp? = null,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    
    Layout(
        content = {
            Text(
                text = text,
                fontSize = fontSize,
                color = color,
                fontWeight = fontWeight,
                textAlign = textAlign,
                maxLines = 1
            )
        },
        modifier = modifier
    ) { measurables, constraints ->
        // 测量文字
        val textPlaceable = measurables[0].measure(constraints)
        
        // 计算容器尺寸
        val containerHeightPx = containerHeight?.let { 
            with(density) { it.toPx().roundToInt() }
        } ?: textPlaceable.height
        
        val width = textPlaceable.width
        val height = max(containerHeightPx, textPlaceable.height)
        
        layout(width, height) {
            // 计算垂直居中位置
            val yOffset = (height - textPlaceable.height) / 2
            textPlaceable.placeRelative(0, yOffset)
        }
    }
}

/**
 * 混合大小文字的垂直居中行
 * 
 * 当需要在同一行显示不同大小的文字时，使用此组件确保它们都垂直居中对齐
 * 
 * @param modifier 修饰符
 * @param horizontalArrangement 水平排列方式
 * @param content 内容，应该包含多个VerticalCenterText
 */
@Composable
fun VerticalCenterRow(
    modifier: Modifier = Modifier,
    horizontalArrangement: Arrangement.Horizontal = Arrangement.Start,
    content: @Composable RowScope.() -> Unit
) {
    Layout(
        content = { Row(content = content) },
        modifier = modifier
    ) { measurables, constraints ->
        // 测量所有子组件
        val placeables = measurables.map { it.measure(constraints) }
        
        // 找到最大高度
        val maxHeight = placeables.maxOfOrNull { it.height } ?: 0
        val totalWidth = placeables.sumOf { it.width }
        
        layout(totalWidth, maxHeight) {
            var xOffset = 0
            placeables.forEach { placeable ->
                // 计算垂直居中位置
                val yOffset = (maxHeight - placeable.height) / 2
                placeable.placeRelative(xOffset, yOffset)
                xOffset += placeable.width
            }
        }
    }
}

/**
 * 标签与文字的垂直居中组合
 * 
 * 专门用于标签和文字混排的场景，确保标签和文字垂直居中对齐
 * 
 * @param tagContent 标签内容
 * @param textContent 文字内容
 * @param spacing 标签和文字之间的间距
 * @param modifier 修饰符
 */
@Composable
fun TagTextVerticalCenter(
    tagContent: @Composable () -> Unit,
    textContent: @Composable () -> Unit,
    spacing: Dp = 4.dp,
    modifier: Modifier = Modifier
) {
    Layout(
        content = {
            tagContent()
            Spacer(modifier = Modifier.width(spacing))
            textContent()
        },
        modifier = modifier
    ) { measurables, constraints ->
        // 测量标签
        val tagPlaceable = measurables[0].measure(constraints)
        
        // 测量间距
        val spacerPlaceable = measurables[1].measure(constraints)
        
        // 测量文字
        val textPlaceable = measurables[2].measure(constraints)
        
        // 计算总尺寸
        val totalWidth = tagPlaceable.width + spacerPlaceable.width + textPlaceable.width
        val maxHeight = max(tagPlaceable.height, textPlaceable.height)
        
        layout(totalWidth, maxHeight) {
            // 标签垂直居中
            val tagYOffset = (maxHeight - tagPlaceable.height) / 2
            tagPlaceable.placeRelative(0, tagYOffset)
            
            // 间距
            val spacerXOffset = tagPlaceable.width
            spacerPlaceable.placeRelative(spacerXOffset, 0)
            
            // 文字垂直居中
            val textXOffset = tagPlaceable.width + spacerPlaceable.width
            val textYOffset = (maxHeight - textPlaceable.height) / 2
            textPlaceable.placeRelative(textXOffset, textYOffset)
        }
    }
}

/**
 * 自适应高度的垂直居中文字
 * 
 * 根据指定的目标高度自动调整文字大小，确保文字在目标高度内垂直居中
 * 
 * @param text 文字内容
 * @param targetHeight 目标高度
 * @param maxFontSize 最大文字大小
 * @param minFontSize 最小文字大小
 * @param color 文字颜色
 * @param fontWeight 文字粗细
 * @param modifier 修饰符
 */
@Composable
fun AdaptiveVerticalCenterText(
    text: String,
    targetHeight: Dp,
    maxFontSize: TextUnit,
    minFontSize: TextUnit,
    color: Color = Color.Black,
    fontWeight: FontWeight? = null,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    
    // 计算合适的文字大小
    val fontSize = remember(text, targetHeight, maxFontSize, minFontSize) {
        with(density) {
            val targetHeightPx = targetHeight.toPx()
            val maxFontSizePx = maxFontSize.toPx()
            val minFontSizePx = minFontSize.toPx()
            
            // 简单的二分查找来找到合适的文字大小
            var low = minFontSizePx
            var high = maxFontSizePx
            var bestSize = minFontSizePx
            
            while (low <= high) {
                val mid = (low + high) / 2
                // 估算文字高度（简化计算）
                val estimatedHeight = mid * 1.2f // 文字高度通常是字体大小的1.2倍
                
                if (estimatedHeight <= targetHeightPx) {
                    bestSize = mid
                    low = mid + 1
                } else {
                    high = mid - 1
                }
            }
            
            bestSize.toSp()
        }
    }
    
    VerticalCenterText(
        text = text,
        fontSize = fontSize,
        color = color,
        fontWeight = fontWeight,
        containerHeight = targetHeight,
        modifier = modifier
    )
}

/**
 * 扩展函数：为标签组件提供垂直居中文字
 */
@Composable
fun TagBean.toVerticalCenterText(
    fontSize: TextUnit,
    containerHeight: Dp? = null,
    modifier: Modifier = Modifier
): Unit {
    VerticalCenterText(
        text = text,
        fontSize = fontSize,
        color = textColor,
        fontWeight = appearance.fontWeight,
        containerHeight = containerHeight,
        modifier = modifier
    )
}
