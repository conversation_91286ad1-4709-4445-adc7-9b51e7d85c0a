package com.taglib.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.taglib.TagBean
import com.taglib.TagType
import com.taglib.ImageLoaderManager
import com.taglib.getCachedPointsIcon

/**
 * 积分标签组件（JF标签）
 * 带图标的积分标签，左侧显示图标，右侧显示积分文字
 * 对应原生库的FROM_JF类型标签
 */
@Composable
fun PointsTag(
    tagBean: TagBean,
    iconPainter: Painter? = null,
    onClick: ((TagBean) -> Unit)? = null,
    arrowIcon: ImageVector? = null,
    modifier: Modifier = Modifier
) {
    require(tagBean.type == TagType.POINTS) {
        "PointsTag only supports POINTS type"
    }
    
    val appearance = tagBean.appearance
    
    // 点击修饰符
    val clickModifier = if (tagBean.isClickable && onClick != null) {
        Modifier.clickable { onClick(tagBean) }
    } else {
        Modifier
    }
    
    Row(
        modifier = modifier
            .then(clickModifier)
            .clip(appearance.shape),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // 图标部分
        val iconSize = appearance.textSize.value.dp * 1.5f
        Box(
            modifier = Modifier
                .size(iconSize)
                .background(
                    color = tagBean.backgroundColor,
                    shape = RoundedCornerShape(
                        topStart = appearance.cornerRadius,
                        bottomStart = appearance.cornerRadius,
                        topEnd = 0.dp,
                        bottomEnd = 0.dp
                    )
                )
                .border(
                    width = appearance.borderWidth,
                    color = tagBean.borderColor,
                    shape = RoundedCornerShape(
                        topStart = appearance.cornerRadius,
                        bottomStart = appearance.cornerRadius,
                        topEnd = 0.dp,
                        bottomEnd = 0.dp
                    )
                ),
            contentAlignment = Alignment.Center
        ) {
            // 使用缓存图标加载，通过ImageLoaderManager获取全局imageLoader
            val cachedIcon = tagBean.getCachedPointsIcon(
                imageLoader = ImageLoaderManager.getComposeImageLoader(),
                defaultIcon = iconPainter
            )

            if (cachedIcon != null) {
                Image(
                    painter = cachedIcon,
                    contentDescription = "Points icon",
                    modifier = Modifier.size(iconSize * appearance.frontImageRate),
                    contentScale = ContentScale.Fit
                )
            } else if (iconPainter != null) {
                Image(
                    painter = iconPainter,
                    contentDescription = "Points icon",
                    modifier = Modifier.size(iconSize * appearance.frontImageRate),
                    contentScale = ContentScale.Fit
                )
            } else {
                // 默认星形图标
                Icon(
                    imageVector = Icons.Default.Star,
                    contentDescription = "Points icon",
                    tint = tagBean.textColor,
                    modifier = Modifier.size(iconSize * appearance.frontImageRate)
                )
            }
        }
        
        // 分隔线
        Box(
            modifier = Modifier
                .width(appearance.borderWidth)
                .height(iconSize)
                .background(tagBean.borderColor)
        )
        
        // 文字部分
        Box(
            modifier = Modifier
                .height(iconSize)
                .background(
                    color = tagBean.backgroundEndColor ?: tagBean.backgroundColor,
                    shape = RoundedCornerShape(
                        topStart = 0.dp,
                        bottomStart = 0.dp,
                        topEnd = appearance.cornerRadius,
                        bottomEnd = appearance.cornerRadius
                    )
                )
                .border(
                    width = appearance.borderWidth,
                    color = tagBean.borderColor,
                    shape = RoundedCornerShape(
                        topStart = 0.dp,
                        bottomStart = 0.dp,
                        topEnd = appearance.cornerRadius,
                        bottomEnd = appearance.cornerRadius
                    )
                )
                .padding(
                    horizontal = appearance.horizontalPadding,
                    vertical = appearance.verticalPadding
                ),
            contentAlignment = Alignment.Center
        ) {
            VerticalCenterText(
                text = tagBean.text,
                fontSize = if (tagBean.useFixedHeight) appearance.fixedTextSize else appearance.textSize,
                color = tagBean.textColor,
                fontWeight = appearance.fontWeight,
                containerHeight = if (tagBean.useFixedHeight && appearance.tagHeight.value > 0) {
                    appearance.tagHeight
                } else null
            )
        }
        
        // 可点击箭头
        if (tagBean.isClickable && arrowIcon != null) {
            Spacer(modifier = Modifier.width(appearance.arrowSpacing))
            Icon(
                imageVector = arrowIcon,
                contentDescription = "Click arrow",
                tint = tagBean.textColor,
                modifier = Modifier.size(appearance.arrowWidth)
            )
        }
    }
}
