package com.taglib.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.taglib.TagBean
import com.taglib.TagType
import com.taglib.ImageLoaderManager
import com.taglib.getCachedPointsIcon

/**
 * 积分标签组件（JF标签）
 * 完全模拟原生库的FROM_JF类型标签
 * 图标加载通过ImageLoaderManager处理，与原生保持一致
 */
@Composable
fun PointsTag(
    tagBean: TagBean,
    onClick: ((TagBean) -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    require(tagBean.type == TagType.POINTS) {
        "PointsTag only supports POINTS type"
    }
    
    val appearance = tagBean.appearance
    
    // 点击修饰符
    val clickModifier = if (tagBean.isClickable && onClick != null) {
        Modifier.clickable { onClick(tagBean) }
    } else {
        Modifier
    }
    
    Row(
        modifier = modifier
            .then(clickModifier)
            .clip(appearance.shape),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // 图标部分 - 使用统一的高度计算
        val tagHeight = TagUtils.calculateTagHeight(tagBean)
        val iconSize = tagHeight.value.dp
        Box(
            modifier = Modifier
                .size(iconSize)
                .background(
                    color = tagBean.backgroundColor,
                    shape = RoundedCornerShape(
                        topStart = appearance.cornerRadius,
                        bottomStart = appearance.cornerRadius,
                        topEnd = 0.dp,
                        bottomEnd = 0.dp
                    )
                )
                .border(
                    width = appearance.borderWidth,
                    color = tagBean.borderColor,
                    shape = RoundedCornerShape(
                        topStart = appearance.cornerRadius,
                        bottomStart = appearance.cornerRadius,
                        topEnd = 0.dp,
                        bottomEnd = 0.dp
                    )
                ),
            contentAlignment = Alignment.Center
        ) {
            // 🎯 完全模拟原生：图标通过ImageLoaderManager的回调机制处理
            var iconPainter by remember { mutableStateOf<Painter?>(null) }
            var isLoading by remember { mutableStateOf(true) }
            var hasError by remember { mutableStateOf(false) }

            // 🎯 模拟原生逻辑：通过回调机制加载图标
            LaunchedEffect(tagBean.imageUrl) {
                tagBean.imageUrl?.let { url ->
                    isLoading = true
                    hasError = false

                    ImageLoaderManager.loadImageCompose(
                        url = url,
                        onSuccess = { painter ->
                            iconPainter = painter
                            isLoading = false
                            hasError = false
                        },
                        onFailure = { failPainter ->
                            iconPainter = failPainter
                            isLoading = false
                            hasError = true
                        }
                    )
                } ?: run {
                    // 没有URL，直接设置为失败状态
                    isLoading = false
                    hasError = true
                }
            }

            when {
                isLoading -> {
                    // 加载中状态 - 使用默认图标
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = "Points icon",
                        tint = tagBean.textColor,
                        modifier = Modifier.size(iconSize * appearance.frontImageRate)
                    )
                }
                iconPainter != null -> {
                    // 加载成功 - 显示自定义图标
                    Image(
                        painter = iconPainter!!,
                        contentDescription = "Points icon",
                        modifier = Modifier.size(iconSize * appearance.frontImageRate),
                        contentScale = ContentScale.Fit
                    )
                }
                else -> {
                    // 加载失败 - 使用默认图标
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = "Points icon",
                        tint = tagBean.textColor,
                        modifier = Modifier.size(iconSize * appearance.frontImageRate)
                    )
                }
            }
        }
        
        // 分隔线
        Box(
            modifier = Modifier
                .width(appearance.borderWidth)
                .height(iconSize)
                .background(tagBean.borderColor)
        )
        
        // 文字部分
        Box(
            modifier = Modifier
                .height(iconSize)
                .background(
                    color = tagBean.backgroundEndColor ?: tagBean.backgroundColor,
                    shape = RoundedCornerShape(
                        topStart = 0.dp,
                        bottomStart = 0.dp,
                        topEnd = appearance.cornerRadius,
                        bottomEnd = appearance.cornerRadius
                    )
                )
                .border(
                    width = appearance.borderWidth,
                    color = tagBean.borderColor,
                    shape = RoundedCornerShape(
                        topStart = 0.dp,
                        bottomStart = 0.dp,
                        topEnd = appearance.cornerRadius,
                        bottomEnd = appearance.cornerRadius
                    )
                )
                .padding(
                    horizontal = appearance.horizontalPadding,
                    vertical = appearance.verticalPadding
                ),
            contentAlignment = Alignment.Center
        ) {
            // 🎯 使用缓存的文字大小计算，避免重复计算
            val realTagTextSize = TagUtils.getTagTextSizeCached(tagBean)
            TagText(
                text = tagBean.text,
                fontSize = realTagTextSize.sp,
                color = tagBean.textColor,
                fontWeight = appearance.fontWeight
            )
        }
        
        // 🎯 模拟原生：原生版本没有箭头图标，移除此功能
    }
}
