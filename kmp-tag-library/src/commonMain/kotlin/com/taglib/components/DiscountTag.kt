package com.taglib.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.taglib.TagBean
import com.taglib.TagType

/**
 * 折省标签组件（ZS标签）
 * 特殊的分段显示标签，通常用于显示折扣信息
 * 例如："折"和"省"分别显示在不同的背景区域
 */
@Composable
fun DiscountTag(
    tagBean: TagBean,
    onClick: ((TagBean) -> Unit)? = null,
    arrowIcon: ImageVector? = null,
    modifier: Modifier = Modifier
) {
    require(tagBean.type == TagType.DISCOUNT) {
        "DiscountTag only supports DISCOUNT type"
    }
    
    val appearance = tagBean.appearance
    val text = tagBean.text
    
    // 如果文字长度小于2，则按普通标签处理
    if (text.length < 2) {
        FillTag(
            tagBean = tagBean.copy(type = TagType.FILL),
            onClick = onClick,
            arrowIcon = arrowIcon,
            modifier = modifier
        )
        return
    }
    
    // 分割文字：第一个字符和剩余字符
    val firstChar = text.substring(0, 1)
    val remainingText = text.substring(1)
    
    // 点击修饰符
    val clickModifier = if (tagBean.isClickable && onClick != null) {
        Modifier.clickable { onClick(tagBean) }
    } else {
        Modifier
    }
    
    Row(
        modifier = modifier
            .then(clickModifier)
            .clip(appearance.shape),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // 第一部分（通常是"折"）
        Box(
            modifier = Modifier
                .background(
                    color = tagBean.backgroundColor,
                    shape = RoundedCornerShape(
                        topStart = appearance.cornerRadius,
                        bottomStart = appearance.cornerRadius,
                        topEnd = 0.dp,
                        bottomEnd = 0.dp
                    )
                )
                .border(
                    width = appearance.borderWidth,
                    color = tagBean.borderColor,
                    shape = RoundedCornerShape(
                        topStart = appearance.cornerRadius,
                        bottomStart = appearance.cornerRadius,
                        topEnd = 0.dp,
                        bottomEnd = 0.dp
                    )
                )
                .padding(
                    horizontal = appearance.horizontalPadding,
                    vertical = appearance.verticalPadding
                ),
            contentAlignment = Alignment.Center
        ) {
            TagText(
                text = firstChar,
                fontSize = if (tagBean.useFixedHeight) appearance.fixedTextSize else appearance.textSize,
                color = tagBean.textColor,
                fontWeight = appearance.fontWeight
            )
        }
        
        // 分隔线 - 使用统一的高度计算
        val separatorHeight = TagUtils.calculateTagHeight(tagBean)
        Box(
            modifier = Modifier
                .width(appearance.borderWidth)
                .height(separatorHeight.value.dp)
                .background(tagBean.borderColor)
        )
        
        // 第二部分（通常是"省"或其他内容）
        Box(
            modifier = Modifier
                .background(
                    color = tagBean.backgroundEndColor ?: tagBean.backgroundColor,
                    shape = RoundedCornerShape(
                        topStart = 0.dp,
                        bottomStart = 0.dp,
                        topEnd = appearance.cornerRadius,
                        bottomEnd = appearance.cornerRadius
                    )
                )
                .border(
                    width = appearance.borderWidth,
                    color = tagBean.borderColor,
                    shape = RoundedCornerShape(
                        topStart = 0.dp,
                        bottomStart = 0.dp,
                        topEnd = appearance.cornerRadius,
                        bottomEnd = appearance.cornerRadius
                    )
                )
                .padding(
                    horizontal = appearance.horizontalPadding,
                    vertical = appearance.verticalPadding
                ),
            contentAlignment = Alignment.Center
        ) {
            TagText(
                text = remainingText,
                fontSize = if (tagBean.useFixedHeight) appearance.fixedTextSize else appearance.textSize,
                color = tagBean.textColor,
                fontWeight = appearance.fontWeight
            )
        }
        
        // 可点击箭头
        if (tagBean.isClickable && arrowIcon != null) {
            Spacer(modifier = Modifier.width(appearance.arrowSpacing))
            Icon(
                imageVector = arrowIcon,
                contentDescription = "Click arrow",
                tint = tagBean.textColor,
                modifier = Modifier.size(appearance.arrowWidth)
            )
        }
    }
}
