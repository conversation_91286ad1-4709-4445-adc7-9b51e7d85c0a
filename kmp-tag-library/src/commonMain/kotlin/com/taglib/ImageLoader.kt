package com.taglib

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.painter.Painter

/**
 * 图片加载接口
 * 对应原生库的INetPicLoader
 */
interface ImageLoader {

    /**
     * 加载网络图片
     * @param url 图片URL
     * @param callback 加载回调
     */
    fun loadImage(url: String, callback: ImageCallback)

    /**
     * 取消加载
     * @param url 图片URL
     */
    fun cancelLoad(url: String)

    /**
     * 清除缓存
     */
    fun clearCache()
}

/**
 * 图片加载回调接口
 * 对应原生库的ImageCallback
 */
interface ImageCallback {

    /**
     * 图片加载成功
     * @param painter 图片Painter
     */
    fun onSuccess(painter: Painter)

    /**
     * 图片加载失败
     * @param error 错误信息
     */
    fun onError(error: Throwable)

    /**
     * 加载开始
     */
    fun onStart() {}
}

/**
 * 图片加载回调接口
 * 完全模拟Android原生的ImageCallback
 */
interface ImageCallback {
    /**
     * 图片加载成功回调
     * @param painter 加载成功的Painter
     */
    fun onBitmapReady(painter: Painter?)

    /**
     * 图片加载失败回调
     * @param failPainter 失败时的Painter（可选）
     */
    fun onFail(failPainter: Painter?)
}

/**
 * Compose图片加载器接口
 * 完全模拟Android原生的INetPicLoader接口
 *
 * 与原生保持一致：
 * - 异步回调机制
 * - 用户自定义加载逻辑
 * - 标签库不依赖第三方图片加载库
 */
interface TagImageLoader {

    /**
     * 加载图片
     * 完全模拟原生的loadImage方法
     *
     * @param url 图片URL
     * @param imageCallback 图片加载回调
     */
    fun loadImage(url: String, imageCallback: ImageCallback?)
}

/**
 * Compose图片加载器别名
 * 为了兼容性，提供ComposeImageLoader别名
 */
typealias ComposeImageLoader = TagImageLoader

/**
 * 图片加载管理器
 * 管理全局的图片加载器实例
 * 对应原生库中的netPicLoader全局变量
 */
object ImageLoaderManager {

    /**
     * Compose图片加载器实例
     * 专门为Compose环境设计的图片加载器
     */
    private var tagImageLoader: TagImageLoader? = null

    /**
     * 调试模式标志
     * 对应原生的isDebug
     */
    private var isDebugMode: Boolean = false

    /**
     * 初始化图片加载器
     * 对应原生的TagUtils.initNetLoader，但适配Compose
     * @param loader 图片加载器实例
     * @param debug 是否开启调试模式
     */
    fun initializeCompose(loader: TagImageLoader?, debug: Boolean = false) {
        tagImageLoader = loader
        isDebugMode = debug

        if (debug && loader == null) {
            println("⚠️ ImageLoaderManager: TagImageLoader is null")
        }
    }

    /**
     * 获取图片加载器
     * 对应原生的netPicLoader
     */
    fun getTagImageLoader(): TagImageLoader? = tagImageLoader

    /**
     * 是否为调试模式
     * 对应原生的isDebug
     */
    fun isDebug(): Boolean = isDebugMode

    /**
     * 检查是否已初始化
     * 对应原生库中对netPicLoader的null检查
     */
    fun isInitialized(): Boolean = tagImageLoader != null



    /**
     * 在Compose中加载图片
     * 完全模拟原生的图片加载逻辑，使用回调机制
     *
     * @param url 图片URL
     * @param onSuccess 成功回调
     * @param onFailure 失败回调
     */
    fun loadImageCompose(
        url: String,
        onSuccess: (Painter?) -> Unit,
        onFailure: (Painter?) -> Unit
    ) {
        if (url.isBlank()) {
            if (isDebugMode) {
                println("⚠️ ImageLoaderManager: Image URL is blank")
            }
            onFailure(null)
            return
        }

        val loader = tagImageLoader
        if (loader == null) {
            if (isDebugMode) {
                println("⚠️ ImageLoaderManager: TagImageLoader not initialized")
            }
            onFailure(null)
            return
        }

        try {
            loader.loadImage(url, object : ImageCallback {
                override fun onBitmapReady(painter: Painter?) {
                    if (isDebugMode) {
                        println("✅ ImageLoaderManager: Image loaded successfully: $url")
                    }
                    onSuccess(painter)
                }

                override fun onFail(failPainter: Painter?) {
                    if (isDebugMode) {
                        println("❌ ImageLoaderManager: Failed to load image: $url")
                    }
                    onFailure(failPainter)
                }
            })
        } catch (e: Exception) {
            if (isDebugMode) {
                println("⚠️ ImageLoaderManager: Error loading image: ${e.message}")
            }
            onFailure(null)
        }
    }

    /**
     * 重置管理器状态
     */
    fun reset() {
        tagImageLoader = null
        isDebugMode = false
    }
}

/**
 * 默认的空图片加载器实现（已废弃）
 * 注意：这个类引用了已废弃的ImageLoader接口
 */
@Deprecated("Use EmptyTagImageLoader instead")
class EmptyImageLoader {
    // 这个类已废弃，不再使用
}

/**
 * 默认的空图片加载器实现
 * 模拟原生的空实现，直接调用失败回调
 */
class EmptyTagImageLoader : TagImageLoader {
    override fun loadImage(url: String, imageCallback: ImageCallback?) {
        // 模拟原生：如果没有图片加载器，直接调用失败回调
        imageCallback?.onFail(null)
    }
}
