package com.taglib

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.painter.Painter

/**
 * 图片加载接口
 * 对应原生库的INetPicLoader
 */
interface ImageLoader {
    
    /**
     * 加载网络图片
     * @param url 图片URL
     * @param callback 加载回调
     */
    fun loadImage(url: String, callback: ImageCallback)
    
    /**
     * 取消加载
     * @param url 图片URL
     */
    fun cancelLoad(url: String)
    
    /**
     * 清除缓存
     */
    fun clearCache()
}

/**
 * 图片加载回调接口
 * 对应原生库的ImageCallback
 */
interface ImageCallback {
    
    /**
     * 图片加载成功
     * @param painter 图片Painter
     */
    fun onSuccess(painter: Painter)
    
    /**
     * 图片加载失败
     * @param error 错误信息
     */
    fun onError(error: Throwable)
    
    /**
     * 加载开始
     */
    fun onStart() {}
}

/**
 * Compose图片加载器
 * 提供Compose环境下的图片加载功能
 */
interface ComposeImageLoader {
    
    /**
     * 在Compose中加载图片
     * @param url 图片URL
     * @param placeholder 占位图
     * @param error 错误图
     * @return Painter
     */
    @Composable
    fun loadImage(
        url: String,
        placeholder: Painter? = null,
        error: Painter? = null
    ): Painter?
}

/**
 * 图片加载管理器
 * 管理全局的图片加载器实例
 * 对应原生库中的netPicLoader全局变量
 */
object ImageLoaderManager {

    private var imageLoader: ImageLoader? = null
    private var composeImageLoader: ComposeImageLoader? = null
    private var isDebugMode: Boolean = false

    /**
     * 初始化图片加载器
     * 对应原生的TagUtils.initNetLoader(context, loader, debug)
     * @param loader 图片加载器实例
     * @param debug 是否开启调试模式
     */
    fun initialize(loader: ImageLoader?, debug: Boolean = false) {
        imageLoader = loader
        isDebugMode = debug

        if (debug && loader == null) {
            println("⚠️ ImageLoaderManager: ImageLoader is null")
        }
    }

    /**
     * 初始化Compose图片加载器
     * 对应原生的TagUtils.initNetLoader，但适配Compose
     * @param loader Compose图片加载器实例
     */
    fun initializeCompose(loader: ComposeImageLoader?) {
        composeImageLoader = loader

        if (isDebugMode && loader == null) {
            println("⚠️ ImageLoaderManager: ComposeImageLoader is null")
        }
    }

    /**
     * 获取图片加载器
     * 对应原生的netPicLoader
     */
    fun getImageLoader(): ImageLoader? = imageLoader

    /**
     * 获取Compose图片加载器
     * 对应原生的netPicLoader，但返回Compose版本
     */
    fun getComposeImageLoader(): ComposeImageLoader? = composeImageLoader

    /**
     * 是否为调试模式
     * 对应原生的isDebug
     */
    fun isDebug(): Boolean = isDebugMode

    /**
     * 检查是否已初始化
     * 对应原生库中对netPicLoader的null检查
     */
    fun isInitialized(): Boolean = composeImageLoader != null

    /**
     * 加载图片
     * 对应原生的netPicLoader.loadImage(url, callback)
     */
    fun loadImage(url: String, callback: ImageCallback) {
        if (url.isBlank()) {
            callback.onError(IllegalArgumentException("Image URL is blank"))
            return
        }

        val loader = imageLoader
        if (loader == null) {
            if (isDebugMode) {
                println("⚠️ ImageLoaderManager: ImageLoader not initialized, call initialize() first")
            }
            callback.onError(IllegalStateException("ImageLoader not initialized"))
            return
        }

        try {
            loader.loadImage(url, callback)
        } catch (e: Exception) {
            if (isDebugMode) {
                println("⚠️ ImageLoaderManager: Error loading image: ${e.message}")
            }
            callback.onError(e)
        }
    }

    /**
     * 在Compose中加载图片
     * 对应原生的图片加载逻辑，但适配Compose
     */
    @Composable
    fun loadImageCompose(
        url: String,
        placeholder: Painter? = null,
        error: Painter? = null
    ): Painter? {
        if (url.isBlank()) {
            if (isDebugMode) {
                println("⚠️ ImageLoaderManager: Image URL is blank")
            }
            return error
        }

        val loader = composeImageLoader
        if (loader == null) {
            if (isDebugMode) {
                println("⚠️ ImageLoaderManager: ComposeImageLoader not initialized")
            }
            return error
        }

        // Compose函数不能用try-catch包围，错误处理由具体实现负责
        return loader.loadImage(url, placeholder, error)
    }

    /**
     * 重置管理器状态
     */
    fun reset() {
        imageLoader = null
        composeImageLoader = null
        isDebugMode = false
    }
}

/**
 * 默认的空图片加载器实现
 */
class EmptyImageLoader : ImageLoader {
    override fun loadImage(url: String, callback: ImageCallback) {
        callback.onError(UnsupportedOperationException("ImageLoader not implemented"))
    }
    
    override fun cancelLoad(url: String) {
        // 空实现
    }
    
    override fun clearCache() {
        // 空实现
    }
}

/**
 * 默认的空Compose图片加载器实现
 */
class EmptyComposeImageLoader : ComposeImageLoader {
    @Composable
    override fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter? {
        return error // 直接返回错误图片
    }
}
