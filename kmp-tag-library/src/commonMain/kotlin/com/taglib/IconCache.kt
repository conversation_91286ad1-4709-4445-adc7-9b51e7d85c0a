package com.taglib

import androidx.compose.runtime.*
import androidx.compose.ui.graphics.painter.Painter
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock


/**
 * 图标缓存管理器
 * 
 * 对应原生库的getJFBitmap方法，用于缓存积分标签等常用图标，
 * 避免重复加载，提升性能。
 * 
 * 主要功能：
 * 1. 内存缓存：将加载过的图标保存在内存中
 * 2. 异步加载：支持异步加载图标，不阻塞UI
 * 3. 生命周期管理：自动清理过期缓存
 * 4. 线程安全：支持多线程并发访问
 * 
 * 使用场景：
 * - 积分标签的星形图标
 * - 折扣标签的特殊图标
 * - 其他频繁使用的小图标
 * 
 * <AUTHOR> Tag Library Team
 * @since 1.0.0
 */
object IconCache {
    
    /**
     * 缓存项数据类
     */
    private data class CacheItem(
        val painter: Painter,
        val timestamp: Long = getCurrentTimeMillis(),
        val accessCount: Int = 1
    )
    
    /**
     * 内存缓存
     * key: 图标标识（URL或资源ID）
     * value: 缓存项
     */
    private val cache = mutableMapOf<String, CacheItem>()
    
    /**
     * 缓存访问锁
     */
    private val cacheMutex = Mutex()
    
    /**
     * 缓存配置
     */
    private var maxCacheSize = 50 // 最大缓存数量
    private var maxCacheAge = 30 * 60 * 1000L // 最大缓存时间：30分钟
    
    /**
     * 获取积分图标
     * 对应原生库的getJFBitmap方法
     *
     * 简化版本：直接在Compose作用域中处理，避免在协程中调用Compose函数
     *
     * @param iconKey 图标标识（URL或资源标识）
     * @param imageLoader 图片加载器
     * @param defaultIcon 默认图标
     * @return 图标Painter
     */
    @Composable
    fun getPointsIcon(
        iconKey: String,
        imageLoader: TagImageLoader?,
        defaultIcon: Painter? = null
    ): Painter? {
        // 参数验证
        if (iconKey.isBlank() || imageLoader == null) {
            return defaultIcon
        }

        // 状态：缓存的图标
        var cachedIcon by remember(iconKey) { mutableStateOf<Painter?>(null) }
        var hasCheckedCache by remember(iconKey) { mutableStateOf(false) }

        // 检查缓存
        LaunchedEffect(iconKey) {
            if (!hasCheckedCache) {
                val cached = getCachedIcon(iconKey)
                cachedIcon = cached
                hasCheckedCache = true
            }
        }

        // 如果缓存中有，直接返回
        if (cachedIcon != null) {
            return cachedIcon
        }

        // 如果还没检查过缓存，返回默认图标
        if (!hasCheckedCache) {
            return defaultIcon
        }

        // 缓存中没有，直接在Compose作用域中加载
        val loadedIcon = imageLoader.loadImage(iconKey, defaultIcon, defaultIcon)

        // 如果加载成功，缓存结果
        if (loadedIcon != null && loadedIcon != defaultIcon) {
            LaunchedEffect(loadedIcon) {
                putCachedIcon(iconKey, loadedIcon)
            }
            return loadedIcon
        }

        return defaultIcon
    }
    
    /**
     * 从缓存获取图标
     * 
     * @param key 图标标识
     * @return 缓存的图标，如果不存在返回null
     */
    private suspend fun getCachedIcon(key: String): Painter? {
        return cacheMutex.withLock {
            val item = cache[key]
            if (item != null) {
                // 检查是否过期
                if (getCurrentTimeMillis() - item.timestamp > maxCacheAge) {
                    cache.remove(key)
                    null
                } else {
                    // 更新访问计数
                    cache[key] = item.copy(accessCount = item.accessCount + 1)
                    item.painter
                }
            } else {
                null
            }
        }
    }
    
    /**
     * 将图标放入缓存
     * 
     * @param key 图标标识
     * @param painter 图标Painter
     */
    private suspend fun putCachedIcon(key: String, painter: Painter) {
        cacheMutex.withLock {
            // 检查缓存大小，如果超过限制则清理
            if (cache.size >= maxCacheSize) {
                cleanupCache()
            }
            
            cache[key] = CacheItem(painter)
        }
    }
    
    /**
     * 清理缓存
     *
     * 清理策略：
     * 1. 优先清理过期的缓存项
     * 2. 如果仍然超过限制，清理访问次数最少的项
     */
    private fun cleanupCache() {
        val currentTime = getCurrentTimeMillis()

        // 1. 清理过期项
        val expiredKeys = cache.filter { (_, item) ->
            currentTime - item.timestamp > maxCacheAge
        }.keys
        expiredKeys.forEach { cache.remove(it) }

        // 2. 如果仍然超过限制，清理访问次数最少的项
        if (cache.size >= maxCacheSize) {
            val sortedByAccess = cache.toList().sortedBy { it.second.accessCount }
            val toRemove = sortedByAccess.take(cache.size - maxCacheSize + 1)
            toRemove.forEach { (key, _) ->
                cache.remove(key)
            }
        }
    }
    
    /**
     * 手动清理过期缓存
     * 
     * 可以定期调用此方法清理过期缓存，释放内存
     */
    suspend fun cleanupExpiredCache() {
        cacheMutex.withLock {
            cleanupCache()
        }
    }
    
    /**
     * 清空所有缓存
     */
    suspend fun clearAllCache() {
        cacheMutex.withLock {
            cache.clear()
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    suspend fun getCacheStats(): CacheStats {
        return cacheMutex.withLock {
            CacheStats(
                totalItems = cache.size,
                maxSize = maxCacheSize,
                maxAge = maxCacheAge,
                oldestTimestamp = cache.values.minOfOrNull { it.timestamp } ?: 0,
                newestTimestamp = cache.values.maxOfOrNull { it.timestamp } ?: 0,
                totalAccessCount = cache.values.sumOf { it.accessCount }
            )
        }
    }
    
    /**
     * 配置缓存参数
     * 
     * @param maxSize 最大缓存数量
     * @param maxAge 最大缓存时间（毫秒）
     */
    fun configureCacheSettings(maxSize: Int = 50, maxAge: Long = 30 * 60 * 1000L) {
        maxCacheSize = maxSize
        maxCacheAge = maxAge
    }
    
    /**
     * 预加载常用图标
     *
     * 注意：由于Compose函数不能在协程中调用，这个方法改为非Compose版本
     * 需要在应用启动时通过其他方式调用
     *
     * @param iconKeys 要预加载的图标标识列表
     * @param imageLoader 图片加载器
     */
    suspend fun preloadIconsAsync(
        iconKeys: List<String>,
        imageLoader: TagImageLoader?
    ) {
        if (imageLoader == null) return

        iconKeys.forEach { key ->
            if (getCachedIcon(key) == null) {
                // 注意：这里不能调用Compose函数
                // 实际的预加载需要在具体的ImageLoader实现中处理
                // 这里只是一个占位实现
                if (TagUtils.isDebugMode) {
                    println("📋 IconCache: Scheduling preload for icon '$key'")
                }
            }
        }
    }
}

/**
 * 缓存统计信息
 */
data class CacheStats(
    val totalItems: Int,
    val maxSize: Int,
    val maxAge: Long,
    val oldestTimestamp: Long,
    val newestTimestamp: Long,
    val totalAccessCount: Int
) {
    /**
     * 缓存使用率
     */
    val usageRatio: Float = totalItems.toFloat() / maxSize
    
    /**
     * 平均访问次数
     */
    val averageAccessCount: Float = if (totalItems > 0) totalAccessCount.toFloat() / totalItems else 0f
}

/**
 * 扩展函数：为TagBean提供积分图标缓存
 */
@Composable
fun TagBean.getCachedPointsIcon(
    imageLoader: TagImageLoader?,
    defaultIcon: Painter? = null
): Painter? {
    return if (type == TagType.POINTS && !imageUrl.isNullOrBlank()) {
        IconCache.getPointsIcon(imageUrl, imageLoader, defaultIcon)
    } else {
        defaultIcon
    }
}
