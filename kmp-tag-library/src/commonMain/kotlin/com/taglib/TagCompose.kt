package com.taglib

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.components.*

/**
 * 标签组合组件 - 主要入口点
 *
 * 使用原生风格的实现，基于InlineTextContent模拟Android原生的SpannableString效果
 *
 * @param tags 标签列表
 * @param text 文字内容
 * @param textStyle 文字样式
 * @param showTagsAtStart 标签是否在前面（true: 标签在前，false: 文字在前）
 * @param onTagClick 标签点击回调
 * @param arrowIcon 箭头图标
 * @param imagePainters 图片画笔映射
 * @param loadingContent 加载中内容
 * @param errorContent 错误内容
 * @param forceTagHeight 是否强制标签高度
 * @param maxLines 最大行数
 * @param overflow 文字溢出处理方式
 * @param modifier 修饰符
 */
@Composable
fun TagGroup(
    tags: List<TagBean>,
    text: String = "",
    textStyle: TextStyle = MaterialTheme.typography.bodyMedium,
    showTagsAtStart: Boolean = true,
    onTagClick: ((TagBean) -> Unit)? = null,
    arrowIcon: ImageVector? = null,
    imagePainters: Map<String, Painter> = emptyMap(),
    loadingContent: @Composable (() -> Unit)? = null,
    errorContent: @Composable (() -> Unit)? = null,
    forceTagHeight: Boolean = false,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip,
    modifier: Modifier = Modifier
) {
    // 早期返回：如果没有内容，不渲染任何东西
    if (tags.isEmpty() && text.isBlank()) return

    // 早期返回：如果只有文字，直接显示文字
    if (tags.isEmpty()) {
        BasicText(
            text = text,
            style = textStyle,
            maxLines = maxLines,
            overflow = overflow,
            modifier = modifier
        )
        return
    }

    // 预处理标签数据（合并重复逻辑）
    val processedTags = remember(tags) {
        TagUtils.processTagList(tags).map { tag ->
            if (tag.appearance == null) {
                tag.copy(appearance = TagAppearance.Default)
            } else {
                tag
            }
        }
    }

    // 处理标签高度逻辑
    val density = LocalDensity.current.density
    val (adjustedTextStyle, adjustedTags) = remember(processedTags, textStyle, forceTagHeight, density) {
        processTagHeightLogic(processedTags, textStyle, forceTagHeight, density)
    }

    // 使用原生风格的混合文本布局
    MixedStyleText(
        tags = adjustedTags,
        text = text,
        showTagsAtStart = showTagsAtStart,
        maxLines = maxLines,
        overflow = overflow,
        textStyle = adjustedTextStyle,
        onTagClick = onTagClick,
        arrowIcon = arrowIcon,
        imagePainters = imagePainters,
        loadingContent = loadingContent,
        errorContent = errorContent,
        modifier = modifier
    )
}

/**
 * 处理标签高度逻辑
 *
 * 实现原生版本的三个核心规则：
 * 1. 外部文字高度大于设置的固定标签高度 → 显示标签的固定高度
 * 2. 外部文字高度小于设置的固定标签高度 → 标签高度随文字高度自适应
 * 3. 若强制设置标签高度 → 调整外部文字大小兼容处理
 */
private fun processTagHeightLogic(
    tags: List<TagBean>,
    textStyle: TextStyle,
    forceTagHeight: Boolean,
    density: Float
): Pair<TextStyle, List<TagBean>> {
    if (tags.isEmpty()) return textStyle to tags

    val appearance = tags.first().appearance
    val tagHeightDp = appearance.tagHeight.value

    // 如果没有设置固定标签高度，直接返回
    if (tagHeightDp <= 0) return textStyle to tags

    val currentTextSizeSp = textStyle.fontSize.value
    val needsAdjustment = TagUtils.needAdjustTextSize(tagHeightDp, currentTextSizeSp, density)

    // 根据规则决定是否使用固定高度
    val useFixedHeight = !needsAdjustment || forceTagHeight
    val adjustedTags = if (useFixedHeight) {
        tags.map { it.copy(useFixedHeight = true) }
    } else {
        tags
    }

    // 规则3：强制固定高度且需要调整文字大小
    val adjustedTextStyle = if (forceTagHeight && needsAdjustment) {
        val adjustedTextSize = TagUtils.adjustTextSize(currentTextSizeSp, tagHeightDp, density)
        textStyle.copy(fontSize = adjustedTextSize.sp)
    } else {
        textStyle
    }

    return adjustedTextStyle to adjustedTags
}



/**
 * 原生风格的混合文本组件
 *
 * 核心思路：模拟Android原生的SpannableString方式
 * 1. 构建完整的文本内容（标签占位符 + 文字）
 * 2. 使用BasicText组件的自动换行能力（类似TextView）
 * 3. 通过InlineContent在占位符位置插入标签组件
 */
@Composable
private fun MixedStyleText(
    tags: List<TagBean>,
    text: String,
    showTagsAtStart: Boolean,
    maxLines: Int,
    overflow: TextOverflow,
    textStyle: TextStyle,
    onTagClick: ((TagBean) -> Unit)?,
    arrowIcon: ImageVector?,
    imagePainters: Map<String, Painter>,
    loadingContent: @Composable (() -> Unit)?,
    errorContent: @Composable (() -> Unit)?,
    modifier: Modifier
) {
    // 构建混合内容
    val (annotatedText, inlineContentMap) = remember(tags, text, showTagsAtStart) {
        buildNativeStyleContent(tags, text, showTagsAtStart)
    }

    // 创建内联内容映射
    val inlineContent = remember(inlineContentMap, onTagClick, arrowIcon, imagePainters, loadingContent, errorContent) {
        inlineContentMap.mapValues { (_, tagBean) ->
            InlineTextContent(
                placeholder = Placeholder(
                    width = calculateTagWidth(tagBean),
                    height = calculateTagHeight(tagBean),
                    placeholderVerticalAlign = PlaceholderVerticalAlign.Center
                )
            ) {
                SingleTag(
                    tagBean = tagBean,
                    onTagClick = onTagClick,
                    arrowIcon = arrowIcon,
                    imagePainter = tagBean.imageUrl?.let { imagePainters[it] },
                    loadingContent = loadingContent,
                    errorContent = errorContent
                )
            }
        }
    }

    // 使用BasicText组件的自动换行能力（类似TextView）
    BasicText(
        text = annotatedText,
        style = textStyle,
        maxLines = maxLines,
        overflow = overflow,
        inlineContent = inlineContent,
        modifier = modifier
    )
}

/**
 * 单个标签组件
 */
@Composable
private fun SingleTag(
    tagBean: TagBean,
    onTagClick: ((TagBean) -> Unit)?,
    arrowIcon: ImageVector?,
    imagePainter: Painter?,
    loadingContent: @Composable (() -> Unit)?,
    errorContent: @Composable (() -> Unit)?
) {
    when (tagBean.type) {
        TagType.FILL, TagType.FILL_AND_STROKE -> {
            FillTag(
                tagBean = tagBean,
                onClick = onTagClick,
                arrowIcon = arrowIcon
            )
        }
        TagType.STROKE -> {
            StrokeTag(
                tagBean = tagBean,
                onClick = onTagClick,
                arrowIcon = arrowIcon
            )
        }
        TagType.IMAGE -> {
            ImageTag(
                tagBean = tagBean,
                imagePainter = imagePainter,
                loadingContent = loadingContent,
                errorContent = errorContent,
                onClick = onTagClick,
                arrowIcon = arrowIcon
            )
        }
        TagType.DISCOUNT -> {
            DiscountTag(
                tagBean = tagBean,
                onClick = onTagClick,
                arrowIcon = arrowIcon
            )
        }
        TagType.POINTS -> {
            PointsTag(
                tagBean = tagBean,
                iconPainter = imagePainter, // 可以复用imagePainter作为积分图标
                onClick = onTagClick,
                arrowIcon = arrowIcon
            )
        }
    }
}

/**
 * 计算标签宽度 - 使用TagUtils的计算方法
 */
private fun calculateTagWidth(tagBean: TagBean): androidx.compose.ui.unit.TextUnit {
    return TagUtils.calculateTagWidth(tagBean).sp
}

/**
 * 计算标签高度
 * 处理负数高度的情况
 */
private fun calculateTagHeight(tagBean: TagBean): TextUnit {
    val appearance = tagBean.appearance

    return if (appearance.tagHeight.value > 0) {
        // 使用设定的标签高度
        appearance.tagHeight.value.sp
    } else {
        // 自适应高度：基于文字大小 + 垂直内边距
        (appearance.textSize.value + appearance.verticalPadding.value * 2).sp
    }
}

/**
 * 构建原生风格的混合内容
 *
 * 模拟Android原生TagUtils.showTag的逻辑：
 * 1. 根据showTagsAtStart决定标签和文字的位置
 * 2. 为每个标签创建占位符
 * 3. 构建完整的AnnotatedString
 * 4. 使用标签的textSpacing设置间距
 */
private fun buildNativeStyleContent(
    tags: List<TagBean>,
    text: String,
    showTagsAtStart: Boolean
): Pair<AnnotatedString, Map<String, TagBean>> {
    val builder = AnnotatedString.Builder()
    val inlineContentMap = mutableMapOf<String, TagBean>()

    val hasText = text.isNotBlank()
    val firstAppearance = tags.firstOrNull()?.appearance
    val tagSpacing = firstAppearance?.tagSpacing?.value?.toInt() ?: 1
    val textSpacing = firstAppearance?.textSpacing?.value?.toInt() ?: 1

    // 添加标签的辅助函数
    fun addTags() {
        tags.forEachIndexed { index, tag ->
            val placeholderId = "tag_$index"
            builder.appendInlineContent(placeholderId, tag.text)
            inlineContentMap[placeholderId] = tag

            // 标签之间添加间距
            if (index < tags.size - 1) {
                repeat(tagSpacing) { builder.append(" ") }
            }
        }
    }

    // 添加文字和标签之间的间距
    fun addTextSpacing() {
        if (tags.isNotEmpty() && hasText) {
            repeat(textSpacing) { builder.append(" ") }
        }
    }

    if (showTagsAtStart) {
        // 标签在前：标签 → 间距 → 文字
        addTags()
        addTextSpacing()
        if (hasText) builder.append(text)
    } else {
        // 标签在后：文字 → 间距 → 标签
        if (hasText) builder.append(text)
        addTextSpacing()
        addTags()
    }

    return builder.toAnnotatedString() to inlineContentMap
}



/**
 * 便捷方法 - 模拟Android原生的showRectStart等方法
 */
@Composable
fun ShowRectStart(
    tags: List<TagBean>,
    content: String,
    maxLines: Int = Int.MAX_VALUE,
    onTagClick: ((TagBean) -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    TagGroup(
        tags = tags,
        text = content,
        showTagsAtStart = true,
        maxLines = maxLines,
        onTagClick = onTagClick,
        modifier = modifier
    )
}

@Composable
fun ShowRectEnd(
    tags: List<TagBean>,
    content: String,
    maxLines: Int = Int.MAX_VALUE,
    onTagClick: ((TagBean) -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    TagGroup(
        tags = tags,
        text = content,
        showTagsAtStart = false,
        maxLines = maxLines,
        onTagClick = onTagClick,
        modifier = modifier
    )
}

/**
 * 扩展函数 - 更简洁的API
 */
@Composable
fun List<TagBean>.showRectStart(
    content: String,
    maxLines: Int = Int.MAX_VALUE,
    onTagClick: ((TagBean) -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    ShowRectStart(this, content, maxLines, onTagClick, modifier)
}

@Composable
fun List<TagBean>.showRectEnd(
    content: String,
    maxLines: Int = Int.MAX_VALUE,
    onTagClick: ((TagBean) -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    ShowRectEnd(this, content, maxLines, onTagClick, modifier)
}

// ==================== 原生风格宽度计算函数 ====================


