package com.taglib

import androidx.compose.runtime.*
import androidx.compose.ui.graphics.painter.Painter
import kotlinx.coroutines.delay

/**
 * 图片加载验证器
 * 
 * 对应原生库的poseSetImgText和setTextWithClearTag机制，
 * 用于防止在列表场景中图片异步加载导致的显示错乱问题。
 * 
 * 核心问题：
 * 在RecyclerView等列表组件中，当用户快速滚动时，ViewHolder会被复用。
 * 如果某个ViewHolder开始加载图片A，但在图片A加载完成前，这个ViewHolder
 * 被复用去显示其他内容，那么当图片A加载完成时，就会错误地显示在新内容上。
 * 
 * 解决方案：
 * 1. 为每个图片加载请求生成唯一标识
 * 2. 在图片加载完成时验证标识是否仍然有效
 * 3. 只有验证通过的图片才会被显示
 * 
 * <AUTHOR> Tag Library Team
 * @since 1.0.0
 */
object ImageLoadingValidator {

    /**
     * 图片加载状态
     */
    sealed class LoadingState {
        object Loading : LoadingState()
        data class Success(val painter: Painter) : LoadingState()
        data class Error(val throwable: Throwable) : LoadingState()
    }

    /**
     * 图片加载验证信息
     */
    data class LoadingToken(
        val url: String,
        val timestamp: Long = getCurrentTimeMillis(),
        val requestId: String = "${url}_${timestamp}_${(kotlin.random.Random.nextInt(1000))}"
    )

    /**
     * 验证图片加载的Composable
     *
     * 适配新的回调机制：使用ImageCallback处理异步加载
     *
     * @param url 图片URL
     * @param imageLoader 图片加载器
     * @param placeholder 占位图
     * @param error 错误图
     * @param onStateChange 状态变化回调
     * @return 当前的加载状态
     */
    @Composable
    fun ValidatedImageLoader(
        url: String,
        imageLoader: TagImageLoader?,
        placeholder: Painter? = null,
        error: Painter? = null,
        onStateChange: ((LoadingState) -> Unit)? = null
    ): LoadingState {
        // 为当前加载请求生成唯一token
        val currentToken = remember(url) { LoadingToken(url) }

        // 加载状态
        var loadingState by remember(url) { mutableStateOf<LoadingState>(LoadingState.Loading) }

        // 激活token
        LaunchedEffect(currentToken) {
            activateToken(currentToken)
        }

        // 清理：当组件被销毁时取消加载
        DisposableEffect(currentToken) {
            onDispose {
                invalidateToken(currentToken)
            }
        }

        // 参数验证
        if (url.isBlank()) {
            val errorState = LoadingState.Error(IllegalArgumentException("Empty URL"))
            LaunchedEffect(Unit) {
                loadingState = errorState
                onStateChange?.invoke(errorState)
            }
            return errorState
        }

        if (imageLoader == null) {
            val errorState = LoadingState.Error(IllegalStateException("ImageLoader is null"))
            LaunchedEffect(Unit) {
                loadingState = errorState
                onStateChange?.invoke(errorState)
            }
            return errorState
        }

        // 启动图片加载
        LaunchedEffect(currentToken) {
            // 验证token是否有效
            if (!isTokenValid(currentToken, url)) {
                val errorState = LoadingState.Error(IllegalStateException("Token invalid"))
                loadingState = errorState
                onStateChange?.invoke(errorState)
                return@LaunchedEffect
            }

            // 使用新的回调机制加载图片
            imageLoader.loadImage(url, object : ImageCallback {
                override fun onBitmapReady(painter: Painter?) {
                    // 再次验证token（防止异步加载完成时token已失效）
                    if (isTokenValid(currentToken, url)) {
                        val successState = if (painter != null) {
                            LoadingState.Success(painter)
                        } else {
                            LoadingState.Error(IllegalStateException("Painter is null"))
                        }
                        loadingState = successState
                        onStateChange?.invoke(successState)
                    }
                }

                override fun onFail(failPainter: Painter?) {
                    // 再次验证token
                    if (isTokenValid(currentToken, url)) {
                        val errorState = LoadingState.Error(IllegalStateException("Image loading failed"))
                        loadingState = errorState
                        onStateChange?.invoke(errorState)
                    }
                }
            })
        }

        return loadingState
    }

    /**
     * 简化版本的验证图片加载
     *
     * 直接返回Painter，内部处理验证逻辑
     *
     * @param url 图片URL
     * @param imageLoader 图片加载器
     * @param placeholder 占位图
     * @param error 错误图
     * @return 验证后的Painter，可能为null
     */
    @Composable
    fun loadValidatedImage(
        url: String,
        imageLoader: TagImageLoader?,
        placeholder: Painter? = null,
        error: Painter? = null
    ): Painter? {
        val loadingState = ValidatedImageLoader(url, imageLoader, placeholder, error)

        return when (loadingState) {
            is LoadingState.Loading -> placeholder
            is LoadingState.Success -> loadingState.painter
            is LoadingState.Error -> error
        }
    }

    // ==================== 内部验证机制 ====================

    /**
     * 活跃的token集合
     * 用于跟踪当前有效的加载请求
     */
    private val activeTokens = mutableSetOf<String>()

    /**
     * 验证token是否仍然有效
     *
     * @param token 加载token
     * @param currentUrl 当前URL
     * @return 是否有效
     */
    private fun isTokenValid(token: LoadingToken, currentUrl: String): Boolean {
        return activeTokens.contains(token.requestId) && token.url == currentUrl
    }

    /**
     * 使token失效
     *
     * @param token 要失效的token
     */
    private fun invalidateToken(token: LoadingToken) {
        activeTokens.remove(token.requestId)
    }

    /**
     * 激活token
     *
     * @param token 要激活的token
     */
    private fun activateToken(token: LoadingToken) {
        activeTokens.add(token.requestId)
    }

    /**
     * 清理过期的token
     *
     * 定期清理超过指定时间的token，防止内存泄漏
     *
     * @param maxAge 最大存活时间（毫秒）
     */
    fun cleanupExpiredTokens(maxAge: Long = 5 * 60 * 1000) { // 默认5分钟
        val currentTime = getCurrentTimeMillis()
        val expiredTokens = activeTokens.filter { tokenId ->
            val timestamp = tokenId.split("_").getOrNull(1)?.toLongOrNull() ?: 0
            currentTime - timestamp > maxAge
        }
        activeTokens.removeAll(expiredTokens.toSet())
    }
}

/**
 * 扩展函数：为TagBean提供验证图片加载
 */
@Composable
fun TagBean.loadValidatedImage(
    imageLoader: TagImageLoader?,
    placeholder: Painter? = null,
    error: Painter? = null
): Painter? {
    return if (type == TagType.IMAGE && !imageUrl.isNullOrBlank()) {
        ImageLoadingValidator.loadValidatedImage(imageUrl, imageLoader, placeholder, error)
    } else {
        null
    }
}