package com.taglib

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * AppTag配置类
 * 对应原生Android项目中的AppTag配置
 * 提供KMP Compose版本的标签库初始化和使用方法
 */
object AppTag {

    private var isInitialized = false
    private var imageLoader: TagImageLoader? = null

    /**
     * 初始化标签库
     * 对应原生的TagUtils.initNetLoader(context, loader, debug)
     *
     * @param loader 图片加载器实现，对应原生的INetPicLoader
     * @param debug 是否开启调试模式
     */
    fun init(loader: TagImageLoader? = null, debug: Boolean = false) {
        try {
            // 设置图片加载器到全局管理器（组件库通过ImageLoaderManager获取）
            imageLoader = loader ?: EmptyTagImageLoader()
            ImageLoaderManager.initializeCompose(imageLoader)

            // 设置调试模式
            TagUtils.isDebugMode = debug

            // 设置全局默认样式
            // 对应原生: tagHeight(15F).tagTextSize(11F).cornerSizeDp(2f)
            val defaultAppearance = TagAppearance(
                tagHeight = 15.dp,
                textSize = 11.sp,
                cornerRadius = 2.dp,
                borderWidth = 0.5.dp,
                horizontalPadding = 6.dp,
                verticalPadding = 2.dp,
                tagSpacing = 4.dp,
                textSpacing = 4.dp
            )

            TagUtils.setDefaultAppearance(defaultAppearance)

            // 设置默认圆角样式
            val defaultRoundAppearance = TagAppearance(
                tagHeight = 15.dp,
                textSize = 11.sp,
                cornerRadius = 12.dp,
                borderWidth = 0.5.dp,
                horizontalPadding = 6.dp,
                verticalPadding = 2.dp
            )

            TagUtils.setDefaultRoundAppearance(defaultRoundAppearance)

            isInitialized = true

            if (debug) {
                println("✅ AppTag: Initialized successfully with ${if (loader != null) "custom" else "default"} image loader")
            }
        } catch (e: Exception) {
            if (debug) {
                println("❌ AppTag: Initialization failed: ${e.message}")
            }
            throw e
        }
    }

    /**
     * 显示标签组合
     * 对应原生的showTags方法
     *
     * @param tags 商品标签列表
     * @param content 内容文字
     * @param showTagsAtStart 标签是否显示在开头
     * @param onTagClick 标签点击回调
     * @return TagGroup Composable组件
     */
    @Composable
    fun ShowTags(
        tags: List<GoodsTag?>?,
        content: String = "",
        showTagsAtStart: Boolean = true,
        onTagClick: ((TagBean) -> Unit)? = null
    ) {
        if (!isInitialized) {
            init() // 自动初始化
        }

        val convertedTags = convertTagList(tags)
        if (convertedTags.isNotEmpty()) {
            TagGroup(
                tags = convertedTags,
                text = content,
                showTagsAtStart = showTagsAtStart,
                onTagClick = onTagClick
            )
        } else if (content.isNotBlank()) {
            // 如果没有标签但有内容，直接显示文字
            Text(text = content)
        }
    }

    /**
     * 显示标签（通用方法）
     * 对应原生的AppTag.showTag()方法
     *
     * @param tags 标签列表
     * @param text 文字内容
     * @param showTagsAtStart 标签是否显示在文字前面
     * @param onTagClick 标签点击回调
     * @param textStyle 文字样式
     * @param maxLines 最大行数
     * @param overflow 文字溢出处理方式
     * @param forceTagHeight 是否强制标签高度
     * @param modifier 修饰符
     */
    @Composable
    fun showTag(
        tags: List<TagBean>,
        text: String = "",
        showTagsAtStart: Boolean = true,
        onTagClick: ((TagBean) -> Unit)? = null,
        textStyle: TextStyle = MaterialTheme.typography.bodyMedium,
        maxLines: Int = Int.MAX_VALUE,
        overflow: TextOverflow = TextOverflow.Clip,
        forceTagHeight: Boolean = false,
        modifier: Modifier = Modifier
    ) {
        if (!isInitialized) {
            init() // 自动初始化
        }

        TagGroup(
            tags = tags,
            text = text,
            textStyle = textStyle,
            showTagsAtStart = showTagsAtStart,
            onTagClick = onTagClick,
            forceTagHeight = forceTagHeight,
            maxLines = maxLines,
            overflow = overflow,
            modifier = modifier
        )
    }

    /**
     * 打标数据结构转换，GoodsTag转TagBean
     * 对应原生的convertTagList方法
     */
    private fun convertTagList(tags: List<GoodsTag?>?): List<TagBean> {
        if (tags.isNullOrEmpty()) return emptyList()

        return tags.mapNotNull { goodsTag ->
            goodsTag?.let {
                TagBean(
                    type = compatibleTagType(it.form ?: 0),
                    text = it.name ?: "",
                    textColor = TagUtils.parseColor(it.color, Color.Black),
                    backgroundColor = TagUtils.parseColor(it.bgcolor, Color.White),
                    backgroundEndColor = if (!it.bgGraduallyColor.isNullOrBlank()) {
                        TagUtils.parseColor(it.bgGraduallyColor)
                    } else null,
                    borderColor = TagUtils.parseColor(it.bordercolor, Color.Black),
                    imageUrl = it.rlink,
                    appearance = TagUtils.getDefaultAppearance()
                )
            }
        }
    }

    /**
     * 兼容API与组件库的标签类型一致
     * 对应原生的compatible方法
     */
    private fun compatibleTagType(originForm: Int): TagType {
        return when (originForm) {
            1 -> TagType.FILL                    // 填充背景
            2 -> TagType.IMAGE                   // 图片标签
            3 -> TagType.STROKE                  // 镂空边框
            4 -> TagType.DISCOUNT                // 折省标签
            5 -> TagType.POINTS                  // 积分标签
            6, -1 -> TagType.FILL_AND_STROKE     // 填充+描边
            else -> TagType.FILL                 // 默认填充
        }
    }

    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized

    /**
     * 获取当前图片加载器
     */
    fun getImageLoader(): TagImageLoader? = imageLoader
}

/**
 * 商品标签数据类
 * 对应原生项目中的GoodsTag
 */
data class GoodsTag(
    val form: Int? = null,              // 标签类型
    val name: String? = null,           // 标签名称
    val color: String? = null,          // 文字颜色
    val bgcolor: String? = null,        // 背景颜色
    val bgGraduallyColor: String? = null, // 渐变背景颜色
    val bordercolor: String? = null,    // 边框颜色
    val rlink: String? = null           // 图片链接
)



/**
 * 扩展函数：快速创建常用商品标签
 */
object GoodsTagFactory {

    /**
     * 创建新品标签
     */
    fun newTag(): GoodsTag {
        return GoodsTag(
            form = 1,
            name = "新品",
            color = "#FFFFFF",
            bgcolor = "#FF0000"
        )
    }

    /**
     * 创建热销标签
     */
    fun hotTag(): GoodsTag {
        return GoodsTag(
            form = 1,
            name = "热销",
            color = "#FFFFFF",
            bgcolor = "#FF9800"
        )
    }

    /**
     * 创建包邮标签
     */
    fun freeShippingTag(): GoodsTag {
        return GoodsTag(
            form = 3,
            name = "包邮",
            color = "#4CAF50",
            bordercolor = "#4CAF50"
        )
    }

    /**
     * 创建折扣标签
     */
    fun discountTag(text: String): GoodsTag {
        return GoodsTag(
            form = 4,
            name = text,
            color = "#FFFFFF",
            bgcolor = "#E91E63",
            bgGraduallyColor = "#FF5722"
        )
    }

    /**
     * 创建积分标签
     */
    fun pointsTag(points: String): GoodsTag {
        return GoodsTag(
            form = 5,
            name = points,
            color = "#FFFFFF",
            bgcolor = "#9C27B0",
            bgGraduallyColor = "#673AB7"
        )
    }
}
