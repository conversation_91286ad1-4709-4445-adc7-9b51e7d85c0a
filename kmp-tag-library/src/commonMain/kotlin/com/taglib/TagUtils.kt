package com.taglib

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.dp

/**
 * 标签工具类
 *
 * 这是KMP Tag Library的核心工具类，提供了标签处理所需的各种工具方法。
 * 对应原生Android版本的TagUtils类，包含所有核心功能的Compose适配版本。
 *
 * 主要功能模块：
 * 1. 颜色处理：颜色解析、转换、对比度计算
 * 2. 文字测量：文字宽度测量、高度计算、缓存管理
 * 3. 布局计算：基线调整、行高校正、尺寸计算
 * 4. 数据处理：标签验证、列表处理、类型转换
 * 5. 样式管理：默认样式设置、样式获取
 *
 * 核心特性：
 * - 高性能的文字测量缓存机制
 * - 安全的颜色解析，支持多种格式
 * - 智能的对比度计算，确保可读性
 * - 完整的标签数据验证
 * - 灵活的样式管理系统
 *
 * 使用示例：
 * ```kotlin
 * // 颜色解析
 * val color = TagUtils.parseColor("#FF0000", Color.Red)
 *
 * // 文字测量
 * val width = TagUtils.measureTextWidth("标签文字", 12f)
 *
 * // 标签验证
 * val isValid = TagUtils.validateTagBean(tagBean)
 *
 * // 处理标签列表
 * val processedTags = TagUtils.processTagList(originalTags)
 * ```
 *
 * <AUTHOR> Tag Library Team
 * @since 1.0.0
 */
object TagUtils {

    /**
     * 调试模式开关
     * 对应原生版本的isDebug字段
     */
    var isDebugMode: Boolean = false

    /**
     * 特殊颜色标志位
     * 对应原生库的COLOR_NONE，用于标识无颜色状态
     */
    const val COLOR_NONE_VALUE = 1L

    /**
     * 安全解析颜色字符串
     * @param colorString 颜色字符串，支持 #RRGGBB 和 #AARRGGBB 格式
     * @param defaultColor 默认颜色
     * @return 解析后的Color对象
     */
    fun parseColor(colorString: String?, defaultColor: Color = Color.Black): Color {
        if (colorString.isNullOrBlank()) return defaultColor

        return try {
            val cleanColor = colorString.trim()
            when {
                cleanColor.startsWith("#") -> {
                    val hex = cleanColor.substring(1)
                    when (hex.length) {
                        6 -> {
                            // #RRGGBB
                            val rgb = hex.toLong(16)
                            Color(
                                red = ((rgb shr 16) and 0xFF) / 255f,
                                green = ((rgb shr 8) and 0xFF) / 255f,
                                blue = (rgb and 0xFF) / 255f,
                                alpha = 1f
                            )
                        }
                        8 -> {
                            // #AARRGGBB
                            val argb = hex.toLong(16)
                            Color(
                                alpha = ((argb shr 24) and 0xFF) / 255f,
                                red = ((argb shr 16) and 0xFF) / 255f,
                                green = ((argb shr 8) and 0xFF) / 255f,
                                blue = (argb and 0xFF) / 255f
                            )
                        }
                        else -> defaultColor
                    }
                }
                else -> defaultColor
            }
        } catch (e: Exception) {
            defaultColor
        }
    }

    /**
     * 解析渐变背景色终止颜色
     * 对应原生库的getBgEndColor方法
     *
     * 特殊处理逻辑：
     * - 如果颜色值解析结果等于COLOR_NONE，则修改为COLOR_NONE+1避免冲突
     * - 空字符串返回null表示无渐变
     *
     * @param bgColorEnd 背景结束颜色字符串
     * @param defaultColor 默认颜色
     * @return 解析后的Color对象，null表示无渐变
     */
    fun parseEndColor(bgColorEnd: String?, defaultColor: Color = Color.White): Color? {
        if (bgColorEnd.isNullOrBlank()) {
            return null // 无渐变
        }

        val parsedColor = parseColor(bgColorEnd, defaultColor)

        // 使用统一的兼容性处理方法
        return processColorForCompatibility(parsedColor)
    }

    /**
     * 处理Color对象的兼容性问题
     * 检查是否与COLOR_NONE冲突，如果冲突则微调
     *
     * @param color 要处理的颜色
     * @return 处理后的颜色，确保不与COLOR_NONE冲突
     */
    fun processColorForCompatibility(color: Color): Color {
        val colorValue = color.toArgb().toLong() and 0xFFFFFFFFL
        return if (colorValue == COLOR_NONE_VALUE) {
            // 避免与COLOR_NONE冲突，微调颜色值
            color.copy(
                alpha = if (color.alpha == 1f) 0.999f else color.alpha
            )
        } else {
            color
        }
    }
    

    

    


    /**
     * 文字宽度测量缓存
     */
    private val textWidthCache = mutableMapOf<String, Float>()
    private const val MAX_CACHE_SIZE = 1000 // 最大缓存条目数

    /**
     * 测量文字宽度（带缓存）
     * 对应原生库的measureText方法
     */
    fun measureTextWidth(text: String, textSize: Float): Float {
        if (text.isBlank()) return 0f

        val cacheKey = "${text}_$textSize"
        return textWidthCache.getOrPut(cacheKey) {
            // 检查缓存大小，防止内存泄漏
            if (textWidthCache.size >= MAX_CACHE_SIZE) {
                clearOldCacheEntries()
            }

            // 这里应该使用实际的文字测量方法
            // 在Compose中可以使用TextMeasurer
            calculateTextWidth(text, textSize)
        }
    }

    /**
     * 计算文字宽度的实际实现
     */
    private fun calculateTextWidth(text: String, textSize: Float): Float {
        // 简化计算，实际应该根据字体度量计算
        // 中文字符通常比英文字符宽
        var width = 0f
        for (char in text) {
            width += when {
                char.code > 127 -> textSize * 0.9f // 中文字符
                char.isDigit() -> textSize * 0.5f // 数字
                char.isLetter() -> textSize * 0.6f // 英文字母
                else -> textSize * 0.4f // 其他字符
            }
        }
        return width
    }

    /**
     * 清除旧的缓存条目
     */
    private fun clearOldCacheEntries() {
        if (textWidthCache.size > MAX_CACHE_SIZE * 0.8) {
            // 清除一半的缓存条目
            val toRemove = textWidthCache.keys.take(textWidthCache.size / 2)
            toRemove.forEach { textWidthCache.remove(it) }
        }
    }



    /**
     * 获取文字高度
     * 对应原生库的getTextHeight方法
     */
    fun getTextHeight(textSize: Float): Float {
        return textSize * 1.2f // 简化计算，实际应该根据字体度量计算
    }

    /**
     * 调整基线位置
     * 对应原生库的adjustBaseLine方法
     */
    fun adjustBaseLine(
        originalY: Float,
        containerHeight: Float,
        textHeight: Float
    ): Float {
        val offset1 = containerHeight / 2f
        val offset2 = textHeight / 2f
        return originalY - offset1 + offset2
    }

    /**
     * 检查并校正行高
     * 对应原生库的checkLineFM方法
     */
    fun checkLineHeight(
        currentHeight: Float,
        minHeight: Float
    ): Float {
        return if (currentHeight < minHeight) minHeight else currentHeight
    }





    /**
     * 调整文字大小以匹配标签高度
     * 对应原生库的adjustTextViewSize方法
     *
     * 当标签高度大于文字高度时，自动调整文字大小以保持视觉平衡。
     * 这在固定标签高度的场景中特别有用。
     *
     * @param originalTextSize 原始文字大小（sp）
     * @param tagHeightDp 标签高度（dp）
     * @param density 屏幕密度
     * @return 调整后的文字大小（sp）
     */
    fun adjustTextSize(
        originalTextSize: Float,
        tagHeightDp: Float,
        density: Float = 1f
    ): Float {
        var adjustedTextSize = originalTextSize

        // 最大调整次数，防止无限循环
        var adjustCount = 0
        val maxAdjustCount = 20

        while (needAdjustTextSize(tagHeightDp, adjustedTextSize, density) && adjustCount < maxAdjustCount) {
            adjustedTextSize += 1f
            adjustCount++
        }

        return adjustedTextSize
    }

    /**
     * 判断是否需要调整文字大小
     * 对应原生库的needAdjustTxtSize方法
     *
     * @param tagHeightDp 标签高度（dp）
     * @param textSizeSp 文字大小（sp）
     * @param density 屏幕密度
     * @return 是否需要调整
     */
    fun needAdjustTextSize(
        tagHeightDp: Float,
        textSizeSp: Float,
        density: Float = 1f
    ): Boolean {
        if (tagHeightDp <= 0) return false

        // 计算文字实际高度（px）
        val textHeightPx = getTextHeight(textSizeSp * density)

        // 计算标签高度（px）
        val tagHeightPx = tagHeightDp * density

        return tagHeightPx > textHeightPx
    }

    /**
     * 计算最佳文字大小
     * 根据标签高度和内边距计算最适合的文字大小
     *
     * @param tagHeightDp 标签高度（dp）
     * @param verticalPaddingDp 垂直内边距（dp）
     * @param density 屏幕密度
     * @return 最佳文字大小（sp）
     */
    fun calculateOptimalTextSize(
        tagHeightDp: Float,
        verticalPaddingDp: Float = 2f,
        density: Float = 1f
    ): Float {
        if (tagHeightDp <= 0) return 12f // 默认大小

        // 可用于文字的高度
        val availableHeightPx = (tagHeightDp - verticalPaddingDp * 2) * density

        // 根据可用高度计算文字大小
        // 文字高度通常是文字大小的1.2倍左右
        val estimatedTextSizePx = availableHeightPx / 1.2f

        return (estimatedTextSizePx / density).coerceAtLeast(8f).coerceAtMost(24f)
    }

    // ==================== 标签宽度计算方法 - 模拟原生组件 ====================

    /**
     * 计算标签宽度 - 完全模拟原生Android组件的getSize()方法
     */
    fun calculateTagWidth(tagBean: TagBean): Float {
        return when (tagBean.type) {
            TagType.IMAGE -> calculateImageTagWidth(tagBean)
            TagType.POINTS -> calculateJFSpanWidth(tagBean)
            TagType.DISCOUNT -> calculateZSBgTagWidth(tagBean)
            TagType.FILL_AND_STROKE -> calculateFillAndStrokeSpanWidth(tagBean)
            else -> calculateFillStrokeSpanWidth(tagBean)
        }
    }

    /**
     * 计算图片标签宽度 - 精确模拟TagImageSpan.getSize()
     */
    private fun calculateImageTagWidth(tagBean: TagBean): Float {
        val appearance = tagBean.appearance

        // 模拟原生：Rect rect = getDrawable().getBounds();
        val originalWidth = 24f
        val originalHeight = 24f
        val textHeight = appearance.textSize.value * 1.2f

        var rectLeft = 0f
        var rectTop = 0f
        var rectRight = originalWidth
        var rectBottom = originalHeight

        // 🎯 模拟原生的图片高度限制处理
        if (appearance.tagHeight.value > 0 && tagBean.useFixedHeight) {
            // 情况1：固定标签高度
            val targetHeightPx = appearance.tagHeight.value
            if ((rectBottom - rectTop) != targetHeightPx) {
                val scale = targetHeightPx / (rectBottom - rectTop)
                rectRight = rectLeft + (rectRight - rectLeft) * scale
                rectBottom = rectTop + (rectBottom - rectTop) * scale
            }
        } else {
            // 情况2：自适应高度，基于imgHeightLimit
            val targetHeight = appearance.imageHeightRatio * textHeight
            if ((rectBottom - rectTop) != targetHeight) {
                val scale = targetHeight / (rectBottom - rectTop)
                rectRight = rectLeft + (rectRight - rectLeft) * scale
                rectBottom = rectTop + (rectBottom - rectTop) * scale
            }
        }

        // 🎯 模拟原生：int spanWidth = rect.right - rect.left;
        var spanWidth = rectRight - rectLeft
        spanWidth += calculateTagSpacing(tagBean, appearance)

        return spanWidth
    }

    /**
     * 计算积分标签宽度 - 精确模拟JFSpan.getSize()
     */
    private fun calculateJFSpanWidth(tagBean: TagBean): Float {
        val appearance = tagBean.appearance

        val realTagTextSize = getTagTextSize(appearance, tagBean.useFixedHeight)
        val paddingV = getTagTextPaddingV(appearance, realTagTextSize)
        val paddingH = getTagTextPaddingH(appearance, realTagTextSize)

        var frameHeight = realTagTextSize * 1.2f + 2 * paddingV

        if (appearance.tagHeight.value > 0 && tagBean.useFixedHeight) {
            frameHeight = appearance.tagHeight.value
        }

        // 🎯 模拟JFSpan的核心逻辑
        val frame1Width = frameHeight  // 图标部分宽度 = 标签高度
        val frame2Width = measureTextWidth(tagBean.text, realTagTextSize) + 2 * paddingH
        var frameWidth = frame1Width + frame2Width

        if (tagBean.isClickable) {
            frameWidth += appearance.arrowWidth.value + appearance.arrowSpacing.value
        }

        var spanWidth = frameWidth
        spanWidth += calculateTagSpacing(tagBean, appearance)

        return spanWidth
    }

    /**
     * 计算折扣标签宽度 - 精确模拟ZSBgTag.getSize()
     */
    private fun calculateZSBgTagWidth(tagBean: TagBean): Float {
        val appearance = tagBean.appearance
        val realTagTextSize = getTagTextSize(appearance, tagBean.useFixedHeight)
        val paddingV = getTagTextPaddingV(appearance, realTagTextSize)
        val paddingH = getTagTextPaddingH(appearance, realTagTextSize)

        var frameWidth: Float

        if (tagBean.text.length == 1) {
            // 单字符：正方形
            frameWidth = dealForSquareFrame(realTagTextSize, paddingV, appearance)
        } else {
            // 🎯 ZSBgTag的特殊处理：分别计算每个字符
            val frame1Width = measureTextWidth(tagBean.text.take(1), realTagTextSize) + 2 * paddingH
            val frame2Width = measureTextWidth(tagBean.text.drop(1), realTagTextSize) + 2 * paddingH
            frameWidth = frame1Width + frame2Width
        }

        if (tagBean.isClickable) {
            frameWidth += appearance.arrowWidth.value + appearance.arrowSpacing.value
        }

        var spanWidth = frameWidth
        spanWidth += calculateTagSpacing(tagBean, appearance)

        return spanWidth
    }

    /**
     * 计算填充+描边标签宽度 - 精确模拟FillAndStrokeBgSpan.getSize()
     */
    private fun calculateFillAndStrokeSpanWidth(tagBean: TagBean): Float {
        val appearance = tagBean.appearance
        val realTagTextSize = getTagTextSize(appearance, tagBean.useFixedHeight)
        val paddingV = getTagTextPaddingV(appearance, realTagTextSize)
        val paddingH = getTagTextPaddingH(appearance, realTagTextSize)

        var frameWidth: Float

        if (tagBean.text.length == 1) {
            frameWidth = dealForSquareFrame(realTagTextSize, paddingV, appearance)
        } else {
            frameWidth = measureTextWidth(tagBean.text, realTagTextSize) + 2 * paddingH
        }

        if (tagBean.isClickable) {
            frameWidth += appearance.arrowWidth.value + appearance.arrowSpacing.value
        }

        var spanWidth = frameWidth
        spanWidth += calculateTagSpacing(tagBean, appearance)

        return spanWidth
    }

    /**
     * 计算填充/描边标签宽度 - 精确模拟FillBgSpan.getSize() 和 StrokeBgSpan.getSize()
     */
    private fun calculateFillStrokeSpanWidth(tagBean: TagBean): Float {
        val appearance = tagBean.appearance
        val realTagTextSize = getTagTextSize(appearance, tagBean.useFixedHeight)
        val paddingV = getTagTextPaddingV(appearance, realTagTextSize)
        val paddingH = getTagTextPaddingH(appearance, realTagTextSize)

        var frameWidth: Float

        if (tagBean.text.length == 1) {
            frameWidth = dealForSquareFrame(realTagTextSize, paddingV, appearance)
        } else {
            frameWidth = measureTextWidth(tagBean.text, realTagTextSize) + 2 * paddingH
        }

        if (tagBean.isClickable) {
            frameWidth += appearance.arrowWidth.value + appearance.arrowSpacing.value
        }

        var spanWidth = frameWidth
        spanWidth += calculateTagSpacing(tagBean, appearance)

        return spanWidth
    }

    // ==================== 辅助函数 - 模拟原生TagAppearance方法 ====================

    /**
     * 模拟原生TagAppearance.getTagTextSize()
     */
    private fun getTagTextSize(appearance: TagAppearance, useFixedTagHeight: Boolean): Float {
        return if (useFixedTagHeight && appearance.fixedTextSize.value > 0) {
            appearance.fixedTextSize.value
        } else if (appearance.textSize.value > 0) {
            appearance.textSize.value
        } else {
            // 模拟：rawPaint.getTextSize() * defaultTagTextSizeRate
            12f * appearance.defaultTagTextSizeRate
        }
    }

    /**
     * 模拟原生TagAppearance.getTagTextPaddingV()
     */
    private fun getTagTextPaddingV(appearance: TagAppearance, tagTextSize: Float): Float {
        return if (appearance.verticalPadding.value >= 0) {
            appearance.verticalPadding.value
        } else {
            tagTextSize * appearance.defaultPaddingVerticalRate
        }
    }

    /**
     * 模拟原生TagAppearance.getTagTextPaddingH()
     */
    private fun getTagTextPaddingH(appearance: TagAppearance, tagTextSize: Float): Float {
        return if (appearance.horizontalPadding.value >= 0) {
            appearance.horizontalPadding.value
        } else {
            tagTextSize * appearance.defaultPaddingHorizontalRate
        }
    }

    /**
     * 模拟原生TagAppearance.dealForSquareFrame()
     */
    private fun dealForSquareFrame(tagTextSize: Float, paddingV: Float, appearance: TagAppearance): Float {
        // 模拟：TagUtils.getTextHeight(tagPaint) + 2 * paddingV
        val textHeight = tagTextSize * 1.2f  // 模拟getTextHeight

        return if (appearance.cornerRadius.value >= tagTextSize / 2) {
            // 半圆角的情况下调整需要稍微调大边距
            textHeight + 2 * paddingV * appearance.circleFrameScale
        } else {
            textHeight + 2 * paddingV
        }
    }

    /**
     * 模拟原生的间距处理逻辑
     */
    private fun calculateTagSpacing(tagBean: TagBean, appearance: TagAppearance): Float {
        var spacing = 0f

        // 🎯 模拟原生：多个标签,添加标签间距
        if (!tagBean.isLastTag()) {
            spacing += appearance.tagSpacing.value
        }

        // 🎯 模拟原生：从开始位置显示,最后一个标签,并且后面有文字内容,添加标签与文字间距
        if (tagBean.fromStart && tagBean.hasText && tagBean.isLastTag()) {
            spacing += appearance.textSpacing.value
        }

        // 🎯 模拟原生：从结束位置显示,第一个标签,添加便签与文字间的间距
        if (!tagBean.fromStart && tagBean.isFirstTag()) {
            spacing += appearance.textSpacing.value
        }

        return spacing
    }

    // ==================== 数据处理方法 ====================

    /**
     * 创建标签列表的索引信息
     * 为标签列表中的每个标签设置正确的索引和总数信息
     */
    fun processTagList(tags: List<TagBean>): List<TagBean> {
        return tags.mapIndexed { index, tag ->
            tag.copy(
                tagIndex = index,
                tagCount = tags.size
            )
        }
    }

    // ==================== 样式管理方法 ====================

    /**
     * 默认样式存储
     */
    private var defaultAppearance: TagAppearance = TagAppearance.Default
    private var defaultRoundAppearance: TagAppearance = TagAppearance.Round

    /**
     * 设置默认标签样式
     * 对应原生TagUtils.setDefaultAppearance()
     */
    fun setDefaultAppearance(appearance: TagAppearance) {
        defaultAppearance = appearance
    }

    /**
     * 设置默认圆角标签样式
     * 对应原生TagUtils.setDefaultRoundAppearance()
     */
    fun setDefaultRoundAppearance(appearance: TagAppearance) {
        defaultRoundAppearance = appearance
    }

    /**
     * 获取默认样式
     */
    fun getDefaultAppearance(): TagAppearance = defaultAppearance

    /**
     * 获取默认圆角样式
     */
    fun getDefaultRoundAppearance(): TagAppearance = defaultRoundAppearance
}
