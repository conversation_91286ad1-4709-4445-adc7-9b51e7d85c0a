# 🎪 Demo使用指南

## 📋 Demo文件位置

```
kmp-tag-library/
├── demo/
│   └── TagLibraryDemo.kt          # 完整功能演示
├── KMP_TAG_LIBRARY_USAGE_GUIDE.md # 详细使用指南
├── ADVANCED_USAGE_EXAMPLES.md     # 高级用法示例
├── QUICK_REFERENCE.md             # 快速参考
└── PROJECT_STRUCTURE.md           # 项目结构说明
```

## 🚀 运行Demo

### 1. 在你的项目中使用

```kotlin
import com.taglib.demo.TagLibraryDemo

@Composable
fun App() {
    MaterialTheme {
        TagLibraryDemo() // 运行完整Demo
    }
}
```

### 2. 单独运行某个功能演示

```kotlin
import com.taglib.demo.*

@Composable
fun MyApp() {
    MaterialTheme {
        Column {
            // 只运行基础标签类型演示
            BasicTagTypesDemo()
            
            // 只运行便捷方法演示
            ConvenienceMethodsDemo()
            
            // 只运行交互功能演示
            InteractiveDemo()
        }
    }
}
```

## 🎯 Demo功能展示

### 1. 基础标签类型 📋

展示6种标签类型的基本用法：

```kotlin
val basicTags = listOf(
    TagBean(type = TagType.FILL, text = "填充标签"),
    TagBean(type = TagType.STROKE, text = "描边标签"),
    TagBean(type = TagType.FILL_AND_STROKE, text = "填充+描边"),
    TagBean(type = TagType.DISCOUNT, text = "折扣标签"),
    TagBean(type = TagType.POINTS, text = "100积分"),
    TagBean(type = TagType.IMAGE, imageUrl = "image_url")
)
```

### 2. 便捷方法 🚀

演示所有便捷API的使用：

```kotlin
// 矩形标签在前
ShowRectStart(tags, "商品名称")

// 矩形标签在后
ShowRectEnd(tags, "商品名称")

// 圆角标签在前
ShowRoundStart(tags, "商品名称")

// 圆角标签在后
ShowRoundEnd(tags, "商品名称")
```

### 3. 扩展函数API ✨

展示现代化的扩展函数调用方式：

```kotlin
// 更简洁的调用方式
tags.showRectStart("使用扩展函数显示")
tags.showRoundEnd("圆角扩展函数")
```

### 4. 高级功能 ⚙️

演示高级配置和功能：

```kotlin
// 自定义样式
TagBean(
    appearance = TagAppearance(
        tagHeight = 24.dp,
        textSize = 12.sp,
        cornerRadius = 8.dp
    )
)

// 强制标签高度
TagGroup(tags = tags, forceTagHeight = true)

// 文字溢出处理
TagGroup(
    tags = tags,
    text = "很长的文字...",
    maxLines = 1,
    overflow = TextOverflow.Ellipsis
)
```

### 5. 自定义样式 🎨

展示不同大小和样式的标签：

```kotlin
// 大标签
TagBean(
    appearance = TagAppearance(
        tagHeight = 32.dp,
        textSize = 14.sp,
        cornerRadius = 16.dp
    )
)

// 小标签
TagBean(
    appearance = TagAppearance(
        tagHeight = 20.dp,
        textSize = 10.sp,
        cornerRadius = 4.dp
    )
)
```

### 6. 图片标签 🖼️

演示图片标签的使用：

```kotlin
val imageTags = listOf(
    TagBean(type = TagType.IMAGE, imageUrl = "star_icon"),
    TagBean(type = TagType.POINTS, text = "500积分", imageUrl = "points_icon")
)
```

### 7. 交互功能 🖱️

展示点击处理和状态管理：

```kotlin
var clickedTag by remember { mutableStateOf<String?>(null) }

TagGroup(
    tags = interactiveTags,
    onTagClick = { tag ->
        clickedTag = tag.text
        tag.clickToast?.let { toast ->
            println("Toast: $toast")
        }
    }
)
```

### 8. 性能测试 ⚡

展示大量标签的渲染性能：

```kotlin
val performanceTags = (1..10).map { index ->
    TagBean(
        type = if (index % 2 == 0) TagType.FILL else TagType.STROKE,
        text = "标签$index"
    )
}
```

## 🔧 自定义图片加载器

Demo中包含了一个示例图片加载器：

```kotlin
class DemoImageLoader : ComposeImageLoader {
    @Composable
    override fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter? {
        // 模拟图片加载
        return when {
            url.contains("star") -> ColorPainter(Color.Yellow)
            url.contains("discount") -> ColorPainter(Color.Red)
            url.contains("points") -> ColorPainter(Color.Green)
            else -> error ?: placeholder
        }
    }
}
```

## 📱 运行效果

运行Demo后，你将看到：

1. **完整的功能展示** - 所有标签类型和功能
2. **交互式演示** - 可以点击标签查看效果
3. **实时状态更新** - 点击后显示状态变化
4. **性能测试** - 大量标签的流畅渲染
5. **调试信息** - 控制台输出详细日志

## 🎯 学习路径

### 新手用户
1. 运行 `TagLibraryDemo()` 查看完整效果
2. 阅读 `QUICK_REFERENCE.md` 了解基础API
3. 参考Demo代码实现自己的标签

### 进阶用户
1. 查看 `ADVANCED_USAGE_EXAMPLES.md` 学习高级用法
2. 阅读 `KMP_TAG_LIBRARY_USAGE_GUIDE.md` 了解详细配置
3. 参考 `PROJECT_STRUCTURE.md` 了解架构设计

### 专业用户
1. 研究Demo中的性能优化技巧
2. 自定义图片加载器和样式
3. 扩展组件功能

## 🔍 调试技巧

### 开启调试模式

```kotlin
// 在Demo初始化时已开启
AppTag.init(
    loader = DemoImageLoader(),
    debug = true  // 开启调试模式
)
```

### 查看调试信息

运行Demo时，控制台会输出：

```
✅ AppTag: Initialized successfully with custom image loader
点击了标签: 填充标签
ShowRectStart: 新品
Toast: 点击了可点击标签
⚠️ ImageLoaderManager: Image URL is blank
```

## 💡 实用技巧

### 1. 快速测试标签效果

```kotlin
// 创建测试标签
val testTag = TagBean(
    type = TagType.FILL,
    text = "测试",
    backgroundColor = Color.Blue,
    textColor = Color.White
)

// 快速显示
TagGroup(tags = listOf(testTag), text = "测试文字")
```

### 2. 批量创建标签

```kotlin
val batchTags = (1..5).map { index ->
    TagBean(
        type = TagType.FILL,
        text = "标签$index",
        backgroundColor = Color.hsl(index * 60f, 0.7f, 0.5f)
    )
}
```

### 3. 动态切换样式

```kotlin
var useRoundStyle by remember { mutableStateOf(false) }

val dynamicTags = tags.map { tag ->
    tag.copy(
        appearance = if (useRoundStyle) {
            TagAppearance(cornerRadius = 12.dp)
        } else {
            TagAppearance.Default
        }
    )
}
```

## 🎉 开始使用

现在你可以：

1. **运行Demo** - 查看所有功能效果
2. **复制代码** - 将Demo代码复制到你的项目
3. **自定义修改** - 根据需求调整样式和功能
4. **参考文档** - 查看详细的API文档

享受KMP标签库带来的开发便利！🚀
