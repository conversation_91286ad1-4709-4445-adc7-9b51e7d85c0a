# KMP Tag Library API 文档

## 📚 目录

- [核心类](#核心类)
- [组件API](#组件api)
- [工具方法](#工具方法)
- [配置API](#配置api)
- [类型定义](#类型定义)
- [使用示例](#使用示例)

## 核心类

### TagBean

标签数据模型，定义标签的所有属性。

```kotlin
data class TagBean(
    val type: TagType = TagType.FILL,                    // 标签类型
    val text: String = "",                               // 标签文字
    val textColor: Color = Color.Black,                  // 文字颜色
    val backgroundColor: Color = Color.White,            // 背景颜色
    val backgroundEndColor: Color? = null,               // 渐变结束颜色
    val borderColor: Color = Color.Black,                // 边框颜色
    val imageUrl: String? = null,                        // 图片URL
    val isClickable: Boolean = false,                    // 是否可点击
    val clickToast: String? = null,                      // 点击提示
    val appearance: TagAppearance = TagAppearance.Default, // 样式配置
    val useFixedHeight: Boolean = false,                 // 是否固定高度
    val tagIndex: Int = 0,                              // 标签索引
    val tagCount: Int = 1,                              // 标签总数
    val showAtStart: Boolean = true                      // 显示位置
)
```

**方法：**
- `isFirstTag(): Boolean` - 是否为第一个标签
- `isLastTag(): Boolean` - 是否为最后一个标签
- `hasGradientBackground(): Boolean` - 是否有渐变背景

### TagAppearance

标签样式配置类，控制标签的视觉外观。

```kotlin
data class TagAppearance(
    val textSize: TextUnit = 12.sp,                     // 文字大小
    val fixedTextSize: TextUnit = 12.sp,                // 固定文字大小
    val tagHeight: Dp = (-1).dp,                        // 标签高度
    val fontWeight: FontWeight = FontWeight.Normal,      // 字体粗细
    val borderWidth: Dp = 0.5.dp,                       // 边框宽度
    val cornerRadius: Dp = 4.dp,                        // 圆角大小
    val shape: Shape = RoundedCornerShape(cornerRadius), // 标签形状
    val horizontalPadding: Dp = 6.dp,                   // 水平内边距
    val verticalPadding: Dp = 2.dp,                     // 垂直内边距
    val tagSpacing: Dp = 4.dp,                          // 标签间距
    val textSpacing: Dp = 4.dp,                         // 文字间距
    val imageHeightRatio: Float = 0.95f,                // 图片高度比例
    val arrowWidth: Dp = 8.dp,                          // 箭头宽度
    val arrowSpacing: Dp = 2.dp,                        // 箭头间距
    // ... 更多配置项
)
```

**预定义样式：**
- `TagAppearance.Default` - 默认样式
- `TagAppearance.Round` - 圆角样式
- `TagAppearance.Capsule` - 胶囊样式
- `TagAppearance.Square` - 方形样式

**方法：**
- `withCornerRadius(radius: Dp): TagAppearance` - 设置圆角
- `withTextSize(size: TextUnit): TagAppearance` - 设置文字大小
- `withPadding(horizontal: Dp, vertical: Dp): TagAppearance` - 设置内边距

### GoodsTag

商品标签数据类，与原生Android项目兼容。

```kotlin
data class GoodsTag(
    val form: Int? = null,              // 标签类型（数字）
    val name: String? = null,           // 标签名称
    val color: String? = null,          // 文字颜色（十六进制）
    val bgcolor: String? = null,        // 背景颜色（十六进制）
    val bgGraduallyColor: String? = null, // 渐变颜色（十六进制）
    val bordercolor: String? = null,    // 边框颜色（十六进制）
    val rlink: String? = null           // 图片链接
)
```

## 组件API

### TagGroup

主要的标签组合组件，用于显示标签和文字。

```kotlin
@Composable
fun TagGroup(
    tags: List<TagBean>,                                 // 标签列表
    text: String = "",                                   // 文字内容
    textStyle: TextStyle = MaterialTheme.typography.bodyMedium, // 文字样式
    showTagsAtStart: Boolean = true,                     // 标签位置
    onTagClick: ((TagBean) -> Unit)? = null,            // 点击回调
    arrowIcon: ImageVector = Icons.Default.KeyboardArrowRight, // 箭头图标
    imagePainters: Map<String, Painter> = emptyMap(),   // 图片画笔映射
    loadingContent: @Composable (() -> Unit)? = null,   // 加载中内容
    errorContent: @Composable (() -> Unit)? = null,     // 错误内容
    modifier: Modifier = Modifier                        // 修饰符
)
```

### 单独标签组件

#### FillTag - 填充背景标签

```kotlin
@Composable
fun FillTag(
    tagBean: TagBean,                                    // 标签数据
    onClick: ((TagBean) -> Unit)? = null,               // 点击回调
    arrowIcon: ImageVector? = null,                     // 箭头图标
    modifier: Modifier = Modifier                        // 修饰符
)
```

#### StrokeTag - 镂空边框标签

```kotlin
@Composable
fun StrokeTag(
    tagBean: TagBean,                                    // 标签数据
    onClick: ((TagBean) -> Unit)? = null,               // 点击回调
    arrowIcon: ImageVector? = null,                     // 箭头图标
    modifier: Modifier = Modifier                        // 修饰符
)
```

#### ImageTag - 图片标签

```kotlin
@Composable
fun ImageTag(
    tagBean: TagBean,                                    // 标签数据
    imagePainter: Painter? = null,                      // 图片画笔
    loadingContent: @Composable (() -> Unit)? = null,   // 加载中内容
    errorContent: @Composable (() -> Unit)? = null,     // 错误内容
    onClick: ((TagBean) -> Unit)? = null,               // 点击回调
    arrowIcon: ImageVector? = null,                     // 箭头图标
    modifier: Modifier = Modifier                        // 修饰符
)
```

#### DiscountTag - 折省标签

```kotlin
@Composable
fun DiscountTag(
    tagBean: TagBean,                                    // 标签数据
    onClick: ((TagBean) -> Unit)? = null,               // 点击回调
    arrowIcon: ImageVector? = null,                     // 箭头图标
    modifier: Modifier = Modifier                        // 修饰符
)
```

#### PointsTag - 积分标签

```kotlin
@Composable
fun PointsTag(
    tagBean: TagBean,                                    // 标签数据
    iconPainter: Painter? = null,                       // 图标画笔
    onClick: ((TagBean) -> Unit)? = null,               // 点击回调
    arrowIcon: ImageVector? = null,                     // 箭头图标
    modifier: Modifier = Modifier                        // 修饰符
)
```

## 工具方法

### TagUtils

核心工具类，提供各种实用方法。

#### 颜色处理

```kotlin
// 解析颜色字符串
fun parseColor(colorString: String?, defaultColor: Color = Color.Black): Color

// 颜色转十六进制
fun colorToHex(color: Color): String

// 判断是否为深色
fun isDarkColor(color: Color): Boolean

// 获取对比色
fun getContrastColor(backgroundColor: Color): Color

// 创建渐变色列表
fun createGradientColors(startColor: Color, endColor: Color): List<Color>
```

#### 文字测量

```kotlin
// 测量文字宽度（带缓存）
fun measureTextWidth(text: String, textSize: Float): Float

// 获取文字高度
fun getTextHeight(textSize: Float): Float

// 清除测量缓存
fun clearTextWidthCache()
```

#### 布局计算

```kotlin
// 调整基线位置
fun adjustBaseLine(originalY: Float, containerHeight: Float, textHeight: Float): Float

// 检查并校正行高
fun checkLineHeight(currentHeight: Float, minHeight: Float): Float
```

#### 数据处理

```kotlin
// 验证标签数据
fun validateTagBean(tagBean: TagBean): Boolean

// 处理标签列表（设置索引）
fun processTagList(tags: List<TagBean>): List<TagBean>

// 根据类型获取默认样式
fun getDefaultAppearanceForType(type: TagType): TagAppearance
```

#### 样式管理

```kotlin
// 设置默认样式
fun setDefaultAppearance(appearance: TagAppearance)

// 设置默认圆角样式
fun setDefaultRoundAppearance(appearance: TagAppearance)

// 获取默认样式
fun getDefaultAppearance(): TagAppearance

// 获取默认圆角样式
fun getDefaultRoundAppearance(): TagAppearance
```

## 配置API

### AppTag

应用级配置类，提供简化的使用方式。

```kotlin
object AppTag {
    // 初始化配置
    fun init(loader: ComposeImageLoader? = null)
    
    // 显示标签组合
    @Composable
    fun ShowTags(
        tags: List<GoodsTag?>?,                          // 商品标签列表
        content: String = "",                            // 内容文字
        showTagsAtStart: Boolean = true,                 // 标签位置
        onTagClick: ((TagBean) -> Unit)? = null          // 点击回调
    )
    
    // 检查初始化状态
    fun isInitialized(): Boolean
    
    // 获取图片加载器
    fun getImageLoader(): ComposeImageLoader?
}
```

### 工厂方法

#### TagFactory

快速创建TagBean的工厂方法。

```kotlin
object TagFactory {
    fun newTag(text: String = "新品"): TagBean
    fun hotTag(text: String = "热销"): TagBean
    fun recommendTag(text: String = "推荐"): TagBean
    fun limitedTag(text: String = "限时"): TagBean
    fun freeShippingTag(text: String = "包邮"): TagBean
    fun discountTag(text: String): TagBean
    fun pointsTag(text: String): TagBean
}
```

#### GoodsTagFactory

快速创建GoodsTag的工厂方法。

```kotlin
object GoodsTagFactory {
    fun newTag(): GoodsTag
    fun hotTag(): GoodsTag
    fun freeShippingTag(): GoodsTag
    fun discountTag(text: String): GoodsTag
    fun pointsTag(points: String): GoodsTag
}
```

## 类型定义

### TagType

标签类型枚举。

```kotlin
enum class TagType {
    FILL,                    // 填充背景标签
    STROKE,                  // 镂空边框标签
    IMAGE,                   // 图片标签
    DISCOUNT,                // 折省标签
    POINTS,                  // 积分标签
    FILL_AND_STROKE         // 填充+描边标签
}
```

### 图片加载接口

```kotlin
interface ImageLoader {
    fun loadImage(url: String, callback: ImageCallback)
    fun cancelLoad(url: String)
    fun clearCache()
}

interface ComposeImageLoader {
    @Composable
    fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter?
}

interface ImageCallback {
    fun onSuccess(painter: Painter)
    fun onError(error: Throwable)
    fun onStart()
}
```

## 使用示例

### 基础使用

```kotlin
@Composable
fun BasicExample() {
    val tags = listOf(
        TagBean(
            type = TagType.FILL,
            text = "新品",
            textColor = Color.White,
            backgroundColor = Color.Red
        )
    )
    
    TagGroup(
        tags = tags,
        text = "商品名称",
        onTagClick = { tag ->
            println("点击了: ${tag.text}")
        }
    )
}
```

### AppTag配置使用

```kotlin
@Composable
fun AppTagExample() {
    // 初始化
    LaunchedEffect(Unit) {
        AppTag.init()
    }
    
    val goodsTags = listOf(
        GoodsTag(form = 1, name = "新品", color = "#FFFFFF", bgcolor = "#FF0000")
    )
    
    AppTag.ShowTags(
        tags = goodsTags,
        content = "商品名称"
    )
}
```

### 自定义样式

```kotlin
@Composable
fun CustomStyleExample() {
    val customAppearance = TagAppearance(
        textSize = 14.sp,
        cornerRadius = 8.dp,
        horizontalPadding = 12.dp,
        fontWeight = FontWeight.Bold
    )
    
    val tag = TagBean(
        type = TagType.FILL,
        text = "自定义",
        appearance = customAppearance
    )
    
    TagGroup(tags = listOf(tag))
}
```

---

更多详细信息请参考源码注释和示例代码。
