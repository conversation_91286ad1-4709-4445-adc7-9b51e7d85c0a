# Android原生 → KMP Compose 迁移指南

## 📋 对比总览

| 功能 | 原生Android | KMP Compose | 说明 |
|------|------------|-------------|------|
| **初始化** | `AppTag.init(context)` | `AppTag.init(imageLoader)` | 不再需要Context |
| **显示标签** | `AppTag.showTags(context, textView, tags, content)` | `AppTag.ShowTags(tags, content)` | Composable函数 |
| **数据结构** | `GoodsTag` | `GoodsTag` | 保持一致 |
| **图片加载** | `INetPicLoader` | `ComposeImageLoader` | 适配Compose |

## 🔄 详细迁移步骤

### 1. 初始化配置迁移

**原生Android版本:**
```kotlin
object AppTag {
    fun init(c: Context?) {
        TagUtils.initNetLoader(c, TagPicLoader())
        val appearance = TagAppearance.Builder()
            .tagHeight(15F)
            .tagTextSize(11F)
            .cornerSizeDp(2f)
            .build()
        TagUtils.setDefaultAppearance(appearance)
    }
}
```

**KMP Compose版本:**
```kotlin
object AppTag {
    fun init(loader: ComposeImageLoader? = null) {
        val imageLoader = loader ?: DefaultComposeImageLoader()
        ImageLoaderManager.initializeCompose(imageLoader)
        
        val defaultAppearance = TagAppearance(
            tagHeight = 15.dp,
            textSize = 11.sp,
            cornerRadius = 2.dp
        )
        TagUtils.setDefaultAppearance(defaultAppearance)
    }
}
```

**迁移要点:**
- ❌ 不再需要传入`Context`
- ✅ 传入`ComposeImageLoader`实现
- ✅ 使用Compose的单位系统（dp、sp）

### 2. 显示标签迁移

**原生Android版本:**
```kotlin
AppTag.showTags(context, textView, tags, content)
```

**KMP Compose版本:**
```kotlin
@Composable
fun ProductItem() {
    AppTag.ShowTags(
        tags = tags,
        content = content,
        onTagClick = { tag -> 
            // 处理点击事件
        }
    )
}
```

**迁移要点:**
- ❌ 不再需要`Context`和`TextView`
- ✅ 在`@Composable`函数中调用
- ✅ 支持点击回调

### 3. 图片加载器迁移

**原生Android版本:**
```kotlin
private class TagPicLoader : INetPicLoader {
    override fun loadImage(url: String, imageCallback: ImageCallback?) {
        LoadImageModel.loadImage(url, object : BitmapCallback() {
            override fun onSucceed(what: Int, result: Bitmap?) {
                imageCallback?.onBitmapReady(result)
            }
        })
    }
}
```

**KMP Compose版本:**
```kotlin
class CustomImageLoader : ComposeImageLoader {
    @Composable
    override fun loadImage(
        url: String, 
        placeholder: Painter?, 
        error: Painter?
    ): Painter? {
        return rememberAsyncImagePainter(
            ImageRequest.Builder(LocalContext.current)
                .data(url)
                .placeholder(placeholder)
                .error(error)
                .build()
        )
    }
}
```

**迁移要点:**
- ✅ 实现`ComposeImageLoader`接口
- ✅ 返回`Painter`而不是`Bitmap`
- ✅ 在`@Composable`函数中使用

### 4. 数据结构迁移

**GoodsTag数据结构保持完全一致:**
```kotlin
data class GoodsTag(
    val form: Int? = null,              // 标签类型
    val name: String? = null,           // 标签名称  
    val color: String? = null,          // 文字颜色
    val bgcolor: String? = null,        // 背景颜色
    val bgGraduallyColor: String? = null, // 渐变背景颜色
    val bordercolor: String? = null,    // 边框颜色
    val rlink: String? = null           // 图片链接
)
```

**无需修改现有的数据结构！**

## 🚀 完整迁移示例

### 原生Android项目

```kotlin
// Application中初始化
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        AppTag.init(this)
    }
}

// Activity中使用
class ProductActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_product)
        
        val textView = findViewById<TextView>(R.id.product_title)
        val tags = listOf(
            GoodsTag(1, "新品", "#FFFFFF", "#FF0000", null, null, null),
            GoodsTag(3, "包邮", "#4CAF50", null, null, "#4CAF50", null)
        )
        
        AppTag.showTags(this, textView, tags, "iPhone 15 Pro")
    }
}
```

### KMP Compose项目

```kotlin
// Application中初始化（Android）
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        AppTag.init(CoilImageLoader()) // 使用Coil图片加载器
    }
}

// Compose中使用
@Composable
fun ProductScreen() {
    val tags = listOf(
        GoodsTag(1, "新品", "#FFFFFF", "#FF0000", null, null, null),
        GoodsTag(3, "包邮", "#4CAF50", null, null, "#4CAF50", null)
    )
    
    Column {
        AppTag.ShowTags(
            tags = tags,
            content = "iPhone 15 Pro",
            onTagClick = { tag ->
                println("点击了标签: ${tag.text}")
            }
        )
        
        // 其他UI组件...
    }
}
```

## 📱 平台特定配置

### Android平台
```kotlin
// 使用Coil图片加载器
class CoilImageLoader : ComposeImageLoader {
    @Composable
    override fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter? {
        return rememberAsyncImagePainter(
            ImageRequest.Builder(LocalContext.current)
                .data(url)
                .placeholder(placeholder)
                .error(error)
                .build()
        )
    }
}
```

### iOS平台
```kotlin
// 使用Ktor或其他网络库
class KtorImageLoader : ComposeImageLoader {
    @Composable
    override fun loadImage(url: String, placeholder: Painter?, error: Painter?): Painter? {
        // TODO: 实现iOS图片加载
        return error
    }
}
```

## ✅ 迁移检查清单

- [ ] 移除原生tag库依赖
- [ ] 添加KMP tag库依赖
- [ ] 更新初始化代码（移除Context参数）
- [ ] 实现`ComposeImageLoader`
- [ ] 将`showTags`调用替换为`ShowTags` Composable
- [ ] 测试所有标签类型显示正常
- [ ] 测试点击交互功能
- [ ] 测试图片标签加载

## 🎯 迁移优势

1. **跨平台支持** - 一套代码支持Android和iOS
2. **现代化UI** - 基于Compose的声明式UI
3. **更好的性能** - Compose的高效渲染机制
4. **类型安全** - Kotlin的类型系统保障
5. **易于维护** - 清晰的组件化架构

## 🔧 常见问题

**Q: 迁移后性能如何？**
A: Compose版本在大多数场景下性能相当或更好，特别是在复杂布局和动画方面。

**Q: 是否支持所有原生功能？**
A: 是的，所有原生功能都已完整实现，并且增加了一些新特性。

**Q: 可以渐进式迁移吗？**
A: 可以，建议先迁移新功能，然后逐步替换现有页面。

**Q: iOS平台图片加载如何实现？**
A: 可以使用Ktor、Kamel等跨平台图片加载库，或者实现平台特定的加载器。
