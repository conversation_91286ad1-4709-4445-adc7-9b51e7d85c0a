# KMP Tag Library 使用示例

## 快速开始

### 1. 基本标签使用

```kotlin
import com.taglib.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

@Composable
fun SimpleTagExample() {
    // 创建一个简单的填充标签
    val tag = TagBean(
        type = TagType.FILL,
        text = "新品",
        textColor = Color.White,
        backgroundColor = Color.Red
    )
    
    TagGroup(
        tags = listOf(tag),
        text = "商品名称"
    )
}
```

### 2. 多标签组合

```kotlin
@Composable
fun MultipleTagsExample() {
    val tags = listOf(
        TagFactory.newTag("新品"),
        TagFactory.hotTag("热销"),
        TagFactory.freeShippingTag("包邮")
    )
    
    TagGroup(
        tags = tags,
        text = "多标签商品",
        showTagsAtStart = true,
        onTagClick = { tag ->
            println("点击了: ${tag.text}")
        }
    )
}
```

### 3. 自定义样式

```kotlin
@Composable
fun CustomStyleExample() {
    val customAppearance = TagAppearance(
        textSize = 14.sp,
        cornerRadius = 8.dp,
        horizontalPadding = 12.dp,
        verticalPadding = 6.dp,
        fontWeight = FontWeight.Bold
    )
    
    val customTag = TagBean(
        type = TagType.FILL,
        text = "自定义样式",
        textColor = Color.White,
        backgroundColor = Color(0xFF6200EA),
        appearance = customAppearance
    )
    
    TagGroup(tags = listOf(customTag))
}
```

### 4. 渐变背景标签

```kotlin
@Composable
fun GradientTagExample() {
    val gradientTag = TagBean(
        type = TagType.FILL,
        text = "限时特价",
        textColor = Color.White,
        backgroundColor = Color(0xFFFF6B6B),
        backgroundEndColor = Color(0xFFFFE66D),
        appearance = TagAppearance.Capsule
    )
    
    TagGroup(
        tags = listOf(gradientTag),
        text = "渐变效果商品"
    )
}
```

### 5. 镂空边框标签

```kotlin
@Composable
fun StrokeTagExample() {
    val strokeTag = TagBean(
        type = TagType.STROKE,
        text = "推荐",
        textColor = Color.Blue,
        borderColor = Color.Blue,
        appearance = TagAppearance.Round
    )
    
    TagGroup(
        tags = listOf(strokeTag),
        text = "推荐商品"
    )
}
```

### 6. 可点击标签

```kotlin
@Composable
fun ClickableTagExample() {
    val clickableTag = TagBean(
        type = TagType.FILL,
        text = "查看详情",
        textColor = Color.White,
        backgroundColor = Color.Blue,
        isClickable = true,
        clickToast = "点击查看更多信息"
    )
    
    TagGroup(
        tags = listOf(clickableTag),
        text = "可点击商品",
        onTagClick = { tag ->
            // 处理点击事件
            if (tag.clickToast != null) {
                showToast(tag.clickToast)
            }
        }
    )
}
```

### 7. 图片标签（需要图片加载器）

```kotlin
@Composable
fun ImageTagExample() {
    val imageTag = TagBean(
        type = TagType.IMAGE,
        imageUrl = "https://example.com/tag-icon.png"
    )
    
    // 在实际使用中，你需要提供图片Painter
    val imagePainters = mapOf(
        "https://example.com/tag-icon.png" to rememberAsyncImagePainter("https://example.com/tag-icon.png")
    )
    
    TagGroup(
        tags = listOf(imageTag),
        text = "图片标签商品",
        imagePainters = imagePainters,
        loadingContent = {
            CircularProgressIndicator(modifier = Modifier.size(16.dp))
        },
        errorContent = {
            Icon(Icons.Default.Error, contentDescription = "加载失败")
        }
    )
}
```

### 8. 电商场景完整示例

```kotlin
@Composable
fun ECommerceProductCard(
    productName: String,
    price: String,
    originalPrice: String? = null,
    isNew: Boolean = false,
    isHot: Boolean = false,
    isFreeShipping: Boolean = false,
    discount: String? = null
) {
    val tags = mutableListOf<TagBean>()
    
    // 根据条件添加标签
    if (isNew) {
        tags.add(TagFactory.newTag())
    }
    
    if (isHot) {
        tags.add(TagFactory.hotTag())
    }
    
    if (discount != null) {
        tags.add(TagFactory.discountTag(discount))
    }
    
    if (isFreeShipping) {
        tags.add(TagFactory.freeShippingTag())
    }
    
    Card(
        modifier = Modifier.fillMaxWidth().padding(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            // 商品标签和名称
            TagGroup(
                tags = tags,
                text = productName,
                showTagsAtStart = true,
                onTagClick = { tag ->
                    when (tag.text) {
                        "新品" -> showNewProductInfo()
                        "热销" -> showHotProductInfo()
                        "包邮" -> showShippingInfo()
                        else -> showTagInfo(tag.text)
                    }
                }
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 价格信息
            Row {
                Text(
                    text = price,
                    style = MaterialTheme.typography.headlineSmall,
                    color = Color.Red,
                    fontWeight = FontWeight.Bold
                )
                
                if (originalPrice != null) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = originalPrice,
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.Gray,
                        textDecoration = TextDecoration.LineThrough
                    )
                }
            }
        }
    }
}

// 使用示例
@Composable
fun ProductListExample() {
    LazyColumn {
        item {
            ECommerceProductCard(
                productName = "iPhone 15 Pro",
                price = "¥7999",
                originalPrice = "¥8999",
                isNew = true,
                isHot = true,
                isFreeShipping = true,
                discount = "限时8.9折"
            )
        }
        
        item {
            ECommerceProductCard(
                productName = "MacBook Air M2",
                price = "¥8999",
                isNew = false,
                isHot = true,
                isFreeShipping = true
            )
        }
    }
}
```

## 高级用法

### 自定义标签组件

```kotlin
@Composable
fun CustomBadgeTag(
    text: String,
    badgeCount: Int,
    modifier: Modifier = Modifier
) {
    val tag = TagBean(
        type = TagType.FILL,
        text = text,
        textColor = Color.White,
        backgroundColor = Color.Red,
        appearance = TagAppearance.Round
    )
    
    Box(modifier = modifier) {
        TagGroup(tags = listOf(tag))
        
        if (badgeCount > 0) {
            Badge(
                modifier = Modifier.align(Alignment.TopEnd)
            ) {
                Text(badgeCount.toString())
            }
        }
    }
}
```

### 动态标签生成

```kotlin
@Composable
fun DynamicTagsExample(productData: ProductData) {
    val tags = remember(productData) {
        buildList {
            if (productData.isNew) add(TagFactory.newTag())
            if (productData.salesRank <= 10) add(TagFactory.hotTag())
            if (productData.freeShipping) add(TagFactory.freeShippingTag())
            if (productData.discountPercent > 0) {
                add(TagFactory.discountTag("${productData.discountPercent}折"))
            }
            if (productData.points > 0) {
                add(TagFactory.pointsTag("${productData.points}积分"))
            }
        }
    }
    
    TagGroup(
        tags = tags,
        text = productData.name,
        onTagClick = { tag ->
            handleTagClick(tag, productData)
        }
    )
}
```

这些示例展示了KMP Tag Library的各种使用场景，从简单的单标签到复杂的电商产品卡片，都能很好地支持。
