package com.taglib.test

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.*

/**
 * 优化后代码的验证测试
 */
object OptimizationTest {
    
    /**
     * 测试TagBean的新增方法
     */
    fun testTagBeanOptimizations() {
        println("🧪 Testing TagBean optimizations...")
        
        val tag = TagBean(
            type = TagType.FILL,
            text = "测试标签",
            textColor = Color.White,
            backgroundColor = Color.Red,
            imageUrl = "https://example.com/image.png"
        )
        
        // 测试新增的实用方法
        println("✅ isValid: ${tag.isValid()}")
        println("✅ getDisplayText: ${tag.getDisplayText()}")
        println("✅ needsImageLoading: ${tag.needsImageLoading()}")
        println("✅ getUniqueId: ${tag.getUniqueId()}")
        println("✅ shouldShowArrow: ${tag.shouldShowArrow()}")
        println("✅ getContrastTextColor: ${tag.getContrastTextColor()}")
        
        // 测试索引方法
        val tagWithIndex = tag.copyWithIndex(1, 3)
        println("✅ copyWithIndex: index=${tagWithIndex.tagIndex}, count=${tagWithIndex.tagCount}")
    }
    
    /**
     * 测试TagUtils的优化
     */
    fun testTagUtilsOptimizations() {
        println("\n🧪 Testing TagUtils optimizations...")
        
        // 测试文字测量缓存
        val text = "测试文字Test123"
        val textSize = 14f
        
        val width1 = TagUtils.measureTextWidth(text, textSize)
        val width2 = TagUtils.measureTextWidth(text, textSize) // 应该从缓存获取
        
        println("✅ Text width measurement: $width1 (cached: ${width1 == width2})")
        
        // 测试缓存统计
        val cacheStats = TagUtils.getTextCacheStats()
        println("✅ Cache stats: $cacheStats")
        
        // 测试颜色解析
        val color1 = TagUtils.parseColor("#FF0000", Color.Black)
        val color2 = TagUtils.parseColor("#AAFF0000", Color.Black)
        val color3 = TagUtils.parseColor("invalid", Color.Black)
        
        println("✅ Color parsing: RGB=$color1, ARGB=$color2, Invalid=$color3")
        
        // 测试对比色
        val contrastColor = TagUtils.getContrastColor(Color.Red)
        println("✅ Contrast color for Red: $contrastColor")
    }
    
    /**
     * 测试ImageLoaderManager的优化
     */
    fun testImageLoaderManagerOptimizations() {
        println("\n🧪 Testing ImageLoaderManager optimizations...")
        
        // 测试初始化状态
        println("✅ Is initialized: ${ImageLoaderManager.isInitialized()}")
        
        // 测试调试模式
        println("✅ Is debug mode: ${ImageLoaderManager.isDebug()}")
        
        // 测试空安全
        val loader = ImageLoaderManager.getComposeImageLoader()
        println("✅ Loader is null: ${loader == null}")
        
        // 测试重置
        ImageLoaderManager.reset()
        println("✅ After reset - Is initialized: ${ImageLoaderManager.isInitialized()}")
    }
    
    /**
     * 测试AppTag的优化
     */
    fun testAppTagOptimizations() {
        println("\n🧪 Testing AppTag optimizations...")
        
        try {
            // 测试带调试模式的初始化
            AppTag.init(debug = true)
            println("✅ AppTag initialization successful")
            
            // 测试状态检查
            println("✅ AppTag is initialized: ${AppTag.isInitialized()}")
            
            // 测试图片加载器获取
            val imageLoader = AppTag.getImageLoader()
            println("✅ ImageLoader type: ${imageLoader?.javaClass?.simpleName}")
            
        } catch (e: Exception) {
            println("❌ AppTag initialization failed: ${e.message}")
        }
    }
    
    /**
     * 测试标签验证
     */
    fun testTagValidation() {
        println("\n🧪 Testing tag validation...")
        
        val validTags = listOf(
            TagBean(type = TagType.FILL, text = "有效标签"),
            TagBean(type = TagType.IMAGE, imageUrl = "https://example.com/image.png"),
            TagBean(type = TagType.POINTS, text = "100积分", imageUrl = "icon.png")
        )
        
        val invalidTags = listOf(
            TagBean(type = TagType.FILL, text = ""), // 空文字
            TagBean(type = TagType.IMAGE, imageUrl = ""), // 空URL
            TagBean(type = TagType.STROKE, text = "   ") // 空白文字
        )
        
        println("✅ Valid tags:")
        validTags.forEach { tag ->
            println("   - ${tag.type}: ${tag.isValid()}")
        }
        
        println("✅ Invalid tags:")
        invalidTags.forEach { tag ->
            println("   - ${tag.type}: ${tag.isValid()}")
        }
    }
    
    /**
     * 测试性能优化
     */
    fun testPerformanceOptimizations() {
        println("\n🧪 Testing performance optimizations...")
        
        // 测试文字测量缓存性能
        val testTexts = listOf("短文字", "这是一个比较长的测试文字", "Mixed中英文123")
        val textSize = 14f
        
        val startTime = System.currentTimeMillis()
        
        // 第一次测量（会缓存）
        testTexts.forEach { text ->
            TagUtils.measureTextWidth(text, textSize)
        }
        
        val firstRoundTime = System.currentTimeMillis() - startTime
        
        val cacheStartTime = System.currentTimeMillis()
        
        // 第二次测量（从缓存获取）
        repeat(100) {
            testTexts.forEach { text ->
                TagUtils.measureTextWidth(text, textSize)
            }
        }
        
        val cacheRoundTime = System.currentTimeMillis() - cacheStartTime
        
        println("✅ Performance test:")
        println("   - First round (with caching): ${firstRoundTime}ms")
        println("   - Cache round (100x): ${cacheRoundTime}ms")
        println("   - Cache efficiency: ${if (cacheRoundTime < firstRoundTime * 10) "Good" else "Needs improvement"}")
    }
    
    /**
     * 运行所有测试
     */
    fun runAllTests() {
        println("🚀 Starting optimization tests...\n")
        
        testTagBeanOptimizations()
        testTagUtilsOptimizations()
        testImageLoaderManagerOptimizations()
        testAppTagOptimizations()
        testTagValidation()
        testPerformanceOptimizations()
        
        println("\n🎉 All optimization tests completed!")
    }
}

/**
 * Compose测试组件
 */
@Composable
fun OptimizationTestCompose() {
    // 这里可以添加Compose相关的测试
    // 比如测试扩展函数、图片加载验证等
    
    val testTag = TagBean(
        type = TagType.IMAGE,
        text = "测试图片标签",
        imageUrl = "https://example.com/test.png"
    )
    
    // 测试扩展函数
    val validatedImage = testTag.loadValidatedImage(
        imageLoader = ImageLoaderManager.getComposeImageLoader()
    )
    
    val cachedIcon = testTag.getCachedPointsIcon(
        imageLoader = ImageLoaderManager.getComposeImageLoader()
    )
}
