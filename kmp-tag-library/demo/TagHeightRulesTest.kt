package com.taglib.demo

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.*

/**
 * 标签高度规则测试
 * 
 * 验证原生库的三个核心规则：
 * 1. 外部文字高度大于设置的固定标签高度 → 显示标签的固定高度
 * 2. 外部文字高度小于设置的固定标签高度 → 标签高度随文字高度自适应
 * 3. 若强制设置标签高度 → 调整外部文字大小兼容处理
 */
@Composable
fun TagHeightRulesTest() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Text(
            text = "📏 标签高度规则测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = "验证原生库的三个核心高度处理规则",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 规则1测试：文字高度 > 标签高度
        Rule1Test()
        
        // 规则2测试：文字高度 < 标签高度
        Rule2Test()
        
        // 规则3测试：强制标签高度
        Rule3Test()
        
        // 综合对比测试
        ComprehensiveTest()
    }
}

/**
 * 规则1测试：外部文字高度大于设置的固定标签高度 → 显示标签的固定高度
 */
@Composable
private fun Rule1Test() {
    TestSection("📐 规则1：文字高度 > 标签高度 → 使用固定标签高度") {
        Text(
            text = "当外部文字较大时，标签应该使用设定的固定高度",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        val testCases = listOf(
            "大文字" to 20.sp to 24.dp, // 20sp文字 vs 24dp标签高度
            "超大文字" to 24.sp to 28.dp, // 24sp文字 vs 28dp标签高度
            "巨大文字" to 28.sp to 32.dp  // 28sp文字 vs 32dp标签高度
        )
        
        testCases.forEach { (description, textSize, tagHeight) ->
            TestCase(
                description = description,
                textSize = textSize,
                tagHeight = tagHeight,
                expectedBehavior = "使用固定高度 ${tagHeight.value.toInt()}dp",
                forceTagHeight = false
            )
        }
    }
}

/**
 * 规则2测试：外部文字高度小于设置的固定标签高度 → 标签高度随文字高度自适应
 */
@Composable
private fun Rule2Test() {
    TestSection("📐 规则2：文字高度 < 标签高度 → 标签高度自适应") {
        Text(
            text = "当外部文字较小时，标签应该随文字高度自适应",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        val testCases = listOf(
            "小文字" to 10.sp to 32.dp, // 10sp文字 vs 32dp标签高度
            "中文字" to 12.sp to 36.dp, // 12sp文字 vs 36dp标签高度
            "普通文字" to 14.sp to 40.dp  // 14sp文字 vs 40dp标签高度
        )
        
        testCases.forEach { (description, textSize, tagHeight) ->
            TestCase(
                description = description,
                textSize = textSize,
                tagHeight = tagHeight,
                expectedBehavior = "自适应文字高度",
                forceTagHeight = false
            )
        }
    }
}

/**
 * 规则3测试：若强制设置标签高度 → 调整外部文字大小兼容处理
 */
@Composable
private fun Rule3Test() {
    TestSection("📐 规则3：强制标签高度 → 调整文字大小") {
        Text(
            text = "当强制使用标签高度时，应该调整文字大小以适配",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        val testCases = listOf(
            "强制小高度" to 10.sp to 24.dp, // 10sp文字强制适配24dp
            "强制中高度" to 12.sp to 32.dp, // 12sp文字强制适配32dp
            "强制大高度" to 14.sp to 40.dp  // 14sp文字强制适配40dp
        )
        
        testCases.forEach { (description, textSize, tagHeight) ->
            TestCase(
                description = description,
                textSize = textSize,
                tagHeight = tagHeight,
                expectedBehavior = "调整文字大小并使用固定高度",
                forceTagHeight = true
            )
        }
    }
}

/**
 * 单个测试用例
 */
@Composable
private fun TestCase(
    description: String,
    textSize: androidx.compose.ui.unit.TextUnit,
    tagHeight: androidx.compose.ui.unit.Dp,
    expectedBehavior: String,
    forceTagHeight: Boolean
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "$description (${textSize.value.toInt()}sp文字 vs ${tagHeight.value.toInt()}dp标签)",
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Medium
        )
        
        // 创建测试标签
        val testTag = TagBean(
            type = TagType.FILL,
            text = "测试",
            backgroundColor = Color(0xFF2196F3),
            textColor = Color.White,
            appearance = TagAppearance.Default.copy(
                tagHeight = tagHeight,
                textSize = textSize
            )
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 显示标签
            TagGroup(
                tags = listOf(testTag),
                text = "",
                textStyle = androidx.compose.ui.text.TextStyle(fontSize = textSize),
                forceTagHeight = forceTagHeight,
                maxLines = 1
            )
            
            // 显示预期行为
            Text(
                text = "预期: $expectedBehavior",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF4CAF50),
                modifier = Modifier.weight(1f)
            )
        }
        
        // 显示实际计算结果
        val actualHeight = TagUtils.calculateTagHeight(testTag)
        val isUsingFixedHeight = testTag.useFixedHeight && testTag.appearance.tagHeight.value > 0
        
        Text(
            text = "实际高度: ${String.format("%.1f", actualHeight.value)}sp" +
                    if (isUsingFixedHeight) " (固定)" else " (自适应)",
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray
        )
    }
}

/**
 * 综合对比测试
 */
@Composable
private fun ComprehensiveTest() {
    TestSection("🔍 综合对比测试") {
        Text(
            text = "对比相同配置下的不同规则效果",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        val baseTextSize = 14.sp
        val baseTagHeight = 32.dp
        
        val scenarios = listOf(
            "正常模式" to false,
            "强制高度模式" to true
        )
        
        scenarios.forEach { (scenarioName, forceHeight) ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = scenarioName,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF2196F3)
                )
                
                // 不同文字大小的对比
                val textSizes = listOf(10.sp, 12.sp, 14.sp, 16.sp, 18.sp)
                
                textSizes.forEach { textSize ->
                    val testTag = TagBean(
                        type = TagType.FILL,
                        text = "${textSize.value.toInt()}sp",
                        backgroundColor = Color(0xFF9C27B0),
                        textColor = Color.White,
                        appearance = TagAppearance.Default.copy(
                            tagHeight = baseTagHeight,
                            textSize = textSize
                        )
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        TagGroup(
                            tags = listOf(testTag),
                            text = "",
                            textStyle = androidx.compose.ui.text.TextStyle(fontSize = textSize),
                            forceTagHeight = forceHeight,
                            maxLines = 1
                        )
                        
                        val actualHeight = TagUtils.calculateTagHeight(testTag)
                        Text(
                            text = "${textSize.value.toInt()}sp → ${String.format("%.1f", actualHeight.value)}sp",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.Gray
                        )
                    }
                }
            }
        }
    }
}

/**
 * 测试区块组件
 */
@Composable
private fun TestSection(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            content()
        }
    }
}
