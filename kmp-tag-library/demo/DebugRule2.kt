package com.taglib.demo

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.*

/**
 * 调试规则2的具体实现
 */
@Composable
fun DebugRule2() {
    val density = LocalDensity.current
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "🔍 调试规则2实现",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        // 测试用例：10sp文字 vs 50dp标签
        val testTag = TagBean(
            type = TagType.FILL,
            text = "小文字",
            backgroundColor = Color(0xFF2196F3),
            textColor = Color.White,
            appearance = TagAppearance.Default.copy(
                tagHeight = 50.dp,
                textSize = 10.sp
            )
        )
        
        Text(
            text = "测试配置：10sp文字，50dp标签高度",
            style = MaterialTheme.typography.titleMedium
        )
        
        // 步骤1：检查needAdjustTextSize的结果
        val needsAdjustment = TagUtils.needAdjustTextSize(
            tagHeightDp = 50f,
            textSizeSp = 10f,
            density = density.density
        )

        // 详细计算过程
        val textHeightPx = TagUtils.getTextHeight(10f * density.density)
        val tagHeightPx = 50f * density.density

        Text(
            text = "详细计算：",
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Medium
        )

        Text(
            text = "• 文字高度: ${String.format("%.1f", textHeightPx)}px\n" +
                    "• 标签高度: ${String.format("%.1f", tagHeightPx)}px\n" +
                    "• 标签 > 文字: ${tagHeightPx > textHeightPx}",
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray
        )
        
        Text(
            text = "步骤1 - needAdjustTextSize结果: $needsAdjustment",
            style = MaterialTheme.typography.bodyMedium,
            color = if (needsAdjustment) Color(0xFF4CAF50) else Color(0xFFD32F2F)
        )
        
        Text(
            text = "解释: ${if (needsAdjustment) "标签高度 > 文字高度，需要调整" else "标签高度 <= 文字高度，不需要调整"}",
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray
        )
        
        // 步骤2：检查processTagHeightLogic应该如何处理
        val expectedUseFixedHeight = if (needsAdjustment) {
            false // 规则2：文字小于标签，应该自适应
        } else {
            true  // 规则1：文字大于等于标签，应该固定
        }
        
        Text(
            text = "步骤2 - 预期useFixedHeight: $expectedUseFixedHeight",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF2196F3)
        )
        
        Text(
            text = "规则: ${if (expectedUseFixedHeight) "规则1 - 使用固定高度" else "规则2 - 自适应高度"}",
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray
        )
        
        // 步骤3：检查当前TagBean的useFixedHeight值
        Text(
            text = "步骤3 - 当前tagBean.useFixedHeight: ${testTag.useFixedHeight}",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFFFF9800)
        )
        
        // 步骤4：手动创建正确的TagBean
        val correctedTag = testTag.copy(useFixedHeight = expectedUseFixedHeight)
        
        Text(
            text = "步骤4 - 修正后tagBean.useFixedHeight: ${correctedTag.useFixedHeight}",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF9C27B0)
        )
        
        // 步骤5：检查calculateTagHeight的结果
        val originalHeight = TagUtils.calculateTagHeight(testTag)
        val correctedHeight = TagUtils.calculateTagHeight(correctedTag)
        
        Text(
            text = "步骤5 - 原始高度计算: ${String.format("%.1f", originalHeight.value)}sp",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFFD32F2F)
        )
        
        Text(
            text = "步骤5 - 修正后高度计算: ${String.format("%.1f", correctedHeight.value)}sp",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF4CAF50)
        )
        
        // 步骤6：测试processTagHeightLogic
        Text(
            text = "步骤6 - 测试processTagHeightLogic:",
            style = MaterialTheme.typography.titleMedium
        )

        // 手动调用processTagHeightLogic来验证
        val textStyle = androidx.compose.ui.text.TextStyle(fontSize = 10.sp)
        val processedResult = remember {
            // 这里我们需要模拟processTagHeightLogic的调用
            val tags = listOf(testTag)
            val needsAdjustment = TagUtils.needAdjustTextSize(50f, 10f, density.density)

            if (!needsAdjustment) {
                // 规则1：文字 >= 标签，使用固定高度
                tags.map { it.copy(useFixedHeight = true) }
            } else {
                // 规则2：文字 < 标签，使用自适应高度
                tags.map { it.copy(useFixedHeight = false) }
            }
        }

        Text(
            text = "processTagHeightLogic结果: useFixedHeight = ${processedResult.first().useFixedHeight}",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF673AB7)
        )

        // 步骤7：显示实际标签效果
        Text(
            text = "步骤7 - 实际标签显示:",
            style = MaterialTheme.typography.titleMedium
        )
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Column {
                Text(
                    text = "原始标签",
                    style = MaterialTheme.typography.labelMedium
                )
                TagGroup(
                    tags = listOf(testTag),
                    text = "",
                    textStyle = TextStyle(fontSize = 10.sp),
                    maxLines = 1
                )
            }

            Column {
                Text(
                    text = "修正后标签",
                    style = MaterialTheme.typography.labelMedium
                )
                TagGroup(
                    tags = listOf(correctedTag),
                    text = "",
                    textStyle = TextStyle(fontSize = 10.sp),
                    maxLines = 1
                )
            }

            Column {
                Text(
                    text = "processTagHeightLogic处理后",
                    style = MaterialTheme.typography.labelMedium
                )
                TagGroup(
                    tags = processedResult,
                    text = "",
                    textStyle = TextStyle(fontSize = 10.sp),
                    maxLines = 1
                )
            }
        }
        
        // 结论
        Divider()
        
        Text(
            text = "🎯 结论分析",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF2196F3)
        )
        
        val isWorking = correctedHeight.value < 30f
        
        Text(
            text = if (isWorking) {
                "✅ 规则2可以正常工作，问题在于useFixedHeight没有被正确设置"
            } else {
                "❌ 即使设置了useFixedHeight=false，高度计算仍然有问题"
            },
            style = MaterialTheme.typography.bodyMedium,
            color = if (isWorking) Color(0xFF4CAF50) else Color(0xFFD32F2F),
            fontWeight = FontWeight.Medium
        )
        
        if (isWorking) {
            Text(
                text = "需要检查processTagHeightLogic是否正确执行，以及adjustedTags是否正确传递到组件中。",
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
        }
    }
}
