package com.taglib.demo

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.TagUtils

/**
 * 真实字体度量测试
 * 
 * 测试使用Compose TextMeasurer获取的真实字体度量 vs 经验值计算
 */
@Composable
fun RealFontMetricsTest() {
    val density = LocalDensity.current
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Text(
            text = "📏 真实字体度量测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = "对比使用TextMeasurer的真实测量 vs 经验值计算",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 字体高度对比测试
        FontHeightComparisonTest()
        
        // 实际文字度量测试
        ActualTextMetricsTest()
        
        // 不同字体大小的精确测量
        PreciseMeasurementTest()
        
        // 方法说明
        MethodExplanation()
    }
}

/**
 * 字体高度对比测试
 */
@Composable
private fun FontHeightComparisonTest() {
    TestSection("📊 字体高度对比") {
        val fontSizes = listOf(10f, 12f, 14f, 16f, 18f, 20f)
        
        // 表头
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "字体大小",
                modifier = Modifier.width(70.dp),
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "经验值",
                modifier = Modifier.width(70.dp),
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "真实测量",
                modifier = Modifier.width(70.dp),
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "差异",
                modifier = Modifier.width(60.dp),
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
        }
        
        Divider(modifier = Modifier.padding(vertical = 8.dp))
        
        fontSizes.forEach { fontSize ->
            val empiricalHeight = TagUtils.getTextHeightNonComposable(fontSize)
            val realHeight = TagUtils.getTextHeight(fontSize)
            val difference = realHeight - empiricalHeight
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "${fontSize.toInt()}sp",
                    modifier = Modifier.width(70.dp),
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = "${String.format("%.1f", empiricalHeight)}px",
                    modifier = Modifier.width(70.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFFFF9800)
                )
                Text(
                    text = "${String.format("%.1f", realHeight)}px",
                    modifier = Modifier.width(70.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF4CAF50)
                )
                Text(
                    text = "${if (difference >= 0) "+" else ""}${String.format("%.1f", difference)}",
                    modifier = Modifier.width(60.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = if (difference > 0) Color(0xFF4CAF50) else Color(0xFFD32F2F)
                )
            }
        }
    }
}

/**
 * 实际文字度量测试
 */
@Composable
private fun ActualTextMetricsTest() {
    TestSection("🔍 实际文字度量分析") {
        val testTexts = listOf("A", "g", "Ag", "Typography", "gjpqy")
        
        testTexts.forEach { text ->
            val textMetrics = TagUtils.measureTextMetrics(text, 16f)
            
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "文字: \"$text\"",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "尺寸信息",
                            style = MaterialTheme.typography.labelSmall,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF2196F3)
                        )
                        Text(
                            text = "宽度: ${textMetrics.size.width}px\n" +
                                    "高度: ${textMetrics.size.height}px",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                    
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "基线信息",
                            style = MaterialTheme.typography.labelSmall,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF4CAF50)
                        )
                        Text(
                            text = "第一行基线: ${textMetrics.firstBaseline}px\n" +
                                    "最后行基线: ${textMetrics.lastBaseline}px",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
        }
    }
}

/**
 * 不同字体大小的精确测量
 */
@Composable
private fun PreciseMeasurementTest() {
    TestSection("🎯 精确测量效果对比") {
        val testText = "Typography"
        val fontSizes = listOf(12f, 14f, 16f, 18f)
        
        fontSizes.forEach { fontSize ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "${fontSize.toInt()}sp 字体测试",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 经验值计算的容器
                    val empiricalHeight = TagUtils.getTextHeightNonComposable(fontSize) + 4f
                    TestContainer(
                        label = "经验值",
                        height = empiricalHeight.dp,
                        backgroundColor = Color(0xFFFF9800)
                    ) {
                        Text(
                            text = testText,
                            fontSize = fontSize.sp,
                            color = Color.White,
                            maxLines = 1,
                            textAlign = TextAlign.Center
                        )
                    }
                    
                    // 真实测量的容器
                    val realHeight = TagUtils.getTextHeight(fontSize) + 4f
                    TestContainer(
                        label = "真实测量",
                        height = realHeight.dp,
                        backgroundColor = Color(0xFF4CAF50)
                    ) {
                        Text(
                            text = testText,
                            fontSize = fontSize.sp,
                            color = Color.White,
                            maxLines = 1,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(vertical = 2.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 方法说明
 */
@Composable
private fun MethodExplanation() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "🔧 实现方法说明",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF2196F3)
            )
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "经验值计算 (不推荐):",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium,
                color = Color(0xFFFF9800)
            )
            Text(
                text = "fun getTextHeightNonComposable(textSizePx: Float): Float {\n" +
                        "    return textSizePx * 1.0f // 简单估算\n" +
                        "}",
                style = MaterialTheme.typography.bodySmall,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                modifier = Modifier.padding(start = 8.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "真实测量 (推荐):",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF4CAF50)
            )
            Text(
                text = "@Composable\n" +
                        "fun getTextHeight(textSize: Float): Float {\n" +
                        "    val textMeasurer = rememberTextMeasurer()\n" +
                        "    val result = textMeasurer.measure(\n" +
                        "        text = \"Ag\", // 包含ascent和descent\n" +
                        "        style = TextStyle(fontSize = textSize.sp)\n" +
                        "    )\n" +
                        "    return result.size.height.toFloat()\n" +
                        "}",
                style = MaterialTheme.typography.bodySmall,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                modifier = Modifier.padding(start = 8.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "🎯 关键优势:",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = "• 使用Compose的TextMeasurer获取真实度量\n" +
                        "• 考虑实际字体的ascent和descent\n" +
                        "• 支持不同字体和字重的精确测量\n" +
                        "• 与Compose渲染引擎完全一致",
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(start = 8.dp)
            )
        }
    }
}

/**
 * 测试容器组件
 */
@Composable
private fun TestContainer(
    label: String,
    height: androidx.compose.ui.unit.Dp,
    backgroundColor: Color,
    content: @Composable () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = "$label\n${height.value.toInt()}dp",
            style = MaterialTheme.typography.labelSmall,
            color = Color.Gray,
            textAlign = TextAlign.Center
        )
        
        Box(
            modifier = Modifier
                .width(120.dp)
                .height(height)
                .background(backgroundColor, RoundedCornerShape(4.dp))
                .border(1.dp, Color.Gray.copy(alpha = 0.3f), RoundedCornerShape(4.dp)),
            contentAlignment = Alignment.Center
        ) {
            content()
        }
    }
}

/**
 * 测试区块组件
 */
@Composable
private fun TestSection(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            content()
        }
    }
}
