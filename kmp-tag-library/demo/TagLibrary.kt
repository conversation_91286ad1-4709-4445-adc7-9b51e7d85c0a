package com.taglib

/**
 * KMP Tag Library 主入口文件
 * 导出所有公共API
 */

// 核心数据类型
typealias Tag = TagBean
typealias TagStyle = TagAppearance

// 主要组件
// TagGroup 已在 TagCompose.kt 中定义

// 工具类
// TagUtils 已在 TagUtils.kt 中定义

// 标签类型枚举
// TagType 已在 TagType.kt 中定义

/**
 * 库版本信息
 */
object TagLibrary {
    const val VERSION = "1.0.0"
    const val NAME = "KMP Tag Library"
    
    /**
     * 初始化库（如果需要）
     */
    fun initialize() {
        // 预留初始化逻辑
    }
    
    /**
     * 获取库信息
     */
    fun getInfo(): String {
        return "$NAME v$VERSION"
    }
}

/**
 * 快速创建常用标签的工厂方法
 */
object TagFactory {
    
    /**
     * 创建新品标签
     */
    fun newTag(text: String = "新品"): TagBean {
        return TagBean(
            type = TagType.FILL,
            text = text,
            textColor = androidx.compose.ui.graphics.Color.White,
            backgroundColor = androidx.compose.ui.graphics.Color.Red,
            appearance = TagAppearance.Round
        )
    }
    
    /**
     * 创建热销标签
     */
    fun hotTag(text: String = "热销"): TagBean {
        return TagBean(
            type = TagType.FILL,
            text = text,
            textColor = androidx.compose.ui.graphics.Color.White,
            backgroundColor = androidx.compose.ui.graphics.Color(0xFFFF9800),
            appearance = TagAppearance.Round
        )
    }
    
    /**
     * 创建推荐标签
     */
    fun recommendTag(text: String = "推荐"): TagBean {
        return TagBean(
            type = TagType.STROKE,
            text = text,
            textColor = androidx.compose.ui.graphics.Color.Blue,
            borderColor = androidx.compose.ui.graphics.Color.Blue,
            appearance = TagAppearance.Default
        )
    }
    
    /**
     * 创建限时标签
     */
    fun limitedTag(text: String = "限时"): TagBean {
        return TagBean(
            type = TagType.FILL,
            text = text,
            textColor = androidx.compose.ui.graphics.Color.White,
            backgroundColor = androidx.compose.ui.graphics.Color(0xFFE91E63),
            backgroundEndColor = androidx.compose.ui.graphics.Color(0xFFFF5722),
            appearance = TagAppearance.Capsule
        )
    }
    
    /**
     * 创建包邮标签
     */
    fun freeShippingTag(text: String = "包邮"): TagBean {
        return TagBean(
            type = TagType.STROKE,
            text = text,
            textColor = androidx.compose.ui.graphics.Color(0xFF4CAF50),
            borderColor = androidx.compose.ui.graphics.Color(0xFF4CAF50),
            appearance = TagAppearance.Capsule
        )
    }
    
    /**
     * 创建折扣标签
     */
    fun discountTag(text: String): TagBean {
        return TagBean(
            type = TagType.DISCOUNT,
            text = text,
            textColor = androidx.compose.ui.graphics.Color.White,
            backgroundColor = androidx.compose.ui.graphics.Color(0xFFFF5722),
            appearance = TagAppearance.Round
        )
    }
    
    /**
     * 创建积分标签
     */
    fun pointsTag(text: String): TagBean {
        return TagBean(
            type = TagType.POINTS,
            text = text,
            textColor = androidx.compose.ui.graphics.Color.White,
            backgroundColor = androidx.compose.ui.graphics.Color(0xFF9C27B0),
            appearance = TagAppearance.Round
        )
    }
}
