package com.taglib.demo

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.*

/**
 * 快速文字显示测试
 * 
 * 验证修复后的标签组件是否能正常显示文字
 */
@Composable
fun QuickTextTest() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "🔧 快速文字显示测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = "验证标签组件是否能正常显示文字",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 基础标签测试
        BasicTagTest()
        
        // 不同类型标签测试
        DifferentTypesTest()
        
        // 下沉字符测试
        DescenderTest()
    }
}

/**
 * 基础标签测试
 */
@Composable
private fun BasicTagTest() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "📋 基础标签测试",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            val basicTag = TagBean(
                type = TagType.FILL,
                text = "基础测试",
                backgroundColor = Color(0xFF4CAF50),
                textColor = Color.White
            )
            
            TagGroup(
                tags = listOf(basicTag),
                text = "这是一个基础的标签测试",
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                onTagClick = { tag ->
                    println("点击了标签: ${tag.text}")
                }
            )
            
            Text(
                text = "如果您能看到上面的绿色标签显示'基础测试'，说明文字显示正常",
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
        }
    }
}

/**
 * 不同类型标签测试
 */
@Composable
private fun DifferentTypesTest() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "🏷️ 不同类型标签测试",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            val testTags = listOf(
                TagBean(
                    type = TagType.FILL,
                    text = "填充标签",
                    backgroundColor = Color(0xFF2196F3),
                    textColor = Color.White
                ),
                TagBean(
                    type = TagType.STROKE,
                    text = "描边标签",
                    borderColor = Color(0xFFFF9800),
                    textColor = Color(0xFFFF9800)
                ),
                TagBean(
                    type = TagType.FILL_AND_STROKE,
                    text = "填充+描边",
                    backgroundColor = Color(0xFFFFF3E0),
                    borderColor = Color(0xFFFF9800),
                    textColor = Color(0xFFE65100)
                ),
                TagBean(
                    type = TagType.DISCOUNT,
                    text = "折扣标签",
                    backgroundColor = Color(0xFFFFEBEE),
                    borderColor = Color(0xFFD32F2F),
                    textColor = Color(0xFFD32F2F)
                )
            )
            
            testTags.forEach { tag ->
                TagGroup(
                    tags = listOf(tag),
                    text = "测试${tag.type.name}类型",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            Text(
                text = "如果您能看到上面4个不同颜色的标签，说明所有类型都正常",
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
        }
    }
}

/**
 * 下沉字符测试
 */
@Composable
private fun DescenderTest() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "📝 下沉字符测试",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            val descenderTexts = listOf(
                "正常文字",
                "包含g字符",
                "包含j字符",
                "包含p字符",
                "包含q字符",
                "包含y字符",
                "gjpqy全部",
                "Typography"
            )
            
            descenderTexts.forEach { text ->
                val tag = TagBean(
                    type = TagType.FILL,
                    text = text,
                    backgroundColor = Color(0xFF9C27B0),
                    textColor = Color.White
                )
                
                TagGroup(
                    tags = listOf(tag),
                    text = "测试: $text",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            Text(
                text = "检查上面的标签中，包含g,j,p,q,y字符的文字是否完整显示",
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Card(
                colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
            ) {
                Column(modifier = Modifier.padding(8.dp)) {
                    Text(
                        text = "✅ 检查要点",
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF4CAF50)
                    )
                    Text(
                        text = "• 所有标签都应该显示文字\n• 下沉字符(g,j,p,q,y)不应该被截断\n• 文字应该在标签中居中显示",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFF424242)
                    )
                }
            }
        }
    }
}
