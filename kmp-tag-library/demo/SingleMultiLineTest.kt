package com.taglib

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp

/**
 * 单行/多行展示测试
 * 
 * 测试纯标签场景下的单行和多行展示效果
 * 对应原生Android库中TextView的maxLines设置
 */
@Composable
fun SingleMultiLineTestScreen() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "单行/多行展示测试",
            style = MaterialTheme.typography.headlineMedium
        )
        
        Text(
            text = "对应原生Android库中TextView的maxLines和ellipsize设置",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 测试标签数据
        val testTags = listOf(
            TagBean(
                type = TagType.FILL,
                text = "新品",
                textColor = Color.White,
                backgroundColor = Color.Red
            ),
            TagBean(
                type = TagType.STROKE,
                text = "热销",
                textColor = Color.Blue,
                borderColor = Color.Blue
            ),
            TagBean(
                type = TagType.FILL,
                text = "包邮",
                textColor = Color.White,
                backgroundColor = Color.Green
            ),
            TagBean(
                type = TagType.DISCOUNT,
                text = "折省",
                textColor = Color.White,
                backgroundColor = Color.Orange,
                backgroundEndColor = Color.Red
            ),
            TagBean(
                type = TagType.POINTS,
                text = "积分",
                textColor = Color.White,
                backgroundColor = Color.Purple
            )
        )
        
        val longText = "这是一个很长很长的商品名称描述，用来测试多行显示效果，包含了很多详细的商品信息和特色描述"
        
        // 1. 单行显示测试
        Card {
            Column(modifier = Modifier.padding(16.dp)) {
                Text("1. 单行显示测试", style = MaterialTheme.typography.titleMedium)
                Spacer(modifier = Modifier.height(8.dp))
                
                Text("单行 + 省略号 (对应原生: textView.setMaxLines(1) + setEllipsize(END)):")
                TagGroup(
                    tags = testTags,
                    text = longText,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text("单行 + 裁剪 (对应原生: textView.setMaxLines(1) + setEllipsize(null)):")
                TagGroup(
                    tags = testTags,
                    text = longText,
                    maxLines = 1,
                    overflow = TextOverflow.Clip
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text("纯标签单行:")
                TagGroup(
                    tags = testTags,
                    text = "",
                    maxLines = 1
                )
            }
        }
        
        // 2. 多行显示测试
        Card {
            Column(modifier = Modifier.padding(16.dp)) {
                Text("2. 多行显示测试", style = MaterialTheme.typography.titleMedium)
                Spacer(modifier = Modifier.height(8.dp))
                
                Text("最多2行 + 省略号 (对应原生: textView.setMaxLines(2)):")
                TagGroup(
                    tags = testTags,
                    text = longText,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text("最多3行 + 省略号:")
                TagGroup(
                    tags = testTags,
                    text = longText,
                    maxLines = 3,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text("无限制行数 (对应原生: 不设置maxLines):")
                TagGroup(
                    tags = testTags,
                    text = longText,
                    maxLines = Int.MAX_VALUE
                )
            }
        }
        
        // 3. 便捷方法测试
        Card {
            Column(modifier = Modifier.padding(16.dp)) {
                Text("3. 便捷方法单行/多行测试", style = MaterialTheme.typography.titleMedium)
                Spacer(modifier = Modifier.height(8.dp))
                
                Text("ShowRectStart 单行:")
                ShowRectStart(
                    tags = testTags,
                    content = longText,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text("ShowRectEnd 多行:")
                ShowRectEnd(
                    tags = testTags,
                    content = longText,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
        
        // 4. 不同溢出效果对比
        Card {
            Column(modifier = Modifier.padding(16.dp)) {
                Text("4. 溢出效果对比", style = MaterialTheme.typography.titleMedium)
                Spacer(modifier = Modifier.height(8.dp))
                
                Text("TextOverflow.Ellipsis (省略号):")
                TagGroup(
                    tags = testTags,
                    text = longText,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text("TextOverflow.Clip (直接裁剪):")
                TagGroup(
                    tags = testTags,
                    text = longText,
                    maxLines = 1,
                    overflow = TextOverflow.Clip
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text("TextOverflow.Visible (可见溢出):")
                TagGroup(
                    tags = testTags,
                    text = longText,
                    maxLines = 1,
                    overflow = TextOverflow.Visible
                )
            }
        }
        
        // 5. 实际使用场景
        Card {
            Column(modifier = Modifier.padding(16.dp)) {
                Text("5. 实际使用场景", style = MaterialTheme.typography.titleMedium)
                Spacer(modifier = Modifier.height(8.dp))
                
                Text("商品列表项 (单行):")
                TagGroup(
                    tags = listOf(testTags[0], testTags[1]),
                    text = "Apple iPhone 15 Pro Max 256GB 深空黑色",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text("商品详情页 (多行):")
                TagGroup(
                    tags = testTags,
                    text = "Apple iPhone 15 Pro Max 256GB 深空黑色 全新设计 钛金属材质 A17 Pro芯片 专业级摄像系统",
                    maxLines = 3,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text("搜索结果 (2行):")
                TagGroup(
                    tags = listOf(testTags[0], testTags[3]),
                    text = "限时特价 Apple iPhone 15 Pro Max 官方正品 全国联保",
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
        
        // 6. 原生对比说明
        Card(
            colors = CardDefaults.cardColors(containerColor = Color(0xFFE3F2FD))
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "📱 原生Android对应设置",
                    style = MaterialTheme.typography.titleMedium,
                    color = Color(0xFF1976D2)
                )
                
                Text(
                    text = """
                        原生Android设置方式:
                        
                        // 单行显示
                        textView.setMaxLines(1);
                        textView.setEllipsize(TextUtils.TruncateAt.END);
                        TagUtils.showRectStart(context, textView, tags, content);
                        
                        // 多行显示
                        textView.setMaxLines(3);
                        textView.setEllipsize(TextUtils.TruncateAt.END);
                        TagUtils.showRectStart(context, textView, tags, content);
                        
                        Compose版本设置方式:
                        
                        // 单行显示
                        TagGroup(
                            tags = tags,
                            text = content,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        
                        // 多行显示
                        TagGroup(
                            tags = tags,
                            text = content,
                            maxLines = 3,
                            overflow = TextOverflow.Ellipsis
                        )
                        
                        ✅ Compose版本更简洁，参数直接传入即可！
                    """.trimIndent(),
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF1976D2)
                )
            }
        }
    }
}
