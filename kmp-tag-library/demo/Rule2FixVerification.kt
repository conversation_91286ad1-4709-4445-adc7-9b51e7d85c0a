package com.taglib.demo

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.*

/**
 * 规则2修复验证测试
 * 
 * 专门验证："外部文字高度小于设置的固定标签高度 → 标签高度随文字高度自适应"
 */
@Composable
fun Rule2FixVerification() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Text(
            text = "🔧 规则2修复验证",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = "验证：文字高度 < 标签高度 → 标签高度自适应",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 核心测试用例
        CoreTestCase()
        
        // 对比测试
        ComparisonTest()
        
        // 边界测试
        BoundaryTest()
        
        // 实际应用场景
        RealWorldScenario()
    }
}

/**
 * 核心测试用例：10sp文字 vs 50dp标签高度
 */
@Composable
private fun CoreTestCase() {
    TestSection("🎯 核心测试：10sp文字 vs 50dp标签高度") {
        Text(
            text = "这是您提到的具体场景：文字字体10sp，标签高度50dp",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        val testTag = TagBean(
            type = TagType.FILL,
            text = "小文字",
            backgroundColor = Color(0xFF2196F3),
            textColor = Color.White,
            appearance = TagAppearance.Default.copy(
                tagHeight = 50.dp,  // 设置大的标签高度
                textSize = 10.sp    // 小文字
            )
        )
        
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "测试配置：",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = "• 文字大小：10sp\n• 设定标签高度：50dp\n• 预期行为：标签高度应该自适应文字（约14dp），而不是使用50dp",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF424242)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 显示实际标签
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                TagGroup(
                    tags = listOf(testTag),
                    text = "",
                    textStyle = androidx.compose.ui.text.TextStyle(fontSize = 10.sp),
                    maxLines = 1
                )
                
                // 显示计算结果
                Column {
                    val actualHeight = TagUtils.calculateTagHeight(testTag)
                    val isAdaptive = actualHeight.value < 30f // 如果高度小于30sp，说明是自适应的
                    
                    Text(
                        text = "实际高度: ${String.format("%.1f", actualHeight.value)}sp",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isAdaptive) Color(0xFF4CAF50) else Color(0xFFD32F2F)
                    )
                    
                    Text(
                        text = if (isAdaptive) "✅ 自适应成功" else "❌ 仍使用固定高度",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isAdaptive) Color(0xFF4CAF50) else Color(0xFFD32F2F),
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

/**
 * 对比测试：修复前后的行为对比
 */
@Composable
private fun ComparisonTest() {
    TestSection("📊 修复前后对比") {
        Text(
            text = "对比不同文字大小在大标签高度下的表现",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        val testCases = listOf(
            8.sp to "超小文字",
            10.sp to "小文字", 
            12.sp to "中文字",
            14.sp to "普通文字",
            16.sp to "大文字"
        )
        
        val fixedTagHeight = 40.dp
        
        testCases.forEach { (textSize, description) ->
            val testTag = TagBean(
                type = TagType.FILL,
                text = description,
                backgroundColor = Color(0xFF9C27B0),
                textColor = Color.White,
                appearance = TagAppearance.Default.copy(
                    tagHeight = fixedTagHeight,
                    textSize = textSize
                )
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 显示标签
                TagGroup(
                    tags = listOf(testTag),
                    text = "",
                    textStyle = androidx.compose.ui.text.TextStyle(fontSize = textSize),
                    maxLines = 1
                )
                
                // 显示信息
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "$description (${textSize.value.toInt()}sp)",
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = FontWeight.Medium
                    )
                    
                    val actualHeight = TagUtils.calculateTagHeight(testTag)
                    val shouldAdapt = textSize.value < 16f // 小于16sp的文字应该自适应
                    val isAdapting = actualHeight.value < 30f
                    val isCorrect = if (shouldAdapt) isAdapting else !isAdapting
                    
                    Text(
                        text = "高度: ${String.format("%.1f", actualHeight.value)}sp " +
                                if (isCorrect) "✅" else "❌",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isCorrect) Color(0xFF4CAF50) else Color(0xFFD32F2F)
                    )
                }
            }
        }
    }
}

/**
 * 边界测试：临界情况
 */
@Composable
private fun BoundaryTest() {
    TestSection("🔍 边界测试") {
        Text(
            text = "测试文字高度接近标签高度的临界情况",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        val boundaryTests = listOf(
            "文字 << 标签" to 8.sp to 40.dp,   // 文字远小于标签
            "文字 < 标签" to 12.sp to 32.dp,    // 文字小于标签
            "文字 ≈ 标签" to 16.sp to 24.dp,    // 文字接近标签
            "文字 > 标签" to 20.sp to 24.dp,    // 文字大于标签
            "文字 >> 标签" to 24.sp to 20.dp    // 文字远大于标签
        )
        
        boundaryTests.forEach { (description, textSize, tagHeight) ->
            val testTag = TagBean(
                type = TagType.STROKE,
                text = "测试",
                borderColor = Color(0xFFFF9800),
                textColor = Color(0xFFFF9800),
                appearance = TagAppearance.Default.copy(
                    tagHeight = tagHeight,
                    textSize = textSize
                )
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                TagGroup(
                    tags = listOf(testTag),
                    text = "",
                    textStyle = androidx.compose.ui.text.TextStyle(fontSize = textSize),
                    maxLines = 1
                )
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = FontWeight.Medium
                    )
                    
                    val actualHeight = TagUtils.calculateTagHeight(testTag)
                    val expectedBehavior = if (textSize.value * 1.5f < tagHeight.value) "自适应" else "固定"
                    val isAdaptive = actualHeight.value < tagHeight.value * 0.8f
                    val actualBehavior = if (isAdaptive) "自适应" else "固定"
                    val isCorrect = expectedBehavior == actualBehavior
                    
                    Text(
                        text = "${textSize.value.toInt()}sp vs ${tagHeight.value.toInt()}dp → $actualBehavior " +
                                if (isCorrect) "✅" else "❌",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isCorrect) Color(0xFF4CAF50) else Color(0xFFD32F2F)
                    )
                }
            }
        }
    }
}

/**
 * 实际应用场景
 */
@Composable
private fun RealWorldScenario() {
    TestSection("🌍 实际应用场景") {
        Text(
            text = "模拟实际应用中的标签使用场景",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 场景1：商品标签
        Text(
            text = "场景1：商品标签（小文字，大标签高度设置）",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF2196F3)
        )
        
        val productTags = listOf(
            TagBean(
                type = TagType.FILL,
                text = "新品",
                backgroundColor = Color(0xFFE91E63),
                textColor = Color.White,
                appearance = TagAppearance.Default.copy(
                    tagHeight = 36.dp,  // 设计师设置的大高度
                    textSize = 10.sp    // 但实际文字很小
                )
            ),
            TagBean(
                type = TagType.STROKE,
                text = "热销",
                borderColor = Color(0xFFFF5722),
                textColor = Color(0xFFFF5722),
                appearance = TagAppearance.Default.copy(
                    tagHeight = 36.dp,
                    textSize = 10.sp
                )
            )
        )
        
        TagGroup(
            tags = productTags,
            text = "这是一个商品标题，标签应该自适应小文字的高度",
            textStyle = androidx.compose.ui.text.TextStyle(fontSize = 14.sp),
            maxLines = 2
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 场景2：用户标签
        Text(
            text = "场景2：用户等级标签（超小文字）",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF2196F3)
        )
        
        val userTags = listOf(
            TagBean(
                type = TagType.FILL,
                text = "VIP",
                backgroundColor = Color(0xFFFFD700),
                textColor = Color(0xFF333333),
                appearance = TagAppearance.Default.copy(
                    tagHeight = 32.dp,  // 统一的标签高度
                    textSize = 8.sp     // 超小文字
                )
            )
        )
        
        TagGroup(
            tags = userTags,
            text = "用户名称",
            textStyle = androidx.compose.ui.text.TextStyle(fontSize = 16.sp),
            maxLines = 1
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        Text(
            text = "✅ 现在标签会自适应小文字的实际高度，而不是使用设计师设置的大高度，这样看起来更加协调！",
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF4CAF50),
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 测试区块组件
 */
@Composable
private fun TestSection(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            content()
        }
    }
}
