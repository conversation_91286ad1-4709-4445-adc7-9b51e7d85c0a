package com.taglib.demo

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.*

/**
 * 简单的规则2测试
 */
@Composable
fun SimpleRule2Test() {
    val density = LocalDensity.current
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "🧪 简单规则2测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = "测试：10sp文字 vs 50dp标签高度",
            style = MaterialTheme.typography.titleMedium
        )
        
        // 创建测试标签
        val testTag = TagBean(
            type = TagType.FILL,
            text = "小文字",
            backgroundColor = Color(0xFF2196F3),
            textColor = Color.White,
            appearance = TagAppearance.Default.copy(
                tagHeight = 50.dp,
                textSize = 10.sp
            )
        )
        
        // 测试1：直接计算needAdjustTextSize
        val needsAdjustment = TagUtils.needAdjustTextSize(
            tagHeightDp = 50f,
            textSizeSp = 10f,
            density = density.density
        )
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "测试1：needAdjustTextSize计算",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                
                Text(
                    text = "结果: $needsAdjustment",
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (needsAdjustment) Color(0xFF4CAF50) else Color(0xFFD32F2F)
                )
                
                Text(
                    text = "预期: true (因为50dp > 10sp对应的高度)",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Gray
                )
            }
        }
        
        // 测试2：手动设置useFixedHeight
        val manualTag = testTag.copy(useFixedHeight = false)
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "测试2：手动设置useFixedHeight = false",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                
                val manualHeight = TagUtils.calculateTagHeight(manualTag)
                
                Text(
                    text = "计算高度: ${String.format("%.1f", manualHeight.value)}sp",
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (manualHeight.value < 30f) Color(0xFF4CAF50) else Color(0xFFD32F2F)
                )
                
                Text(
                    text = "预期: 约14sp (自适应文字高度)",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Gray
                )
                
                // 显示标签
                TagGroup(
                    tags = listOf(manualTag),
                    text = "",
                    textStyle = TextStyle(fontSize = 10.sp),
                    maxLines = 1
                )
            }
        }
        
        // 测试3：使用TagGroup的完整流程
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "测试3：TagGroup完整流程",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                
                Text(
                    text = "原始标签 (useFixedHeight=${testTag.useFixedHeight}):",
                    style = MaterialTheme.typography.bodySmall
                )
                
                TagGroup(
                    tags = listOf(testTag),
                    text = "",
                    textStyle = TextStyle(fontSize = 10.sp),
                    maxLines = 1
                )
                
                Text(
                    text = "如果规则2正确工作，这个标签应该显示为自适应高度（约14sp），而不是50dp",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Gray
                )
            }
        }
        
        // 测试4：对比不同useFixedHeight的效果
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "测试4：对比useFixedHeight的效果",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Column {
                        Text(
                            text = "useFixedHeight=true",
                            style = MaterialTheme.typography.labelSmall
                        )
                        TagGroup(
                            tags = listOf(testTag.copy(useFixedHeight = true)),
                            text = "",
                            textStyle = TextStyle(fontSize = 10.sp),
                            maxLines = 1
                        )
                        val fixedHeight = TagUtils.calculateTagHeight(testTag.copy(useFixedHeight = true))
                        Text(
                            text = "${String.format("%.1f", fixedHeight.value)}sp",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.Gray
                        )
                    }
                    
                    Column {
                        Text(
                            text = "useFixedHeight=false",
                            style = MaterialTheme.typography.labelSmall
                        )
                        TagGroup(
                            tags = listOf(testTag.copy(useFixedHeight = false)),
                            text = "",
                            textStyle = TextStyle(fontSize = 10.sp),
                            maxLines = 1
                        )
                        val adaptiveHeight = TagUtils.calculateTagHeight(testTag.copy(useFixedHeight = false))
                        Text(
                            text = "${String.format("%.1f", adaptiveHeight.value)}sp",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.Gray
                        )
                    }
                }
            }
        }
        
        // 结论
        Divider()
        
        val isWorking = run {
            val adaptiveHeight = TagUtils.calculateTagHeight(testTag.copy(useFixedHeight = false))
            val fixedHeight = TagUtils.calculateTagHeight(testTag.copy(useFixedHeight = true))
            adaptiveHeight.value < fixedHeight.value && adaptiveHeight.value < 30f
        }
        
        Text(
            text = "🎯 测试结论",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF2196F3)
        )
        
        Text(
            text = if (isWorking) {
                "✅ calculateTagHeight逻辑正确，问题可能在processTagHeightLogic的调用或传递"
            } else {
                "❌ calculateTagHeight逻辑有问题，需要进一步检查"
            },
            style = MaterialTheme.typography.bodyMedium,
            color = if (isWorking) Color(0xFF4CAF50) else Color(0xFFD32F2F),
            fontWeight = FontWeight.Medium
        )
    }
}
