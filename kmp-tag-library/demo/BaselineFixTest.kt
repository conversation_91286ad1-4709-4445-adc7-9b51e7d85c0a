package com.taglib.demo

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.*
import com.taglib.components.SimpleVerticalCenterText
import com.taglib.components.VerticalCenterText

/**
 * 基线修复效果测试
 * 
 * 专门测试文字下半部分被截断问题的修复效果
 */
@Composable
fun BaselineFixTest() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Text(
            text = "🔧 基线修复效果测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = "测试修复后的标签组件是否能正确显示文字，特别关注包含下沉字符(g,j,p,q,y)的文字",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 下沉字符测试
        DescenderCharacterTest()
        
        // 不同字体大小对比测试
        FontSizeComparisonTest()
        
        // 固定高度测试
        FixedHeightComparisonTest()
        
        // 实际标签效果测试
        RealTagEffectTest()
    }
}

/**
 * 下沉字符测试
 * 测试包含g,j,p,q,y等下沉字符的文字显示效果
 */
@Composable
private fun DescenderCharacterTest() {
    TestSection("📝 下沉字符测试") {
        val testTexts = listOf(
            "正常文字",
            "包含g字符",
            "包含j字符", 
            "包含p字符",
            "包含q字符",
            "包含y字符",
            "gjpqy全部",
            "Typography",
            "Programming"
        )
        
        testTexts.forEach { text ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = text,
                    modifier = Modifier.width(100.dp),
                    style = MaterialTheme.typography.bodySmall
                )
                
                // 使用修复后的标签
                val tag = TagBean(
                    type = TagType.FILL,
                    text = text,
                    backgroundColor = Color(0xFF2196F3),
                    textColor = Color.White,
                    appearance = TagAppearance.Default.copy(
                        tagHeight = 32.dp,
                        textSize = 14.sp
                    ),
                    useFixedHeight = true
                )
                
                TagGroup(
                    tags = listOf(tag),
                    text = "",
                    maxLines = 1
                )
            }
        }
    }
}

/**
 * 字体大小对比测试
 */
@Composable
private fun FontSizeComparisonTest() {
    TestSection("📏 字体大小对比测试") {
        val fontSizes = listOf(10.sp, 12.sp, 14.sp, 16.sp, 18.sp, 20.sp)
        
        fontSizes.forEach { fontSize ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "字体大小: ${fontSize.value.toInt()}sp",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 原始Text组件（对比用）
                    Box(
                        modifier = Modifier
                            .height(32.dp)
                            .background(
                                Color(0xFFFFEBEE),
                                RoundedCornerShape(4.dp)
                            )
                            .border(
                                1.dp,
                                Color(0xFFE57373),
                                RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 8.dp),
                        contentAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "原始Text",
                            fontSize = fontSize,
                            color = Color(0xFFD32F2F),
                            maxLines = 1
                        )
                    }
                    
                    // 修复后的标签
                    val tag = TagBean(
                        type = TagType.FILL,
                        text = "修复后gjpqy",
                        backgroundColor = Color(0xFF4CAF50),
                        textColor = Color.White,
                        appearance = TagAppearance.Default.copy(
                            tagHeight = 32.dp,
                            textSize = fontSize
                        ),
                        useFixedHeight = true
                    )
                    
                    TagGroup(
                        tags = listOf(tag),
                        text = "",
                        maxLines = 1
                    )
                }
            }
        }
    }
}

/**
 * 固定高度对比测试
 */
@Composable
private fun FixedHeightComparisonTest() {
    TestSection("📐 固定高度对比测试") {
        val heights = listOf(20.dp, 24.dp, 28.dp, 32.dp, 36.dp, 40.dp)
        
        heights.forEach { height ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "容器高度: ${height.value.toInt()}dp",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 简单居中版本（对比用）
                    Box(
                        modifier = Modifier
                            .height(height)
                            .background(
                                Color(0xFFFFF3E0),
                                RoundedCornerShape(4.dp)
                            )
                            .border(
                                1.dp,
                                Color(0xFFFFB74D),
                                RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 8.dp),
                        contentAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "简单居中gjpqy",
                            fontSize = 14.sp,
                            color = Color(0xFFE65100),
                            maxLines = 1
                        )
                    }
                    
                    // 精确基线修复版本
                    val tag = TagBean(
                        type = TagType.FILL,
                        text = "基线修复gjpqy",
                        backgroundColor = Color(0xFF9C27B0),
                        textColor = Color.White,
                        appearance = TagAppearance.Default.copy(
                            tagHeight = height,
                            textSize = 14.sp
                        ),
                        useFixedHeight = true
                    )
                    
                    TagGroup(
                        tags = listOf(tag),
                        text = "",
                        maxLines = 1
                    )
                }
            }
        }
    }
}

/**
 * 实际标签效果测试
 */
@Composable
private fun RealTagEffectTest() {
    TestSection("🏷️ 实际标签效果测试") {
        val testTags = listOf(
            TagBean(
                type = TagType.FILL,
                text = "Typography",
                backgroundColor = Color(0xFF4CAF50),
                textColor = Color.White
            ),
            TagBean(
                type = TagType.STROKE,
                text = "Programming",
                borderColor = Color(0xFF2196F3),
                textColor = Color(0xFF2196F3)
            ),
            TagBean(
                type = TagType.FILL_AND_STROKE,
                text = "gjpqy测试",
                backgroundColor = Color(0xFFFFF3E0),
                borderColor = Color(0xFFFF9800),
                textColor = Color(0xFFE65100)
            ),
            TagBean(
                type = TagType.DISCOUNT,
                text = "Debugging",
                backgroundColor = Color(0xFFFFEBEE),
                borderColor = Color(0xFFD32F2F),
                textColor = Color(0xFFD32F2F)
            )
        )
        
        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
            testTags.forEach { tag ->
                TagGroup(
                    tags = listOf(tag),
                    text = "检查${tag.text}文字是否完整显示，特别是下沉字符部分",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
        ) {
            Column(modifier = Modifier.padding(12.dp)) {
                Text(
                    text = "✅ 修复验证要点",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF4CAF50)
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "• 下沉字符(g,j,p,q,y)的下半部分应该完整显示\n" +
                            "• 不同字体大小的文字都应该在标签中垂直居中\n" +
                            "• 固定高度标签中的文字应该精确居中\n" +
                            "• 文字的视觉中心应该与容器的视觉中心对齐",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF424242)
                )
            }
        }
    }
}

/**
 * 测试区块组件
 */
@Composable
private fun TestSection(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            content()
        }
    }
}
