package com.taglib.demo

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.TagUtils

/**
 * 文字高度计算测试
 * 
 * 测试基于字体度量的精确文字高度计算
 */
@Composable
fun TextHeightCalculationTest() {
    val density = LocalDensity.current
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Text(
            text = "📏 文字高度计算测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = "对比简化计算(textSize * 1.2)和基于字体度量的精确计算",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 不同字体大小的高度计算对比
        FontSizeHeightTest(density)
        
        // 实际文字显示效果对比
        ActualDisplayTest()
        
        // 下沉字符高度需求测试
        DescenderHeightTest()
        
        // 计算公式说明
        CalculationFormulaExplanation()
    }
}

/**
 * 不同字体大小的高度计算对比
 */
@Composable
private fun FontSizeHeightTest(density: androidx.compose.ui.unit.Density) {
    TestSection("📊 字体大小 vs 文字高度对比") {
        val fontSizes = listOf(10f, 12f, 14f, 16f, 18f, 20f, 24f)
        
        // 表头
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "字体大小",
                modifier = Modifier.width(80.dp),
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "简化计算",
                modifier = Modifier.width(80.dp),
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "精确计算",
                modifier = Modifier.width(80.dp),
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "差异",
                modifier = Modifier.width(60.dp),
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
        }
        
        Divider(modifier = Modifier.padding(vertical = 8.dp))
        
        fontSizes.forEach { fontSize ->
            val simplifiedHeight = fontSize * 1.2f
            val preciseHeight = TagUtils.getTextHeight(fontSize)
            val difference = preciseHeight - simplifiedHeight
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "${fontSize.toInt()}sp",
                    modifier = Modifier.width(80.dp),
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = "${String.format("%.1f", simplifiedHeight)}px",
                    modifier = Modifier.width(80.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFFFF9800)
                )
                Text(
                    text = "${String.format("%.1f", preciseHeight)}px",
                    modifier = Modifier.width(80.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF4CAF50)
                )
                Text(
                    text = "${if (difference >= 0) "+" else ""}${String.format("%.1f", difference)}",
                    modifier = Modifier.width(60.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = if (difference > 0) Color(0xFF4CAF50) else Color(0xFFD32F2F)
                )
            }
        }
    }
}

/**
 * 实际文字显示效果对比
 */
@Composable
private fun ActualDisplayTest() {
    TestSection("🎯 实际显示效果对比") {
        val testTexts = listOf("Normal", "Typography", "gjpqy", "Programming")
        
        testTexts.forEach { text ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "测试文字: $text",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 简化计算的容器高度
                    val simplifiedHeight = 14f * 1.2f + 4f // 14sp字体 + 4px padding
                    TestContainer(
                        label = "简化计算",
                        height = simplifiedHeight.dp,
                        backgroundColor = Color(0xFFFF9800)
                    ) {
                        Text(
                            text = text,
                            fontSize = 14.sp,
                            color = Color.White,
                            maxLines = 1,
                            textAlign = TextAlign.Center
                        )
                    }
                    
                    // 精确计算的容器高度
                    val preciseHeight = TagUtils.getTextHeight(14f) + 4f // 精确高度 + 4px padding
                    TestContainer(
                        label = "精确计算",
                        height = preciseHeight.dp,
                        backgroundColor = Color(0xFF4CAF50)
                    ) {
                        Text(
                            text = text,
                            fontSize = 14.sp,
                            color = Color.White,
                            maxLines = 1,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(vertical = 2.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 下沉字符高度需求测试
 */
@Composable
private fun DescenderHeightTest() {
    TestSection("📝 下沉字符高度需求") {
        val fontSize = 16f
        val fontMetrics = TagUtils.FontMetrics(
            ascent = fontSize * -0.8f,
            descent = fontSize * 0.2f,
            leading = fontSize * 0.1f
        )
        
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "以16sp字体为例：",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "字体度量分析",
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF2196F3)
                    )
                    Text(
                        text = "• ascent: ${fontMetrics.ascent}px (基线上方)\n" +
                                "• descent: ${fontMetrics.descent}px (基线下方)\n" +
                                "• 总高度: ${fontMetrics.descent - fontMetrics.ascent}px",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "下沉字符需求",
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF4CAF50)
                    )
                    Text(
                        text = "• g,j,p,q,y需要descent空间\n" +
                                "• descent占总高度的20%\n" +
                                "• 简化计算可能空间不足",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
    }
}

/**
 * 计算公式说明
 */
@Composable
private fun CalculationFormulaExplanation() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "📐 计算公式说明",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF2196F3)
            )
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "简化计算 (修改前):",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium,
                color = Color(0xFFFF9800)
            )
            Text(
                text = "textHeight = textSize * 1.2",
                style = MaterialTheme.typography.bodySmall,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                modifier = Modifier.padding(start = 8.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "精确计算 (修改后):",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF4CAF50)
            )
            Text(
                text = "ascent = textSize * -0.8  // 基线上方80%\n" +
                        "descent = textSize * 0.2   // 基线下方20%\n" +
                        "textHeight = descent - ascent = textSize * 1.0",
                style = MaterialTheme.typography.bodySmall,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                modifier = Modifier.padding(start = 8.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "🎯 关键优势:",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = "• 基于Android原生字体度量\n" +
                        "• 为下沉字符预留正确的descent空间\n" +
                        "• 与原生库getTextHeight()方法一致\n" +
                        "• 支持不同字体的精确计算",
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(start = 8.dp)
            )
        }
    }
}

/**
 * 测试容器组件
 */
@Composable
private fun TestContainer(
    label: String,
    height: androidx.compose.ui.unit.Dp,
    backgroundColor: Color,
    content: @Composable () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = "$label\n${height.value.toInt()}dp",
            style = MaterialTheme.typography.labelSmall,
            color = Color.Gray,
            textAlign = TextAlign.Center
        )
        
        Box(
            modifier = Modifier
                .width(100.dp)
                .height(height)
                .background(backgroundColor, RoundedCornerShape(4.dp))
                .border(1.dp, Color.Gray.copy(alpha = 0.3f), RoundedCornerShape(4.dp)),
            contentAlignment = Alignment.Center
        ) {
            content()
        }
    }
}

/**
 * 测试区块组件
 */
@Composable
private fun TestSection(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            content()
        }
    }
}
