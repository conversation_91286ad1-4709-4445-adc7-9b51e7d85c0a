package com.taglib.demo

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.*

/**
 * 文字截断问题修复测试
 * 
 * 测试修复后的标签组件是否能正确显示文字，不再出现下半部分被截断的问题
 */
@Composable
fun TextClippingFixTest() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Text(
            text = "🔧 文字截断问题修复测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = "测试各种标签类型的文字显示是否正常，特别关注文字下半部分是否被截断",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 测试不同字体大小的标签
        FontSizeTest()
        
        // 测试不同标签类型
        TagTypeTest()
        
        // 测试固定高度标签
        FixedHeightTest()
        
        // 测试长文字标签
        LongTextTest()
        
        // 对比测试：修复前后效果
        ComparisonTest()
    }
}

/**
 * 字体大小测试
 */
@Composable
private fun FontSizeTest() {
    TestSection("📏 字体大小测试") {
        val fontSizes = listOf(10.sp, 12.sp, 14.sp, 16.sp, 18.sp, 20.sp)
        
        fontSizes.forEach { fontSize ->
            val tag = TagBean(
                type = TagType.FILL,
                text = "字体${fontSize.value.toInt()}sp",
                backgroundColor = Color(0xFF2196F3),
                textColor = Color.White,
                appearance = TagAppearance.Default.copy(textSize = fontSize)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "字体${fontSize.value.toInt()}sp:",
                    modifier = Modifier.width(100.dp),
                    style = MaterialTheme.typography.bodySmall
                )
                
                TagGroup(
                    tags = listOf(tag),
                    text = "测试文字显示效果",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

/**
 * 标签类型测试
 */
@Composable
private fun TagTypeTest() {
    TestSection("🏷️ 标签类型测试") {
        val testTags = listOf(
            TagBean(
                type = TagType.FILL,
                text = "填充标签",
                backgroundColor = Color(0xFF4CAF50),
                textColor = Color.White
            ),
            TagBean(
                type = TagType.STROKE,
                text = "描边标签",
                borderColor = Color(0xFF2196F3),
                textColor = Color(0xFF2196F3)
            ),
            TagBean(
                type = TagType.FILL_AND_STROKE,
                text = "填充+描边",
                backgroundColor = Color(0xFFFFF3E0),
                borderColor = Color(0xFFFF9800),
                textColor = Color(0xFFE65100)
            ),
            TagBean(
                type = TagType.DISCOUNT,
                text = "折扣标签",
                backgroundColor = Color(0xFFFFEBEE),
                borderColor = Color(0xFFD32F2F),
                textColor = Color(0xFFD32F2F)
            ),
            TagBean(
                type = TagType.POINTS,
                text = "积分标签",
                backgroundColor = Color(0xFFE8F5E8),
                textColor = Color(0xFF2E7D32)
            )
        )
        
        testTags.forEach { tag ->
            TagGroup(
                tags = listOf(tag),
                text = "检查${tag.type.name}文字是否完整显示",
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Spacer(modifier = Modifier.height(8.dp))
        }
    }
}

/**
 * 固定高度测试
 */
@Composable
private fun FixedHeightTest() {
    TestSection("📐 固定高度测试") {
        val heights = listOf(20.dp, 24.dp, 28.dp, 32.dp, 36.dp)
        
        heights.forEach { height ->
            val tag = TagBean(
                type = TagType.FILL,
                text = "高度${height.value.toInt()}dp",
                backgroundColor = Color(0xFF9C27B0),
                textColor = Color.White,
                appearance = TagAppearance.Default.copy(tagHeight = height),
                useFixedHeight = true
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "高度${height.value.toInt()}dp:",
                    modifier = Modifier.width(100.dp),
                    style = MaterialTheme.typography.bodySmall
                )
                
                TagGroup(
                    tags = listOf(tag),
                    text = "固定高度文字测试",
                    maxLines = 1
                )
            }
        }
    }
}

/**
 * 长文字测试
 */
@Composable
private fun LongTextTest() {
    TestSection("📝 长文字测试") {
        val longTexts = listOf(
            "短文字",
            "中等长度的文字内容",
            "这是一个很长很长的标签文字内容测试",
            "超级长的标签文字内容用来测试文字显示是否会被截断或者出现其他显示问题"
        )
        
        longTexts.forEach { text ->
            val tag = TagBean(
                type = TagType.FILL,
                text = text,
                backgroundColor = Color(0xFFFF5722),
                textColor = Color.White
            )
            
            TagGroup(
                tags = listOf(tag),
                text = "长文字测试",
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Spacer(modifier = Modifier.height(8.dp))
        }
    }
}

/**
 * 对比测试
 */
@Composable
private fun ComparisonTest() {
    TestSection("🔄 修复效果对比") {
        Text(
            text = "以下标签使用了VerticalCenterText修复文字截断问题：",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF4CAF50),
            fontWeight = FontWeight.Medium
        )
        
        val testTag = TagBean(
            type = TagType.FILL,
            text = "修复后效果",
            backgroundColor = Color(0xFF4CAF50),
            textColor = Color.White,
            appearance = TagAppearance.Default.copy(
                textSize = 16.sp,
                tagHeight = 32.dp
            ),
            useFixedHeight = true
        )
        
        TagGroup(
            tags = listOf(testTag),
            text = "文字应该完整显示，不会被截断",
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
        ) {
            Column(modifier = Modifier.padding(12.dp)) {
                Text(
                    text = "✅ 修复内容",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF4CAF50)
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "• 所有标签组件(FillTag、StrokeTag、DiscountTag、PointsTag)已使用VerticalCenterText\n" +
                            "• 实现了精确的垂直居中对齐，模拟Android原生的基线调整\n" +
                            "• 修复了文字下半部分被截断的问题\n" +
                            "• 支持固定高度和自适应高度两种模式",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF424242)
                )
            }
        }
    }
}

/**
 * 测试区块组件
 */
@Composable
private fun TestSection(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            content()
        }
    }
}
