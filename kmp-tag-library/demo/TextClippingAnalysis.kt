package com.taglib.demo

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.*

/**
 * 文字截断问题深度分析测试
 * 
 * 专门测试和对比不同实现方式的文字显示效果
 */
@Composable
fun TextClippingAnalysis() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Text(
            text = "🔬 文字截断问题深度分析",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = "对比不同实现方式，分析文字截断问题的根本原因和解决效果",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 下沉字符对比测试
        DescenderComparisonTest()
        
        // 不同字体大小对比
        FontSizeClippingTest()
        
        // 固定高度容器测试
        FixedHeightClippingTest()
        
        // 实际标签效果验证
        RealTagClippingTest()
    }
}

/**
 * 下沉字符对比测试
 */
@Composable
private fun DescenderComparisonTest() {
    TestSection("📝 下沉字符对比测试") {
        val testTexts = listOf(
            "正常ABC",
            "下沉gjpqy", 
            "Typography",
            "Programming",
            "Debugging"
        )
        
        testTexts.forEach { text ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "测试文字: $text",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 1. 普通Box居中（对比用）
                    Box(
                        modifier = Modifier
                            .height(32.dp)
                            .background(
                                Color(0xFFFFEBEE),
                                RoundedCornerShape(4.dp)
                            )
                            .border(
                                1.dp,
                                Color(0xFFE57373),
                                RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = text,
                            fontSize = 14.sp,
                            color = Color(0xFFD32F2F),
                            maxLines = 1,
                            textAlign = TextAlign.Center
                        )
                    }
                    
                    Text(
                        text = "vs",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray
                    )
                    
                    // 2. 修复后的标签
                    val tag = TagBean(
                        type = TagType.FILL,
                        text = text,
                        backgroundColor = Color(0xFF4CAF50),
                        textColor = Color.White,
                        appearance = TagAppearance.Default.copy(
                            tagHeight = 32.dp,
                            textSize = 14.sp
                        ),
                        useFixedHeight = true
                    )
                    
                    TagGroup(
                        tags = listOf(tag),
                        text = "",
                        maxLines = 1
                    )
                }
                
                Text(
                    text = "左侧：普通Box居中，右侧：修复后标签",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Gray
                )
            }
        }
    }
}

/**
 * 不同字体大小截断测试
 */
@Composable
private fun FontSizeClippingTest() {
    TestSection("📏 字体大小截断测试") {
        val fontSizes = listOf(10.sp, 12.sp, 14.sp, 16.sp, 18.sp, 20.sp)
        
        fontSizes.forEach { fontSize ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "字体大小: ${fontSize.value.toInt()}sp",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                val tag = TagBean(
                    type = TagType.FILL,
                    text = "gjpqy测试",
                    backgroundColor = Color(0xFF2196F3),
                    textColor = Color.White,
                    appearance = TagAppearance.Default.copy(
                        tagHeight = 32.dp,
                        textSize = fontSize
                    ),
                    useFixedHeight = true
                )
                
                TagGroup(
                    tags = listOf(tag),
                    text = "检查${fontSize.value.toInt()}sp字体的下沉字符是否被截断",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

/**
 * 固定高度容器截断测试
 */
@Composable
private fun FixedHeightClippingTest() {
    TestSection("📐 固定高度容器截断测试") {
        val heights = listOf(20.dp, 24.dp, 28.dp, 32.dp, 36.dp, 40.dp)
        
        heights.forEach { height ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "容器高度: ${height.value.toInt()}dp",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                val tag = TagBean(
                    type = TagType.FILL,
                    text = "Typography",
                    backgroundColor = Color(0xFF9C27B0),
                    textColor = Color.White,
                    appearance = TagAppearance.Default.copy(
                        tagHeight = height,
                        textSize = 14.sp
                    ),
                    useFixedHeight = true
                )
                
                TagGroup(
                    tags = listOf(tag),
                    text = "检查${height.value.toInt()}dp高度下文字是否完整",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

/**
 * 实际标签截断效果验证
 */
@Composable
private fun RealTagClippingTest() {
    TestSection("🏷️ 实际标签截断效果验证") {
        val criticalTexts = listOf(
            "Typography" to "包含y下沉字符",
            "Programming" to "包含g下沉字符", 
            "Debugging" to "包含g下沉字符",
            "jQuery" to "包含j,q,y下沉字符",
            "Python" to "包含y下沉字符",
            "gjpqy全测试" to "所有下沉字符"
        )
        
        criticalTexts.forEach { (text, description) ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "$text ($description)",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 填充标签
                    val fillTag = TagBean(
                        type = TagType.FILL,
                        text = text,
                        backgroundColor = Color(0xFF4CAF50),
                        textColor = Color.White
                    )
                    
                    TagGroup(
                        tags = listOf(fillTag),
                        text = "",
                        maxLines = 1
                    )
                    
                    // 描边标签
                    val strokeTag = TagBean(
                        type = TagType.STROKE,
                        text = text,
                        borderColor = Color(0xFF2196F3),
                        textColor = Color(0xFF2196F3)
                    )
                    
                    TagGroup(
                        tags = listOf(strokeTag),
                        text = "",
                        maxLines = 1
                    )
                    
                    // 折扣标签
                    val discountTag = TagBean(
                        type = TagType.DISCOUNT,
                        text = text,
                        backgroundColor = Color(0xFFFFEBEE),
                        borderColor = Color(0xFFD32F2F),
                        textColor = Color(0xFFD32F2F)
                    )
                    
                    TagGroup(
                        tags = listOf(discountTag),
                        text = "",
                        maxLines = 1
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
        ) {
            Column(modifier = Modifier.padding(12.dp)) {
                Text(
                    text = "🎯 检查要点",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF4CAF50)
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "• 所有下沉字符(g,j,p,q,y)的下半部分应该完整显示\n" +
                            "• 文字应该在标签中真正的垂直居中\n" +
                            "• 不同类型的标签都应该有一致的显示效果\n" +
                            "• 文字不应该贴着标签的上边缘或下边缘",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF424242)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "🔍 如果仍有截断问题",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFFFF9800)
                )
                Text(
                    text = "• 检查容器高度是否足够\n" +
                            "• 确认字体度量计算是否准确\n" +
                            "• 验证Y坐标计算是否正确",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF424242)
                )
            }
        }
    }
}

/**
 * 测试区块组件
 */
@Composable
private fun TestSection(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            content()
        }
    }
}
