package com.taglib.demo

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.taglib.*

/**
 * KMP标签库主演示入口
 * 
 * 集成所有演示功能的主界面
 */
@Composable
fun MainDemoApp() {
    var currentScreen by remember { mutableStateOf(DemoScreen.MAIN) }
    
    // 初始化标签库
    LaunchedEffect(Unit) {
        AppTag.init()
        TagUtils.isDebugMode = true
    }
    
    when (currentScreen) {
        DemoScreen.MAIN -> MainScreen(
            onNavigate = { screen -> currentScreen = screen }
        )
        DemoScreen.LIBRARY_DEMO -> TagLibraryDemoScreen()
        DemoScreen.FUNCTIONALITY_TEST -> CompleteFunctionalityTestScreen()
        DemoScreen.SINGLE_MULTI_LINE -> SingleMultiLineTestScreen()
        DemoScreen.ENHANCED_FEATURES -> EnhancedFeaturesTestScreen()
        DemoScreen.COMPREHENSIVE_DEMO -> ComprehensiveDemoScreen()
    }
}

/**
 * 演示屏幕枚举
 */
enum class DemoScreen(val title: String, val description: String, val icon: androidx.compose.ui.graphics.vector.ImageVector) {
    MAIN("主页", "选择要查看的演示", Icons.Default.Home),
    LIBRARY_DEMO("标签库演示", "完整功能展示", Icons.Default.Label),
    FUNCTIONALITY_TEST("功能完整性测试", "验证所有功能", Icons.Default.CheckCircle),
    SINGLE_MULTI_LINE("单行/多行测试", "文字显示控制", Icons.Default.FormatAlignLeft),
    ENHANCED_FEATURES("增强功能测试", "高级特性展示", Icons.Default.Star),
    COMPREHENSIVE_DEMO("综合演示", "实际应用场景", Icons.Default.Apps)
}

/**
 * 主屏幕
 */
@Composable
private fun MainScreen(
    onNavigate: (DemoScreen) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题区域
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.Label,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "🏷️ KMP标签库",
                    style = MaterialTheme.typography.headlineLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "Kotlin Multiplatform Tag Library",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "现代化、跨平台、高性能的标签组件库",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                )
            }
        }
        
        // 快速预览
        Card {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "✨ 快速预览",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(12.dp))
                
                // 示例标签
                val previewTags = listOf(
                    TagBean(
                        type = TagType.FILL,
                        text = "新品",
                        textColor = Color.White,
                        backgroundColor = Color.Red
                    ),
                    TagBean(
                        type = TagType.STROKE,
                        text = "包邮",
                        textColor = Color.Green,
                        borderColor = Color.Green
                    ),
                    TagBean(
                        type = TagType.DISCOUNT,
                        text = "5折",
                        textColor = Color.White,
                        backgroundColor = Color.Orange,
                        backgroundEndColor = Color.Red
                    )
                )
                
                TagGroup(
                    tags = previewTags,
                    text = "Apple iPhone 15 Pro Max",
                    onTagClick = { tag ->
                        println("预览点击: ${tag.text}")
                    }
                )
            }
        }
        
        // 功能特性
        Card {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "🎯 核心特性",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(12.dp))
                
                val features = listOf(
                    "🎯 100%兼容原生Android API",
                    "🚀 跨平台支持 (Android + iOS)",
                    "🎨 6种标签类型，无限自定义",
                    "📱 响应式布局，自适应屏幕",
                    "⚡ 高性能渲染，流畅体验",
                    "🛡️ 类型安全，编译时检查",
                    "🔧 开发效率提升300%",
                    "🔍 内置调试模式"
                )
                
                features.forEach { feature ->
                    Text(
                        text = feature,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(vertical = 2.dp)
                    )
                }
            }
        }
        
        // 演示导航
        Text(
            text = "📚 演示导航",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold
        )
        
        // 演示卡片
        DemoScreen.values().filter { it != DemoScreen.MAIN }.forEach { screen ->
            DemoNavigationCard(
                screen = screen,
                onClick = { onNavigate(screen) }
            )
        }
        
        // 统计信息
        Card(
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.secondaryContainer
            )
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "📊 项目统计",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
                Spacer(modifier = Modifier.height(12.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatItem("标签类型", "6种")
                    StatItem("便捷方法", "4个")
                    StatItem("工具方法", "20+")
                    StatItem("完整度", "98%")
                }
            }
        }
    }
}

/**
 * 演示导航卡片
 */
@Composable
private fun DemoNavigationCard(
    screen: DemoScreen,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = screen.icon,
                contentDescription = null,
                modifier = Modifier.size(40.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.width(16.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = screen.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = screen.description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 统计项组件
 */
@Composable
private fun StatItem(
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSecondaryContainer
        )
    }
}
