package com.taglib.demo

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
// VerticalCenterText已删除，使用普通Text组件

/**
 * VerticalCenterText组件诊断测试
 * 
 * 专门测试VerticalCenterText是否真的解决了文字截断问题
 */
@Composable
fun VerticalCenterTextDiagnosis() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Text(
            text = "🔬 VerticalCenterText诊断测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = "对比不同Text组件的显示效果，诊断文字截断问题",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 基础对比测试
        BasicComparisonTest()
        
        // 下沉字符专项测试
        DescenderSpecificTest()
        
        // 容器高度测试
        ContainerHeightTest()
        
        // 诊断结论
        DiagnosisConclusion()
    }
}

/**
 * 基础对比测试
 */
@Composable
private fun BasicComparisonTest() {
    TestSection("📋 基础对比测试") {
        val testTexts = listOf("Normal", "Typography", "gjpqy", "Programming")
        
        testTexts.forEach { text ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "测试文字: $text",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 1. 普通Text + Box居中
                    TestContainer("普通Text") {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = text,
                                fontSize = 14.sp,
                                color = Color.White,
                                maxLines = 1,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                    
                    // 2. Text + padding
                    TestContainer("Text+Padding") {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = text,
                                fontSize = 14.sp,
                                color = Color.White,
                                maxLines = 1,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.padding(vertical = 2.dp)
                            )
                        }
                    }

                    // 3. Text + 更多padding
                    TestContainer("Text+MorePadding") {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = text,
                                fontSize = 14.sp,
                                color = Color.White,
                                maxLines = 1,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.padding(vertical = 4.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 下沉字符专项测试
 */
@Composable
private fun DescenderSpecificTest() {
    TestSection("📝 下沉字符专项测试") {
        val descenderTexts = listOf(
            "g" to "单个g字符",
            "j" to "单个j字符", 
            "p" to "单个p字符",
            "q" to "单个q字符",
            "y" to "单个y字符",
            "gjpqy" to "所有下沉字符"
        )
        
        descenderTexts.forEach { (text, description) ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "$text ($description)",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 对比显示
                    TestContainer("普通Text", Color(0xFFE57373)) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = text,
                                fontSize = 16.sp,
                                color = Color.White,
                                maxLines = 1
                            )
                        }
                    }
                    
                    TestContainer("Text+Padding", Color(0xFF4CAF50)) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = text,
                                fontSize = 16.sp,
                                color = Color.White,
                                maxLines = 1,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.padding(vertical = 2.dp)
                            )
                        }
                    }
                }
                
                Text(
                    text = "检查${text}字符的下半部分是否被截断",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Gray
                )
            }
        }
    }
}

/**
 * 容器高度测试
 */
@Composable
private fun ContainerHeightTest() {
    TestSection("📐 容器高度测试") {
        val heights = listOf(20.dp, 24.dp, 28.dp, 32.dp, 36.dp)
        
        heights.forEach { height ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "容器高度: ${height.value.toInt()}dp",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    TestContainer("普通Text", Color(0xFFFF9800), height) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "Typography",
                                fontSize = 14.sp,
                                color = Color.White,
                                maxLines = 1
                            )
                        }
                    }
                    
                    TestContainer("Text+Padding", Color(0xFF9C27B0), height) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "Typography",
                                fontSize = 14.sp,
                                color = Color.White,
                                maxLines = 1,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.padding(vertical = 2.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 诊断结论
 */
@Composable
private fun DiagnosisConclusion() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "🔍 诊断要点",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF2196F3)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "请仔细观察以下几点：\n" +
                        "• 下沉字符(g,j,p,q,y)的下半部分是否完整显示\n" +
                        "• 添加padding的Text是否比普通Text有改善\n" +
                        "• 不同容器高度下的表现是否一致\n" +
                        "• 文字是否真正在容器中垂直居中",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF424242)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "🎯 简化方案的优势",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF4CAF50)
            )
            Text(
                text = "使用普通Text + padding的好处：\n" +
                        "• 简单可靠，不会引入复杂计算误差\n" +
                        "• 与Compose内部逻辑兼容\n" +
                        "• 通过padding确保下沉字符有足够空间\n" +
                        "• 易于调试和维护",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF424242)
            )
        }
    }
}

/**
 * 测试容器组件
 */
@Composable
private fun TestContainer(
    label: String,
    backgroundColor: Color = Color(0xFF2196F3),
    height: Dp = 32.dp,
    content: @Composable () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = Color.Gray
        )
        
        Box(
            modifier = Modifier
                .width(100.dp)
                .height(height)
                .background(backgroundColor, RoundedCornerShape(4.dp))
                .border(1.dp, Color.Gray.copy(alpha = 0.3f), RoundedCornerShape(4.dp))
        ) {
            content()
        }
    }
}

/**
 * 测试区块组件
 */
@Composable
private fun TestSection(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            content()
        }
    }
}
