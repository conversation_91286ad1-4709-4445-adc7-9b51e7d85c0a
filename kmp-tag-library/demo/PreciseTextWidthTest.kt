package com.taglib.demo

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taglib.TagUtils

/**
 * 精确文字宽度测试
 * 
 * 测试使用Compose TextMeasurer的真实文字宽度测量 vs 经验值计算
 */
@Composable
fun PreciseTextWidthTest() {
    val density = LocalDensity.current
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Text(
            text = "📏 精确文字宽度测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = "对比使用TextMeasurer的真实测量 vs 经验值计算",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 不同文字类型的宽度对比
        TextTypeWidthTest()
        
        // 中英文混合文字测试
        MixedTextTest()
        
        // 不同字体大小的宽度测试
        FontSizeWidthTest()
        
        // 实际标签宽度效果验证
        TagWidthEffectTest()
        
        // 方法对比说明
        MethodComparisonExplanation()
    }
}

/**
 * 不同文字类型的宽度对比
 */
@Composable
private fun TextTypeWidthTest() {
    TestSection("📊 不同文字类型宽度对比") {
        val testTexts = listOf(
            "A" to "单个英文字母",
            "中" to "单个中文字符",
            "1" to "单个数字",
            "ABC" to "英文单词",
            "中文" to "中文词组",
            "123" to "数字组合",
            "A中1" to "混合字符"
        )
        
        // 表头
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "文字",
                modifier = Modifier.width(60.dp),
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "描述",
                modifier = Modifier.width(80.dp),
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "经验值",
                modifier = Modifier.width(70.dp),
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "真实测量",
                modifier = Modifier.width(70.dp),
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "差异",
                modifier = Modifier.width(60.dp),
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold
            )
        }
        
        Divider(modifier = Modifier.padding(vertical = 8.dp))
        
        testTexts.forEach { (text, description) ->
            val fontSize = 14f
            val empiricalWidth = calculateEmpiricalWidth(text, fontSize)
            val realWidth = TagUtils.measureTextWidth(text, fontSize)
            val difference = realWidth - empiricalWidth
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = text,
                    modifier = Modifier.width(60.dp),
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = description,
                    modifier = Modifier.width(80.dp),
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = "${String.format("%.1f", empiricalWidth)}px",
                    modifier = Modifier.width(70.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFFFF9800)
                )
                Text(
                    text = "${String.format("%.1f", realWidth)}px",
                    modifier = Modifier.width(70.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF4CAF50)
                )
                Text(
                    text = "${if (difference >= 0) "+" else ""}${String.format("%.1f", difference)}",
                    modifier = Modifier.width(60.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = if (difference > 0) Color(0xFF4CAF50) else Color(0xFFD32F2F)
                )
            }
        }
    }
}

/**
 * 中英文混合文字测试
 */
@Composable
private fun MixedTextTest() {
    TestSection("🌐 中英文混合文字测试") {
        val mixedTexts = listOf(
            "Hello世界",
            "标签Tag",
            "价格¥99",
            "优惠50%",
            "新品New",
            "限时Flash"
        )
        
        mixedTexts.forEach { text ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "文字: \"$text\"",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 经验值计算的宽度
                    val empiricalWidth = calculateEmpiricalWidth(text, 14f)
                    TestContainer(
                        label = "经验值",
                        width = empiricalWidth.dp,
                        backgroundColor = Color(0xFFFF9800)
                    ) {
                        Text(
                            text = text,
                            fontSize = 14.sp,
                            color = Color.White,
                            maxLines = 1
                        )
                    }
                    
                    // 真实测量的宽度
                    val realWidth = TagUtils.measureTextWidth(text, 14f)
                    TestContainer(
                        label = "真实测量",
                        width = realWidth.dp,
                        backgroundColor = Color(0xFF4CAF50)
                    ) {
                        Text(
                            text = text,
                            fontSize = 14.sp,
                            color = Color.White,
                            maxLines = 1
                        )
                    }
                }
                
                Text(
                    text = "经验值: ${String.format("%.1f", empiricalWidth)}px, " +
                            "真实测量: ${String.format("%.1f", realWidth)}px, " +
                            "差异: ${String.format("%.1f", realWidth - empiricalWidth)}px",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Gray
                )
            }
        }
    }
}

/**
 * 不同字体大小的宽度测试
 */
@Composable
private fun FontSizeWidthTest() {
    TestSection("📏 字体大小宽度测试") {
        val testText = "标签Tag"
        val fontSizes = listOf(10f, 12f, 14f, 16f, 18f, 20f)
        
        fontSizes.forEach { fontSize ->
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "${fontSize.toInt()}sp 字体测试",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 经验值计算
                    val empiricalWidth = calculateEmpiricalWidth(testText, fontSize)
                    TestContainer(
                        label = "经验值",
                        width = empiricalWidth.dp,
                        backgroundColor = Color(0xFFFF9800)
                    ) {
                        Text(
                            text = testText,
                            fontSize = fontSize.sp,
                            color = Color.White,
                            maxLines = 1
                        )
                    }
                    
                    // 真实测量
                    val realWidth = TagUtils.measureTextWidth(testText, fontSize)
                    TestContainer(
                        label = "真实测量",
                        width = realWidth.dp,
                        backgroundColor = Color(0xFF4CAF50)
                    ) {
                        Text(
                            text = testText,
                            fontSize = fontSize.sp,
                            color = Color.White,
                            maxLines = 1
                        )
                    }
                }
            }
        }
    }
}

/**
 * 实际标签宽度效果验证
 */
@Composable
private fun TagWidthEffectTest() {
    TestSection("🏷️ 实际标签宽度效果") {
        Text(
            text = "验证在实际标签中使用精确宽度计算的效果",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 这里可以添加实际的标签组件测试
        // 由于需要TagBean等复杂结构，暂时用简化版本演示
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
        ) {
            Column(modifier = Modifier.padding(12.dp)) {
                Text(
                    text = "📋 测试结果预期",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF2196F3)
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "• 标签宽度更加精确，不会过宽或过窄\n" +
                            "• 中英文混合文字的标签宽度更合理\n" +
                            "• 不同字体大小的标签比例更协调\n" +
                            "• 整体布局更加紧凑和美观",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF424242)
                )
            }
        }
    }
}

/**
 * 方法对比说明
 */
@Composable
private fun MethodComparisonExplanation() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "🔧 实现方法对比",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF2196F3)
            )
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "经验值计算 (修改前):",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium,
                color = Color(0xFFFF9800)
            )
            Text(
                text = "for (char in text) {\n" +
                        "    width += when {\n" +
                        "        char.code > 127 -> textSize * 0.9f // 中文\n" +
                        "        char.isDigit() -> textSize * 0.5f // 数字\n" +
                        "        char.isLetter() -> textSize * 0.6f // 英文\n" +
                        "        else -> textSize * 0.4f // 其他\n" +
                        "    }\n" +
                        "}",
                style = MaterialTheme.typography.bodySmall,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                modifier = Modifier.padding(start = 8.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "真实测量 (修改后):",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF4CAF50)
            )
            Text(
                text = "@Composable\n" +
                        "fun measureTextWidth(text: String, textSize: Float): Float {\n" +
                        "    val textMeasurer = rememberTextMeasurer()\n" +
                        "    val result = textMeasurer.measure(\n" +
                        "        text = text,\n" +
                        "        style = TextStyle(fontSize = textSize.sp)\n" +
                        "    )\n" +
                        "    return result.size.width.toFloat()\n" +
                        "}",
                style = MaterialTheme.typography.bodySmall,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                modifier = Modifier.padding(start = 8.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "🎯 关键优势:",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = "• 使用Compose的TextMeasurer获取真实宽度\n" +
                        "• 考虑实际字体的字符间距和字形\n" +
                        "• 支持不同字体和字重的精确测量\n" +
                        "• 与Compose渲染引擎完全一致",
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(start = 8.dp)
            )
        }
    }
}

/**
 * 经验值宽度计算（用于对比）
 */
private fun calculateEmpiricalWidth(text: String, textSize: Float): Float {
    var width = 0f
    for (char in text) {
        width += when {
            char.code > 127 -> textSize * 0.9f // 中文字符
            char.isDigit() -> textSize * 0.5f // 数字
            char.isLetter() -> textSize * 0.6f // 英文字母
            else -> textSize * 0.4f // 其他字符
        }
    }
    return width
}

/**
 * 测试容器组件
 */
@Composable
private fun TestContainer(
    label: String,
    width: androidx.compose.ui.unit.Dp,
    backgroundColor: Color,
    content: @Composable () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = "$label\n${width.value.toInt()}dp",
            style = MaterialTheme.typography.labelSmall,
            color = Color.Gray,
            textAlign = TextAlign.Center
        )
        
        Box(
            modifier = Modifier
                .width(width)
                .height(32.dp)
                .background(backgroundColor, RoundedCornerShape(4.dp))
                .border(1.dp, Color.Gray.copy(alpha = 0.3f), RoundedCornerShape(4.dp)),
            contentAlignment = Alignment.Center
        ) {
            content()
        }
    }
}

/**
 * 测试区块组件
 */
@Composable
private fun TestSection(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            content()
        }
    }
}
