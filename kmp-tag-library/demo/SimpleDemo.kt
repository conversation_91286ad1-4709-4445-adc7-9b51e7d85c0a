package com.taglib.demo

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.taglib.*

/**
 * 简化的Demo，避免图片加载器问题
 * 专门测试文字截断修复效果
 */
@Composable
fun SimpleDemo() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Text(
            text = "🔧 标签文字截断修复测试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text(
            text = "测试修复后的标签组件是否能正确显示文字，不再出现下半部分被截断的问题",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )
        
        // 基础标签测试
        BasicTagsTest()
        
        // 不同字体大小测试
        FontSizeTest()
        
        // 固定高度测试
        FixedHeightTest()
        
        // 修复效果展示
        FixResultDemo()
    }
}

/**
 * 基础标签测试
 */
@Composable
private fun BasicTagsTest() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "📋 基础标签测试",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            val basicTags = listOf(
                TagBean(
                    type = TagType.FILL,
                    text = "填充标签",
                    backgroundColor = Color(0xFF4CAF50),
                    textColor = Color.White
                ),
                TagBean(
                    type = TagType.STROKE,
                    text = "描边标签",
                    borderColor = Color(0xFF2196F3),
                    textColor = Color(0xFF2196F3)
                ),
                TagBean(
                    type = TagType.FILL_AND_STROKE,
                    text = "填充+描边",
                    backgroundColor = Color(0xFFFFF3E0),
                    borderColor = Color(0xFFFF9800),
                    textColor = Color(0xFFE65100)
                ),
                TagBean(
                    type = TagType.DISCOUNT,
                    text = "折扣标签",
                    backgroundColor = Color(0xFFFFEBEE),
                    borderColor = Color(0xFFD32F2F),
                    textColor = Color(0xFFD32F2F)
                )
            )
            
            basicTags.forEach { tag ->
                TagGroup(
                    tags = listOf(tag),
                    text = "检查${tag.type.name}文字是否完整显示",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    onTagClick = { clickedTag ->
                        println("点击了标签: ${clickedTag.text}")
                    }
                )
            }
        }
    }
}

/**
 * 字体大小测试
 */
@Composable
private fun FontSizeTest() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "📏 字体大小测试",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            val fontSizes = listOf(10, 12, 14, 16, 18, 20)
            
            fontSizes.forEach { size ->
                val tag = TagBean(
                    type = TagType.FILL,
                    text = "${size}sp文字",
                    backgroundColor = Color(0xFF9C27B0),
                    textColor = Color.White,
                    appearance = TagAppearance.Default.copy(
                        textSize = size.dp.value.sp
                    )
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "${size}sp:",
                        modifier = Modifier.width(60.dp),
                        style = MaterialTheme.typography.bodySmall
                    )
                    
                    TagGroup(
                        tags = listOf(tag),
                        text = "字体大小测试",
                        maxLines = 1
                    )
                }
            }
        }
    }
}

/**
 * 固定高度测试
 */
@Composable
private fun FixedHeightTest() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "📐 固定高度测试",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            val heights = listOf(20, 24, 28, 32, 36)
            
            heights.forEach { height ->
                val tag = TagBean(
                    type = TagType.FILL,
                    text = "${height}dp高度",
                    backgroundColor = Color(0xFFFF5722),
                    textColor = Color.White,
                    appearance = TagAppearance.Default.copy(tagHeight = height.dp),
                    useFixedHeight = true
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "${height}dp:",
                        modifier = Modifier.width(60.dp),
                        style = MaterialTheme.typography.bodySmall
                    )
                    
                    TagGroup(
                        tags = listOf(tag),
                        text = "固定高度测试",
                        maxLines = 1
                    )
                }
            }
        }
    }
}

/**
 * 修复效果展示
 */
@Composable
private fun FixResultDemo() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "✅ 修复效果展示",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF4CAF50)
            )
            
            Text(
                text = "以下标签使用了VerticalCenterText修复文字截断问题：",
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF424242)
            )
            
            val testTag = TagBean(
                type = TagType.FILL,
                text = "修复后效果",
                backgroundColor = Color(0xFF4CAF50),
                textColor = Color.White,
                appearance = TagAppearance.Default.copy(
                    textSize = 16.sp,
                    tagHeight = 32.dp
                ),
                useFixedHeight = true
            )
            
            TagGroup(
                tags = listOf(testTag),
                text = "文字应该完整显示，不会被截断",
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "🎯 修复内容：\n" +
                        "• 所有标签组件已使用VerticalCenterText\n" +
                        "• 实现了精确的垂直居中对齐\n" +
                        "• 修复了文字下半部分被截断的问题\n" +
                        "• 支持固定高度和自适应高度",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF424242)
            )
        }
    }
}
