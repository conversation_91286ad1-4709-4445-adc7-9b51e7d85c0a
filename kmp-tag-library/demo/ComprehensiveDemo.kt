package com.taglib

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp

/**
 * KMP Tag Library 综合演示
 * 
 * 这是一个完整的演示应用，展示了KMP Tag Library的所有功能特性：
 * 1. 基础标签类型演示
 * 2. AppTag配置使用演示
 * 3. 电商场景实际应用演示
 * 4. 自定义样式演示
 * 5. 交互功能演示
 * 
 * 本演示合并了TagExample和AppTagExample的所有功能，
 * 提供了从基础使用到高级应用的完整展示。
 * 
 * <AUTHOR> Tag Library Team
 * @since 1.0.0
 */
@Composable
fun ComprehensiveTagDemo() {
    /**
     * 初始化AppTag配置
     * 
     * 在实际项目中，这通常在Application的onCreate中调用。
     * 这里为了演示目的，在Composable中初始化。
     */
    LaunchedEffect(Unit) {
        AppTag.init() // 使用默认配置初始化
    }
    
    /**
     * 主界面布局
     * 
     * 使用LazyColumn来展示不同类型的标签演示，
     * 每个演示都有详细的说明和代码示例。
     */
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        item {
            DemoHeader()
        }
        
        // 基础标签类型演示
        item {
            BasicTagTypesDemo()
        }
        
        // AppTag配置演示
        item {
            AppTagConfigDemo()
        }
        
        // 电商场景演示
        item {
            ECommerceScenarioDemo()
        }
        
        // 自定义样式演示
        item {
            CustomStyleDemo()
        }
        
        // 交互功能演示
        item {
            InteractiveDemo()
        }
        
        // 高级功能演示
        item {
            AdvancedFeaturesDemo()
        }
    }
}

/**
 * 演示标题组件
 * 
 * 显示库的名称、版本信息和主要特性介绍
 */
@Composable
private fun DemoHeader() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "🏷️ KMP Tag Library",
                style = MaterialTheme.typography.headlineLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "跨平台标签库 - Kotlin Multiplatform + Compose",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "✨ 支持6种标签类型 | 🎨 丰富样式配置 | 📱 Android & iOS | 🖱️ 交互支持",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
        }
    }
}

/**
 * 基础标签类型演示
 * 
 * 展示所有6种标签类型的基本用法：
 * FILL, STROKE, IMAGE, DISCOUNT, POINTS, FILL_AND_STROKE
 */
@Composable
private fun BasicTagTypesDemo() {
    DemoSection(
        title = "📋 基础标签类型",
        description = "展示所有6种标签类型的基本用法和外观效果"
    ) {
        // 填充背景标签
        DemoItem(
            title = "FILL - 填充背景标签",
            description = "纯色或渐变背景，最常用的标签类型"
        ) {
            TagGroup(
                tags = listOf(
                    TagBean(
                        type = TagType.FILL,
                        text = "新品",
                        textColor = Color.White,
                        backgroundColor = Color.Red,
                        appearance = TagAppearance.Default
                    ),
                    TagBean(
                        type = TagType.FILL,
                        text = "热销",
                        textColor = Color.White,
                        backgroundColor = Color(0xFFFF6B6B),
                        backgroundEndColor = Color(0xFFFFE66D), // 渐变效果
                        appearance = TagAppearance.Round
                    )
                ),
                text = "商品名称"
            )
        }
        
        // 镂空边框标签
        DemoItem(
            title = "STROKE - 镂空边框标签",
            description = "只有边框，背景透明，轻量级设计"
        ) {
            TagGroup(
                tags = listOf(
                    TagBean(
                        type = TagType.STROKE,
                        text = "限量",
                        textColor = Color.Red,
                        borderColor = Color.Red,
                        appearance = TagAppearance.Default
                    ),
                    TagBean(
                        type = TagType.STROKE,
                        text = "包邮",
                        textColor = Color.Green,
                        borderColor = Color.Green,
                        appearance = TagAppearance.Capsule
                    )
                ),
                text = "商品描述"
            )
        }
        
        // 折省标签
        DemoItem(
            title = "DISCOUNT - 折省标签",
            description = "特殊的分段显示标签，第一个字符和剩余字符分别显示"
        ) {
            TagGroup(
                tags = listOf(
                    TagBean(
                        type = TagType.DISCOUNT,
                        text = "折省",
                        textColor = Color.White,
                        backgroundColor = Color(0xFFE91E63),
                        backgroundEndColor = Color(0xFFFF5722),
                        appearance = TagAppearance.Default
                    )
                ),
                text = "折省标签商品"
            )
        }
        
        // 积分标签
        DemoItem(
            title = "POINTS - 积分标签",
            description = "带图标的积分标签，左侧显示图标，右侧显示积分文字"
        ) {
            TagGroup(
                tags = listOf(
                    TagBean(
                        type = TagType.POINTS,
                        text = "100积分",
                        textColor = Color.White,
                        backgroundColor = Color(0xFF9C27B0),
                        backgroundEndColor = Color(0xFF673AB7),
                        appearance = TagAppearance.Round
                    )
                ),
                text = "积分商品"
            )
        }
        
        // 填充+描边标签
        DemoItem(
            title = "FILL_AND_STROKE - 填充+描边标签",
            description = "既有背景填充又有边框描边的标签"
        ) {
            TagGroup(
                tags = listOf(
                    TagBean(
                        type = TagType.FILL_AND_STROKE,
                        text = "推荐",
                        textColor = Color.Blue,
                        backgroundColor = Color(0xFFE3F2FD),
                        borderColor = Color.Blue,
                        appearance = TagAppearance.Round
                    )
                ),
                text = "推荐商品"
            )
        }
    }
}

/**
 * AppTag配置演示
 *
 * 展示如何使用AppTag配置来简化标签的创建和使用，
 * 这是推荐的使用方式，特别适合从原生Android项目迁移。
 */
@Composable
private fun AppTagConfigDemo() {
    DemoSection(
        title = "⚙️ AppTag配置演示",
        description = "使用AppTag配置简化标签创建，推荐的使用方式"
    ) {
        // 使用GoodsTag数据结构
        DemoItem(
            title = "GoodsTag数据结构",
            description = "与原生Android项目完全兼容的数据结构"
        ) {
            val goodsTags = listOf(
                GoodsTag(form = 1, name = "新品", color = "#FFFFFF", bgcolor = "#FF0000"),
                GoodsTag(form = 3, name = "包邮", color = "#4CAF50", bordercolor = "#4CAF50"),
                GoodsTag(form = 4, name = "折省", color = "#FFFFFF", bgcolor = "#E91E63", bgGraduallyColor = "#FF5722")
            )

            AppTag.ShowTags(
                tags = goodsTags,
                content = "使用AppTag配置的商品",
                onTagClick = { tag ->
                    println("点击了标签: ${tag.text}")
                }
            )
        }

        // 使用工厂方法
        DemoItem(
            title = "工厂方法快速创建",
            description = "使用GoodsTagFactory快速创建常用标签"
        ) {
            val factoryTags = listOf(
                GoodsTagFactory.newTag(),
                GoodsTagFactory.hotTag(),
                GoodsTagFactory.freeShippingTag(),
                GoodsTagFactory.discountTag("限时8.5折"),
                GoodsTagFactory.pointsTag("200积分")
            )

            AppTag.ShowTags(
                tags = factoryTags,
                content = "工厂方法创建的标签",
                showTagsAtStart = true
            )
        }
    }
}

/**
 * 电商场景演示
 *
 * 展示在实际电商项目中的应用场景，
 * 包括商品卡片、价格显示、促销信息等。
 */
@Composable
private fun ECommerceScenarioDemo() {
    DemoSection(
        title = "🛒 电商场景演示",
        description = "实际电商项目中的应用场景展示"
    ) {
        // 商品卡片示例
        val products = listOf(
            ProductData(
                name = "iPhone 15 Pro 256GB",
                price = "¥7999",
                originalPrice = "¥8999",
                description = "A17 Pro芯片，钛金属设计，专业级摄像头系统",
                tags = listOf(
                    GoodsTagFactory.newTag(),
                    GoodsTagFactory.hotTag(),
                    GoodsTagFactory.freeShippingTag()
                )
            ),
            ProductData(
                name = "MacBook Air M2 13英寸",
                price = "¥8999",
                description = "M2芯片，13.6英寸液晶显示屏，轻薄便携",
                tags = listOf(
                    GoodsTagFactory.hotTag(),
                    GoodsTagFactory.discountTag("限时8.5折"),
                    GoodsTagFactory.pointsTag("500积分")
                )
            )
        )

        products.forEach { product ->
            ProductCard(product)
            Spacer(modifier = Modifier.height(8.dp))
        }
    }
}

/**
 * 自定义样式演示
 *
 * 展示如何创建和使用自定义样式，
 * 包括预定义样式和完全自定义的样式。
 */
@Composable
private fun CustomStyleDemo() {
    DemoSection(
        title = "🎨 自定义样式演示",
        description = "展示预定义样式和自定义样式的使用"
    ) {
        // 预定义样式
        DemoItem(
            title = "预定义样式",
            description = "Default, Round, Capsule, Square四种预定义样式"
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                TagGroup(
                    tags = listOf(
                        TagBean(type = TagType.FILL, text = "Default", textColor = Color.White, backgroundColor = Color.Blue, appearance = TagAppearance.Default)
                    ),
                    text = "默认样式"
                )
                TagGroup(
                    tags = listOf(
                        TagBean(type = TagType.FILL, text = "Round", textColor = Color.White, backgroundColor = Color.Green, appearance = TagAppearance.Round)
                    ),
                    text = "圆角样式"
                )
                TagGroup(
                    tags = listOf(
                        TagBean(type = TagType.FILL, text = "Capsule", textColor = Color.White, backgroundColor = Color(0xFFFF9800), appearance = TagAppearance.Capsule)
                    ),
                    text = "胶囊样式"
                )
                TagGroup(
                    tags = listOf(
                        TagBean(type = TagType.FILL, text = "Square", textColor = Color.White, backgroundColor = Color.Red, appearance = TagAppearance.Square)
                    ),
                    text = "方形样式"
                )
            }
        }

        // 自定义样式
        DemoItem(
            title = "自定义样式",
            description = "创建完全自定义的标签样式"
        ) {
            val customAppearance = TagAppearance(
                textSize = 14.sp,
                cornerRadius = 8.dp,
                horizontalPadding = 12.dp,
                verticalPadding = 6.dp,
                borderWidth = 2.dp,
                fontWeight = FontWeight.Bold
            )

            TagGroup(
                tags = listOf(
                    TagBean(
                        type = TagType.FILL_AND_STROKE,
                        text = "自定义样式",
                        textColor = Color(0xFF6200EA),
                        backgroundColor = Color(0xFFE1BEE7),
                        borderColor = Color(0xFF6200EA),
                        appearance = customAppearance
                    )
                ),
                text = "使用自定义样式的标签"
            )
        }
    }
}

/**
 * 交互功能演示
 *
 * 展示标签的点击交互功能，
 * 包括点击回调、提示信息等。
 */
@Composable
private fun InteractiveDemo() {
    var clickMessage by remember { mutableStateOf("点击任意标签查看效果") }

    DemoSection(
        title = "🖱️ 交互功能演示",
        description = "展示标签的点击交互和回调功能"
    ) {
        DemoItem(
            title = "可点击标签",
            description = "带箭头的可点击标签，支持点击回调"
        ) {
            Column {
                TagGroup(
                    tags = listOf(
                        TagBean(
                            type = TagType.FILL,
                            text = "查看详情",
                            textColor = Color.White,
                            backgroundColor = Color.Blue,
                            isClickable = true,
                            clickToast = "点击查看商品详情",
                            appearance = TagAppearance.Default
                        ),
                        TagBean(
                            type = TagType.STROKE,
                            text = "更多信息",
                            textColor = Color.Green,
                            borderColor = Color.Green,
                            isClickable = true,
                            clickToast = "点击获取更多信息",
                            appearance = TagAppearance.Round
                        )
                    ),
                    text = "可交互商品",
                    onTagClick = { tag ->
                        clickMessage = "点击了「${tag.text}」标签\n提示信息: ${tag.clickToast ?: "无"}"
                    }
                )

                Spacer(modifier = Modifier.height(8.dp))

                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Text(
                        text = clickMessage,
                        modifier = Modifier.padding(12.dp),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

/**
 * 高级功能演示
 *
 * 展示库的高级功能，包括动态标签生成、
 * 复杂布局、性能优化等。
 */
@Composable
private fun AdvancedFeaturesDemo() {
    DemoSection(
        title = "🚀 高级功能演示",
        description = "展示动态标签生成、复杂布局等高级功能"
    ) {
        // 动态标签生成
        DemoItem(
            title = "动态标签生成",
            description = "根据数据动态生成标签，模拟真实业务场景"
        ) {
            val productData = ProductData(
                name = "动态生成标签的商品",
                price = "¥999",
                originalPrice = "¥1299",
                description = "根据商品属性动态生成相应的标签",
                tags = emptyList(), // 将动态生成
                isNew = true,
                isHot = true,
                freeShipping = true,
                discountPercent = 15,
                points = 100
            )

            DynamicTagsExample(productData)
        }

        // 标签位置控制
        DemoItem(
            title = "标签位置控制",
            description = "控制标签显示在文字前面或后面"
        ) {
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                TagGroup(
                    tags = listOf(TagFactory.newTag("前置")),
                    text = "标签在前面的商品",
                    showTagsAtStart = true
                )
                TagGroup(
                    tags = listOf(TagFactory.hotTag("后置")),
                    text = "标签在后面的商品",
                    showTagsAtStart = false
                )
            }
        }

        // 复杂标签组合
        DemoItem(
            title = "复杂标签组合",
            description = "多种类型标签的复杂组合展示"
        ) {
            val complexTags = listOf(
                TagBean(type = TagType.FILL, text = "新", textColor = Color.White, backgroundColor = Color.Red, appearance = TagAppearance.Square.withCornerRadius(2.dp)),
                TagBean(type = TagType.STROKE, text = "热", textColor = Color(0xFFFF9800), borderColor = Color(0xFFFF9800), appearance = TagAppearance.Default),
                TagBean(type = TagType.DISCOUNT, text = "折省", textColor = Color.White, backgroundColor = Color(0xFFE91E63), backgroundEndColor = Color(0xFFFF5722), appearance = TagAppearance.Default),
                TagBean(type = TagType.POINTS, text = "50积分", textColor = Color.White, backgroundColor = Color(0xFF9C27B0), appearance = TagAppearance.Round),
                TagBean(type = TagType.FILL_AND_STROKE, text = "荐", textColor = Color.Blue, backgroundColor = Color.White, borderColor = Color.Blue, appearance = TagAppearance.Round)
            )

            TagGroup(
                tags = complexTags,
                text = "复杂标签组合商品",
                onTagClick = { tag ->
                    println("点击了复杂标签: ${tag.text}")
                }
            )
        }
    }
}

// ==================== 辅助组件和数据类 ====================

/**
 * 演示区域组件
 *
 * 为每个演示提供统一的布局和样式
 */
@Composable
private fun DemoSection(
    title: String,
    description: String,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 区域标题
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )

            // 区域描述
            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Divider()

            // 区域内容
            content()
        }
    }
}

/**
 * 演示项目组件
 *
 * 为每个具体的演示项目提供布局
 */
@Composable
private fun DemoItem(
    title: String,
    description: String,
    content: @Composable () -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 项目标题
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.onSurface
        )

        // 项目描述
        Text(
            text = description,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 演示内容
        Card(
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
            )
        ) {
            Box(
                modifier = Modifier.padding(12.dp)
            ) {
                content()
            }
        }
    }
}

/**
 * 商品数据类
 *
 * 用于演示的商品数据结构，包含商品的基本信息和标签属性
 */
data class ProductData(
    val name: String,                           // 商品名称
    val price: String,                          // 当前价格
    val originalPrice: String? = null,          // 原价（用于显示划线价格）
    val description: String = "",               // 商品描述
    val tags: List<GoodsTag> = emptyList(),     // 预定义标签列表

    // 用于动态生成标签的属性
    val isNew: Boolean = false,                 // 是否为新品
    val isHot: Boolean = false,                 // 是否为热销商品
    val freeShipping: Boolean = false,          // 是否包邮
    val discountPercent: Int = 0,               // 折扣百分比
    val points: Int = 0                         // 积分数量
)

/**
 * 商品卡片组件
 *
 * 展示商品信息的卡片，包含标签、名称、价格等信息
 */
@Composable
private fun ProductCard(product: ProductData) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 商品标签和名称
            AppTag.ShowTags(
                tags = product.tags,
                content = product.name,
                showTagsAtStart = true,
                onTagClick = { tag ->
                    println("点击了商品「${product.name}」的标签: ${tag.text}")
                }
            )

            // 价格信息
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = product.price,
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.error,
                    fontWeight = FontWeight.Bold
                )

                if (product.originalPrice != null) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = product.originalPrice,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textDecoration = TextDecoration.LineThrough
                    )
                }
            }

            // 商品描述
            if (product.description.isNotBlank()) {
                Text(
                    text = product.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 2
                )
            }
        }
    }
}

/**
 * 动态标签生成示例
 *
 * 根据商品属性动态生成相应的标签，展示实际业务场景中的用法
 */
@Composable
private fun DynamicTagsExample(productData: ProductData) {
    /**
     * 根据商品数据动态生成标签列表
     *
     * 这个函数展示了如何在实际项目中根据业务数据
     * 动态生成标签，而不是硬编码标签列表
     */
    val dynamicTags = remember(productData) {
        buildList {
            // 新品标签
            if (productData.isNew) {
                add(GoodsTagFactory.newTag())
            }

            // 热销标签
            if (productData.isHot) {
                add(GoodsTagFactory.hotTag())
            }

            // 包邮标签
            if (productData.freeShipping) {
                add(GoodsTagFactory.freeShippingTag())
            }

            // 折扣标签
            if (productData.discountPercent > 0) {
                add(GoodsTagFactory.discountTag("${productData.discountPercent}折"))
            }

            // 积分标签
            if (productData.points > 0) {
                add(GoodsTagFactory.pointsTag("${productData.points}积分"))
            }
        }
    }

    AppTag.ShowTags(
        tags = dynamicTags,
        content = productData.name,
        onTagClick = { tag ->
            handleDynamicTagClick(tag, productData)
        }
    )
}

/**
 * 处理动态标签点击事件
 *
 * 根据不同的标签类型执行不同的业务逻辑
 */
private fun handleDynamicTagClick(tag: TagBean, productData: ProductData) {
    when (tag.text) {
        "新品" -> {
            println("查看新品详情: ${productData.name}")
            // 实际项目中可以跳转到新品页面
        }
        "热销" -> {
            println("查看热销排行: ${productData.name}")
            // 实际项目中可以显示热销排行
        }
        "包邮" -> {
            println("查看配送信息: ${productData.name}")
            // 实际项目中可以显示配送详情
        }
        else -> {
            if (tag.text.contains("折")) {
                println("查看折扣详情: ${productData.name}")
                // 实际项目中可以显示促销详情
            } else if (tag.text.contains("积分")) {
                println("查看积分规则: ${productData.name}")
                // 实际项目中可以显示积分说明
            }
        }
    }
}
