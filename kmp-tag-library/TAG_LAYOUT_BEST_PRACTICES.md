# 🌊 标签布局最佳实践

## 📋 概述

KMP标签库提供了多种布局方式来处理标签显示，特别是当标签数量较多或标签文字较长时的布局策略。

## 🎯 布局方式对比

### 1. 水平滚动布局（默认）

**特点：**
- 标签在一行内水平排列
- 超出屏幕宽度时可以滚动查看
- 保持界面整洁，不占用额外垂直空间

**适用场景：**
- 标签数量不确定
- 希望保持界面紧凑
- 移动端列表项展示

**实现方式：**
```kotlin
TagGroup(
    tags = tags,
    text = "商品名称",
    showTagsAtStart = true
)
```

**优点：**
- ✅ 界面整洁，不会撑开容器高度
- ✅ 适合标签数量动态变化的场景
- ✅ 用户可以滑动查看所有标签

**缺点：**
- ❌ 用户可能不知道有更多标签
- ❌ 需要滑动操作才能看到所有标签

### 2. 换行布局

**特点：**
- 标签自动换行显示
- 所有标签都可见
- 占用更多垂直空间

**适用场景：**
- 标签数量较少（建议不超过8个）
- 希望所有标签都可见
- 详情页面展示

**实现方式：**
```kotlin
// 自定义换行布局
FlowRowTagLayout(
    tags = tags,
    onTagClick = { tag -> /* 处理点击 */ }
)
```

**优点：**
- ✅ 所有标签都可见
- ✅ 不需要滑动操作
- ✅ 更好的可发现性

**缺点：**
- ❌ 占用更多垂直空间
- ❌ 标签过多时界面会很长
- ❌ 在列表中使用时会影响滚动性能

### 3. 分组展示

**特点：**
- 按类型或重要性分组
- 每组单独显示
- 结构清晰，层次分明

**适用场景：**
- 标签有明确分类
- 需要突出重要标签
- 复杂的标签体系

**实现方式：**
```kotlin
// 重要标签组
TagGroup(
    tags = importantTags,
    text = "重要标签",
    showTagsAtStart = true
)

Spacer(modifier = Modifier.height(8.dp))

// 普通标签组
TagGroup(
    tags = normalTags,
    text = "其他标签",
    showTagsAtStart = true
)
```

**优点：**
- ✅ 结构清晰，易于理解
- ✅ 可以突出重要信息
- ✅ 便于管理和维护

**缺点：**
- ❌ 需要预先分类
- ❌ 占用更多空间
- ❌ 实现相对复杂

## 🎨 实际应用场景

### 1. 商品列表项

**推荐：水平滚动布局**

```kotlin
@Composable
fun ProductListItem(product: Product) {
    Card {
        Column(modifier = Modifier.padding(16.dp)) {
            // 使用水平滚动，保持列表项高度一致
            TagGroup(
                tags = product.tags,
                text = product.name,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            
            Text(text = "¥${product.price}")
        }
    }
}
```

### 2. 商品详情页

**推荐：分组展示或换行布局**

```kotlin
@Composable
fun ProductDetailPage(product: Product) {
    Column {
        // 重要标签（促销、新品等）
        if (product.promotionTags.isNotEmpty()) {
            TagGroup(
                tags = product.promotionTags,
                text = "促销信息",
                showTagsAtStart = true
            )
        }
        
        // 商品特性标签（换行显示）
        if (product.featureTags.isNotEmpty()) {
            Text("商品特性")
            FlowRowTagLayout(
                tags = product.featureTags,
                onTagClick = { tag -> showTagDetail(tag) }
            )
        }
        
        // 服务标签
        if (product.serviceTags.isNotEmpty()) {
            TagGroup(
                tags = product.serviceTags,
                text = "服务保障",
                showTagsAtStart = true
            )
        }
    }
}
```

### 3. 搜索结果页

**推荐：水平滚动 + 文字截断**

```kotlin
@Composable
fun SearchResultItem(product: Product) {
    TagGroup(
        tags = product.tags.take(3), // 只显示前3个标签
        text = product.name,
        maxLines = 2,
        overflow = TextOverflow.Ellipsis,
        showTagsAtStart = true
    )
}
```

## 🔧 技术实现

### 1. 自定义换行布局

```kotlin
@Composable
fun FlowRowTagLayout(
    tags: List<TagBean>,
    maxWidth: Dp = 300.dp,
    onTagClick: ((TagBean) -> Unit)? = null
) {
    // 简化的换行实现
    Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
        tags.chunked(3).forEach { rowTags ->
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                rowTags.forEach { tag ->
                    SingleTagButton(tag = tag, onClick = onTagClick)
                }
            }
        }
    }
}
```

### 2. 响应式布局

```kotlin
@Composable
fun ResponsiveTagLayout(
    tags: List<TagBean>,
    text: String
) {
    BoxWithConstraints {
        if (maxWidth < 400.dp) {
            // 小屏幕：水平滚动
            TagGroup(tags = tags, text = text)
        } else {
            // 大屏幕：换行布局
            Column {
                Text(text)
                FlowRowTagLayout(tags = tags)
            }
        }
    }
}
```

### 3. 动态标签数量控制

```kotlin
@Composable
fun SmartTagLayout(
    tags: List<TagBean>,
    text: String,
    maxVisibleTags: Int = 5
) {
    val visibleTags = tags.take(maxVisibleTags)
    val hasMoreTags = tags.size > maxVisibleTags
    
    TagGroup(
        tags = if (hasMoreTags) {
            visibleTags + TagBean(
                type = TagType.FILL,
                text = "+${tags.size - maxVisibleTags}",
                backgroundColor = Color.Gray,
                textColor = Color.White,
                isClickable = true
            )
        } else {
            visibleTags
        },
        text = text,
        onTagClick = { tag ->
            if (tag.text.startsWith("+")) {
                // 显示所有标签
                showAllTags(tags)
            } else {
                // 处理普通标签点击
                handleTagClick(tag)
            }
        }
    )
}
```

## 📱 平台适配

### Android
- 使用LazyRow实现水平滚动
- 支持触摸滚动和惯性滚动
- 可以使用FlowRow库实现换行

### iOS
- 使用LazyHStack实现水平滚动
- 支持原生滚动手势
- 可以使用自定义布局实现换行

### Desktop
- 支持鼠标滚轮滚动
- 可以显示滚动条
- 更大的屏幕空间适合换行布局

## 💡 性能优化建议

### 1. 标签数量控制
```kotlin
// 限制标签数量，避免性能问题
val displayTags = tags.take(10)
```

### 2. 懒加载
```kotlin
// 在列表中使用时，避免创建过多组件
LazyColumn {
    items(products, key = { it.id }) { product ->
        val tags = remember(product.id) {
            product.generateTags()
        }
        ProductItem(product, tags)
    }
}
```

### 3. 缓存优化
```kotlin
// 缓存标签计算结果
val tagCache = remember { mutableMapOf<String, List<TagBean>>() }

val tags = remember(product.id) {
    tagCache.getOrPut(product.id) {
        product.generateTags()
    }
}
```

## 🎯 选择建议

### 根据场景选择：

1. **列表项** → 水平滚动
2. **详情页** → 分组展示
3. **卡片** → 换行布局（标签少时）
4. **搜索结果** → 水平滚动 + 数量限制
5. **筛选器** → 换行布局

### 根据标签数量选择：

- **1-3个标签** → 任意布局
- **4-6个标签** → 水平滚动或换行
- **7+个标签** → 水平滚动或分组

### 根据屏幕尺寸选择：

- **手机** → 优先水平滚动
- **平板** → 可以使用换行
- **桌面** → 换行布局更友好

## 🔗 相关示例

- [Demo代码](demo/TagLibraryDemo.kt) - 查看完整的布局演示
- [快速参考](QUICK_REFERENCE.md) - 布局API速查
- [使用指南](KMP_TAG_LIBRARY_USAGE_GUIDE.md) - 详细配置说明
