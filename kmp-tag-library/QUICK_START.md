# 🚀 KMP标签库快速开始指南

## 📦 安装

### 1. 添加依赖

在你的 `build.gradle.kts` 中添加：

```kotlin
dependencies {
    implementation("com.taglib:kmp-tag-library:1.0.0")
}
```

### 2. 初始化

在应用启动时初始化：

```kotlin
// 在Application或主Activity中
AppTag.init() // 使用默认配置

// 或者传入自定义图片加载器
AppTag.init(CustomImageLoader())
```

## 🏷️ 基础使用

### 创建第一个标签

```kotlin
@Composable
fun MyFirstTag() {
    val tag = TagBean(
        type = TagType.FILL,
        text = "新品",
        textColor = Color.White,
        backgroundColor = Color.Red
    )
    
    TagGroup(
        tags = listOf(tag),
        text = "商品名称"
    )
}
```

### 多标签组合

```kotlin
@Composable
fun MultipleTagsExample() {
    val tags = listOf(
        TagBean(type = TagType.FILL, text = "新品", textColor = Color.White, backgroundColor = Color.Red),
        TagBean(type = TagType.STROKE, text = "包邮", textColor = Color.Green, borderColor = Color.Green),
        TagBean(type = TagType.DISCOUNT, text = "5折", textColor = Color.White, backgroundColor = Color.Orange, backgroundEndColor = Color.Red)
    )
    
    TagGroup(
        tags = tags,
        text = "Apple iPhone 15 Pro Max",
        onTagClick = { tag ->
            println("点击了: ${tag.text}")
        }
    )
}
```

## 🎨 标签类型

### 1. 填充标签 (FILL)
```kotlin
TagBean(
    type = TagType.FILL,
    text = "新品",
    textColor = Color.White,
    backgroundColor = Color.Red
)
```

### 2. 镂空标签 (STROKE)
```kotlin
TagBean(
    type = TagType.STROKE,
    text = "包邮",
    textColor = Color.Green,
    borderColor = Color.Green
)
```

### 3. 渐变标签
```kotlin
TagBean(
    type = TagType.FILL,
    text = "限时特价",
    textColor = Color.White,
    backgroundColor = Color.Red,
    backgroundEndColor = Color.Orange
)
```

### 4. 可点击标签
```kotlin
TagBean(
    type = TagType.FILL,
    text = "查看详情",
    textColor = Color.White,
    backgroundColor = Color.Blue,
    isClickable = true,
    clickToast = "点击查看更多"
)
```

## 📱 单行/多行控制

### 单行显示
```kotlin
TagGroup(
    tags = tags,
    text = "很长的商品名称描述...",
    maxLines = 1,
    overflow = TextOverflow.Ellipsis
)
```

### 多行显示
```kotlin
TagGroup(
    tags = tags,
    text = "商品详细描述信息",
    maxLines = 3,
    overflow = TextOverflow.Ellipsis
)
```

## 🔧 便捷方法

### 兼容原生API
```kotlin
// 矩形标签在前
ShowRectStart(
    tags = tags,
    content = "商品名称",
    maxLines = 1,
    overflow = TextOverflow.Ellipsis
)

// 圆角标签在后
ShowRoundEnd(
    tags = tags,
    content = "商品名称"
)

// 扩展函数方式（更简洁）
tags.showRectStart("商品名称")
tags.showRoundEnd("商品名称")
```

## 🎭 自定义样式

### 使用预定义样式
```kotlin
val tag = TagBean(
    type = TagType.FILL,
    text = "圆角标签",
    appearance = TagAppearance.Round
)
```

### 完全自定义
```kotlin
val customAppearance = TagAppearance(
    textSize = 16.sp,
    cornerRadius = 12.dp,
    horizontalPadding = 16.dp,
    verticalPadding = 8.dp,
    fontWeight = FontWeight.Bold
)

val customTag = TagBean(
    type = TagType.FILL,
    text = "自定义",
    appearance = customAppearance
)
```

## 🖼️ 图片标签

```kotlin
val imageTag = TagBean(
    type = TagType.IMAGE,
    imageUrl = "https://example.com/tag-image.png"
)

// 提供图片Painter
val imagePainters = mapOf(
    "https://example.com/tag-image.png" to rememberAsyncImagePainter("https://example.com/tag-image.png")
)

TagGroup(
    tags = listOf(imageTag),
    imagePainters = imagePainters,
    loadingContent = { CircularProgressIndicator() },
    errorContent = { Icon(Icons.Default.Error, contentDescription = null) }
)
```

## 🔍 调试模式

```kotlin
// 启用调试模式
TagUtils.isDebugMode = true

// 会自动检查配置问题并输出警告
TagGroup(tags = tags, text = content)
```

## 📊 实际应用场景

### 电商商品列表
```kotlin
@Composable
fun ProductListItem(product: Product) {
    val tags = listOf(
        TagBean(type = TagType.FILL, text = "新品", textColor = Color.White, backgroundColor = Color.Red),
        TagBean(type = TagType.STROKE, text = "包邮", textColor = Color.Green, borderColor = Color.Green)
    )
    
    Card {
        Column(modifier = Modifier.padding(16.dp)) {
            TagGroup(
                tags = tags,
                text = product.name,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            // 其他商品信息...
        }
    }
}
```

### 新闻资讯
```kotlin
@Composable
fun NewsItem(news: News) {
    val tags = listOf(
        TagBean(type = TagType.FILL, text = "热点", textColor = Color.White, backgroundColor = Color.Red),
        TagBean(type = TagType.STROKE, text = news.category, textColor = Color.Blue, borderColor = Color.Blue)
    )
    
    TagGroup(
        tags = tags,
        text = news.title,
        maxLines = 3,
        overflow = TextOverflow.Ellipsis
    )
}
```

## 🚀 从原生Android迁移

### API对应关系
```kotlin
// 原生Android -> Compose
TagUtils.showRectStart() -> ShowRectStart() 或 tags.showRectStart()
TagUtils.showRoundEnd() -> ShowRoundEnd() 或 tags.showRoundEnd()
textView.setMaxLines(1) -> maxLines = 1
textView.setEllipsize() -> overflow = TextOverflow.Ellipsis
```

### 数据结构兼容
```kotlin
// 原生TagBean -> Compose TagBean
val tag = TagBean(
    type = TagType.FILL, // 对应原生的form = 1
    text = "新品",       // 对应原生的tagName
    textColor = Color.White,
    backgroundColor = Color.Red
)
```

## 🎯 最佳实践

1. **性能优化**：使用 `remember` 缓存标签列表
2. **样式统一**：定义全局样式常量
3. **调试开发**：开发时启用调试模式
4. **图片优化**：合理使用图片缓存
5. **响应式设计**：根据屏幕尺寸调整标签大小

## 📚 更多资源

- [完整API文档](API.md)
- [迁移指南](MIGRATION_GUIDE.md)
- [使用示例](USAGE_EXAMPLES.md)
- [演示应用](src/commonMain/kotlin/com/taglib/demo/)

## 🤝 获得帮助

- 查看演示应用了解所有功能
- 启用调试模式获得开发提示
- 参考完整的使用示例
- 提交Issue获得技术支持

开始享受现代化的标签开发体验吧！🎉
