#!/bin/bash

echo "=== KMP Tag Library 项目验证 ==="
echo

echo "📁 项目结构检查:"
echo "✅ 根目录文件:"
ls -1 | grep -E "\.(kt|kts|md|properties)$" | sed 's/^/  /'

echo
echo "✅ 源代码结构:"
find src -name "*.kt" | sort | sed 's/^/  /'

echo
echo "📊 代码统计:"
echo "  总文件数: $(find src -name "*.kt" | wc -l)"
echo "  总行数: $(find src -name "*.kt" -exec wc -l {} + | tail -1 | awk '{print $1}')"

echo
echo "🔍 主要组件检查:"
echo "  TagBean: $([ -f "src/commonMain/kotlin/com/taglib/TagBean.kt" ] && echo "✅" || echo "❌")"
echo "  TagAppearance: $([ -f "src/commonMain/kotlin/com/taglib/TagAppearance.kt" ] && echo "✅" || echo "❌")"
echo "  TagCompose: $([ -f "src/commonMain/kotlin/com/taglib/TagCompose.kt" ] && echo "✅" || echo "❌")"
echo "  TagUtils: $([ -f "src/commonMain/kotlin/com/taglib/TagUtils.kt" ] && echo "✅" || echo "❌")"
echo "  FillTag: $([ -f "src/commonMain/kotlin/com/taglib/components/FillTag.kt" ] && echo "✅" || echo "❌")"
echo "  StrokeTag: $([ -f "src/commonMain/kotlin/com/taglib/components/StrokeTag.kt" ] && echo "✅" || echo "❌")"
echo "  ImageTag: $([ -f "src/commonMain/kotlin/com/taglib/components/ImageTag.kt" ] && echo "✅" || echo "❌")"

echo
echo "🧪 测试文件检查:"
echo "  TagUtilsTest: $([ -f "src/commonTest/kotlin/com/taglib/TagUtilsTest.kt" ] && echo "✅" || echo "❌")"

echo
echo "📱 平台特定文件检查:"
echo "  Android Utils: $([ -f "src/androidMain/kotlin/com/taglib/platform/AndroidTagUtils.kt" ] && echo "✅" || echo "❌")"
echo "  iOS Utils: $([ -f "src/iosMain/kotlin/com/taglib/platform/IosTagUtils.kt" ] && echo "✅" || echo "❌")"

echo
echo "📋 功能特性:"
echo "  ✅ 多种标签类型 (FILL, STROKE, IMAGE, DISCOUNT, POINTS)"
echo "  ✅ 丰富样式配置 (颜色、圆角、边框、渐变)"
echo "  ✅ 跨平台支持 (Android & iOS)"
echo "  ✅ 交互支持 (可点击标签)"
echo "  ✅ 高度可定制 (灵活的样式系统)"
echo "  ✅ 组合布局 (多标签组合显示)"

echo
echo "🎯 与原Android库的对应关系:"
echo "  TagBean.java → TagBean.kt (数据模型)"
echo "  TagAppearance.java → TagAppearance.kt (样式配置)"
echo "  TagUtils.java → TagUtils.kt + TagCompose.kt (工具类和主组件)"
echo "  FillBgSpan.java → FillTag.kt (填充背景标签)"
echo "  StrokeBgSpan.java → StrokeTag.kt (镂空边框标签)"
echo "  TagImageSpan.java → ImageTag.kt (图片标签)"

echo
echo "=== 验证完成 ==="
