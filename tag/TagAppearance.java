/*
 * Copyright (c) 2024. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package lib.component.tag;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.text.TextUtils;

import androidx.annotation.DrawableRes;
import androidx.viewpager.widget.PagerTitleStrip;

/**
 * TagAppearance
 * <p>
 * 标签样式
 * <p>
 * Author ye.xue
 * <p>
 * Date 2021/3/4
 */
public class TagAppearance {

    //无颜色标志位
    public static final int COLOR_NONE = 1;

    //标签与边框间距默认值
    public static final float PADDING = -1F;
    //圆角弧度-默认
    public static final float CORNER_TYPE_DEFAULT = -2F;
    //圆角弧度-半圆
    public static final float CORNER_TYPE_CIRCLE = -1F;

    public static final TagAppearance DEFAULT = new Builder().build();
    public static final TagAppearance ROUND = new Builder().cornerSizeDp(CORNER_TYPE_CIRCLE).build();

    public static class Builder {

        private float tagTextSize = -1F;

        private float fixedTagTextSize = -1F;
        //非-1F则Tag的高度固定
        private float tagHeight = -1F;
        private boolean tagTextBold = false;
        private float lineSpacingExtra = 0F;
        private float borderSizeDp = 0.5F;
        private float cornerSizeDp = CORNER_TYPE_DEFAULT;
        private float textMarginDp = 4F;
        private float tagMarginDp = 5F;
        private float dpPaddingV = PADDING;
        private float dpPaddingH = PADDING;
        private float imgHeightLimit = 0.95F;
        private float circleFrameScale = 1.05F;
        private float frontImageRate = 0.8F;
        private float defaultTagTextSizeRate = 0.72F;
        private float defaultRadiusRate = 0.2F;
        private float defaultPaddingVerticalRate = 0.10F;
        private float defaultPaddingHorizontalRate = 0.32F;

        //积分标的本地图片
        private @DrawableRes int jfResId;

        public TagAppearance build() {
            return new TagAppearance(this);
        }

        /**
         * 标签文字大小dp, <=0根据外部文字大小乘以比例做默认处理
         *
         * @param tagTextSize float
         * @return Builder
         */
        public Builder tagTextSize(float tagTextSize) {
            this.tagTextSize = tagTextSize;
            return this;
        }


        /**
         * 标签高度固定时 标签内字体的大小
         *
         * @param fixedTagTextSize
         * @return
         */
        public Builder fixedTagTextSize(float fixedTagTextSize) {
            this.fixedTagTextSize = fixedTagTextSize;
            return this;
        }

        /**
         * 标签高度   -1则根据外部文字大小乘以比例默认处理
         *
         * @param tagHeight
         * @return
         */
        public Builder tagHeight(float tagHeight) {
            this.tagHeight = tagHeight;
            return this;
        }

        /**
         * 行与行之间的间距
         *
         * @param lineSpacingExtra
         * @return
         */
        public Builder lineSpacingExtra(float lineSpacingExtra) {
            this.lineSpacingExtra = lineSpacingExtra;
            return this;
        }

        /**
         * 标签文字是否加粗
         *
         * @param tagTextBold true-加粗，false-不加粗
         * @return Builder
         */
        public Builder tagTextBold(boolean tagTextBold) {
            this.tagTextBold = tagTextBold;
            return this;
        }

        /**
         * 边框尺寸dp,默认0.5dp
         *
         * @param borderSizeDp float
         * @return Builder
         */
        public Builder borderSizeDp(float borderSizeDp) {
            this.borderSizeDp = borderSizeDp;
            return this;
        }

        /**
         * 边框弧度
         * -2为默认处理,-1为圆形,>=0为实际dp
         *
         * @param cornerSizeDp float
         * @return Builder
         */
        public Builder cornerSizeDp(float cornerSizeDp) {
            this.cornerSizeDp = cornerSizeDp;
            return this;
        }

        /**
         * 标签与前后文字间距,默认4dp
         *
         * @param textMarginDp float
         * @return Builder
         */
        public Builder textMarginDp(float textMarginDp) {
            this.textMarginDp = textMarginDp;
            return this;
        }

        /**
         * 标签之间间距,默认5dp
         *
         * @param tagMarginDp float
         * @return Builder
         */
        public Builder tagMarginDp(float tagMarginDp) {
            this.tagMarginDp = tagMarginDp;
            return this;
        }

        /**
         * 标签与边框之间竖直距离,<=0则默认处理
         *
         * @param dpPaddingV float
         * @return Builder
         */
        public Builder dpPaddingV(float dpPaddingV) {
            this.dpPaddingV = dpPaddingV;
            return this;
        }

        /**
         * 标签与边框之间水平距离,<=0则默认处理
         *
         * @param dpPaddingH float
         * @return dpPaddingH
         */
        public Builder dpPaddingH(float dpPaddingH) {
            this.dpPaddingH = dpPaddingH;
            return this;
        }

        /**
         * 图片标签行高限制, 超过行高则进行缩放
         * 默认最大1倍文字高度
         *
         * @param rate float
         * @return Builder
         */
        public Builder imgHeightLimit(float rate) {
            this.imgHeightLimit = rate;
            return this;
        }

        /**
         * 单个字符标签显示为正方形或圆形时, 边框的缩放比例
         *
         * @param circleFrameScale float
         * @return Builder
         */
        public Builder circleFrameScale(float circleFrameScale) {
            this.circleFrameScale = circleFrameScale;
            return this;
        }

        /**
         * 标签文字的默认处理,与外部文字的比例
         *
         * @param defaultTagTextSizeRate float
         * @return Builder
         */
        public Builder defaultTagTextSizeRate(float defaultTagTextSizeRate) {
            this.defaultTagTextSizeRate = defaultTagTextSizeRate;
            return this;
        }

        /**
         * 默认圆角弧度与标签高度比例
         *
         * @param defaultRadiusRate float
         * @return Builder
         */
        public Builder defaultRadiusRate(float defaultRadiusRate) {
            this.defaultRadiusRate = defaultRadiusRate;
            return this;
        }

        /**
         * 默认标签与上下边框默认值比例
         *
         * @param defaultPaddingVerticalRate float
         * @return Builder
         */
        public Builder defaultPaddingVerticalRate(float defaultPaddingVerticalRate) {
            this.defaultPaddingVerticalRate = defaultPaddingVerticalRate;
            return this;
        }

        /**
         * 默认标签与左右边框默认值比例
         *
         * @param defaultPaddingHorizontalRate float
         * @return Builder
         */
        public Builder defaultPaddingHorizontalRate(float defaultPaddingHorizontalRate) {
            this.defaultPaddingHorizontalRate = defaultPaddingHorizontalRate;
            return this;
        }

        /**
         * 积分标的积分图片
         * @param jfResId
         * @return
         */
        public Builder jfResId(@DrawableRes int jfResId) {
            this.jfResId = jfResId;
            return this;
        }

        /**
         * 积分标icon大小和框的比例
         * @param frontImgRate
         * @return
         */
        public Builder frontImgRate(float frontImgRate){
            this.frontImageRate = frontImgRate;
            return this;
        }
    }

    public final float tagTextSizeDp;

    public final float fixedTagTextSizeDp;
    public final float tagHeightDp;
    public final boolean tagTextBold;
    public final float lineSpacingExtraDp;
    public final float borderSizeDp;
    public final float cornerSizeDp;
    public final float textMarginDp;
    public final float tagMarginDp;
    public final float dpPaddingV;
    public final float dpPaddingH;
    public final float imgHeightLimit;
    public final float circleFrameScale;
    public final float defaultTagTextSizeRate;
    public final float defaultRadiusRate;
    public final float defaultPaddingVerticalRate;
    public final float defaultPaddingHorizontalRate;

    public final @DrawableRes int jfResId;
    public final float frontImgRate;

    private TagAppearance(Builder builder) {
        this.tagTextSizeDp = builder.tagTextSize;
        this.fixedTagTextSizeDp = builder.fixedTagTextSize;
        this.tagHeightDp = builder.tagHeight;
        this.tagTextBold = builder.tagTextBold;
        this.borderSizeDp = builder.borderSizeDp;
        this.cornerSizeDp = builder.cornerSizeDp;
        this.textMarginDp = builder.textMarginDp;
        this.tagMarginDp = builder.tagMarginDp;
        this.dpPaddingV = builder.dpPaddingV;
        this.dpPaddingH = builder.dpPaddingH;
        this.imgHeightLimit = builder.imgHeightLimit;
        this.circleFrameScale = builder.circleFrameScale;
        this.defaultTagTextSizeRate = builder.defaultTagTextSizeRate;
        this.defaultRadiusRate = builder.defaultRadiusRate;
        this.defaultPaddingVerticalRate = builder.defaultPaddingVerticalRate;
        this.defaultPaddingHorizontalRate = builder.defaultPaddingHorizontalRate;
        this.lineSpacingExtraDp = builder.lineSpacingExtra;
        this.jfResId = builder.jfResId;
        this.frontImgRate = builder.frontImageRate;
    }

    /**
     * 确定标签文字大小
     *
     * @param context  Context
     * @param rawPaint Paint
     * @return 实际标签文字大小 px
     */
    float getTagTextSize(Context context, Paint rawPaint, boolean useFixedTagHeight) {

        if (useFixedTagHeight && fixedTagTextSizeDp > 0) {
            return TagUtils.dpToPx(context, fixedTagTextSizeDp);
        }

        if (tagTextSizeDp > 0F) {
            return TagUtils.dpToPx(context, tagTextSizeDp);
        } else {
            return rawPaint.getTextSize() * defaultTagTextSizeRate;
        }
    }

    /**
     * 处理圆角弧度
     *
     * @param c           Context
     * @param frameHeight 边框高度
     * @return 最终圆角弧度
     */
    float getCornerSize(Context c, float frameHeight) {
        if (cornerSizeDp == CORNER_TYPE_DEFAULT) {
            //默认弧度
            return frameHeight * defaultRadiusRate;
        } else if (cornerSizeDp == CORNER_TYPE_CIRCLE) {
            //半圆弧度
            return frameHeight / 2F;
        } else {
            return Math.max(0F, TagUtils.dpToPx(c, cornerSizeDp));
        }
    }

    /**
     * 确定标签文字与上下边框间距
     *
     * @param context     Context
     * @param tagTextSize 打标文字大小
     * @return 实际间距px
     */
    float getTagTextPaddingV(Context context, float tagTextSize) {
        if (dpPaddingV < 0F) {
            return tagTextSize * defaultPaddingVerticalRate;
        } else {
            return TagUtils.dpToPx(context, dpPaddingV);
        }
    }


    /**
     * 确定标签文字与左右边框间距
     *
     * @param context     Context
     * @param tagTextSize 打标文字大小
     * @return 实际间距px
     */
    float getTagTextPaddingH(Context context, float tagTextSize) {
        if (dpPaddingH < 0F) {
            return tagTextSize * defaultPaddingHorizontalRate;
        } else {
            return TagUtils.dpToPx(context, dpPaddingH);
        }
    }

    /**
     * 单个字符下显示成正方形标签的长宽处理
     *
     * @param tagPaint 打标paint
     * @param paddingV 文字与边框间距
     * @return float 宽=高
     */
    float dealForSquareFrame(Paint tagPaint, float paddingV) {
        //如果是单字标签,显示成正方形,宽=高
        if (cornerSizeDp == CORNER_TYPE_CIRCLE) {
            //半圆角的情况下调整需要稍微调大边距
            return TagUtils.getTextHeight(tagPaint) + 2 * paddingV * circleFrameScale;
        } else {
            return TagUtils.getTextHeight(tagPaint) + 2 * paddingV;
        }
    }

    /**
     * 解析渐变背景色终止颜色
     * color值1做为特殊标志位, 如果有color解析为1,修改成2
     *
     * @param bgColorEnd String
     * @return int
     */
    static int getBgEndColor(String bgColorEnd) {
        if (TextUtils.isEmpty(bgColorEnd)) {
            return COLOR_NONE;
        } else {
            int colorBg = safeParseColor(bgColorEnd, Color.WHITE);
            return colorBg == COLOR_NONE ? (COLOR_NONE + 1) : colorBg;
        }
    }

    /**
     * 解析color值
     *
     * @param colorStr     color字符串
     * @param defaultColor 解析失败时默认值
     * @return int
     */
    static int safeParseColor(String colorStr, int defaultColor) {
        if (TextUtils.isEmpty(colorStr)) return defaultColor;
        try {
            return Color.parseColor(colorStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return defaultColor;
    }
}
