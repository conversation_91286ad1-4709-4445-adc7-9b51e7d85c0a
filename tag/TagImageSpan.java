/*
 * Copyright (c) 2024. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package lib.component.tag;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.style.ImageSpan;

import androidx.annotation.NonNull;

/**
 * TagImageSpan
 * 图片标签
 * Created by <PERSON> on 2016/2/19.
 * Modified by ye.xue on 2021/03/05
 */
public class TagImageSpan extends ImageSpan {

    private final TagBean data;

    private final float textMargin;
    private final float tagMargin;

    private final Context context;

    /**
     * 构造方法
     *
     * @param context Context
     * @param b       Bitmap
     * @param bean    TagBean
     */
    public TagImageSpan(Context context, Bitmap b, TagBean bean) {
        super(context, b);
        this.context = context;
        this.data = bean;
        if (this.data.appearance == null) {
            this.data.appearance = TagAppearance.DEFAULT;
        }
        textMargin = TagUtils.dpToPx(context, this.data.appearance.textMarginDp);
        tagMargin = TagUtils.dpToPx(context, this.data.appearance.tagMarginDp);
    }

    @Override
    public int getSize(@NonNull Paint paint, CharSequence text, int start, int end, Paint.FontMetricsInt fm) {
        Rect rect = getDrawable().getBounds();
        Paint.FontMetricsInt textFM = paint.getFontMetricsInt();
        int textHeight = -textFM.top;
        //图片高度限制处理
        if (data.appearance.tagHeightDp > 0 && data.useFixedTagHeight) {
            if ((rect.bottom - rect.top) != TagUtils.dpToPx(context, data.appearance.tagHeightDp)) {
                float scale = (float) TagUtils.dpToPx(context, data.appearance.tagHeightDp) / (rect.bottom - rect.top);
                rect.right = (int) (rect.left + (rect.right - rect.left) * scale);
                rect.bottom = (int) (rect.top + (rect.bottom - rect.top) * scale);
            }
        } else {
            if ((rect.bottom - rect.top) != data.appearance.imgHeightLimit * textHeight) {
                float scale = data.appearance.imgHeightLimit * textHeight / (rect.bottom - rect.top);
                rect.right = (int) (rect.left + (rect.right - rect.left) * scale);
                rect.bottom = (int) (rect.top + (rect.bottom - rect.top) * scale);
            }
        }

        //行高处理
        if (fm != null) {
            int picHeight = rect.bottom - rect.top;
            int top = picHeight / 2 - textHeight / 4;
            int bottom = picHeight / 2 + textHeight / 4;
            fm.ascent = -bottom;
            fm.top = -bottom;
            fm.bottom = top;
            fm.descent = top;
            //单独只有图片的情况下高度会不足,这里纠正
            if (data.tagCount == 1 && !data.hasText) {
                paint.getFontMetricsInt(fm);
            }
        }
        int spanWidth = rect.right - rect.left;

        //多个标签,添加标签间距
        if (!data.isLastTag()) {
            spanWidth += tagMargin;
        }
        //从开始位置显示,最后一个标签,并且后面有文字内容,添加标签与文字间距
        if (data.fromStart && data.hasText && data.isLastTag()) {
            spanWidth += textMargin;
        }

        //从结束位置显示,第一个标签,添加便签与文字间的间距
        if (!data.fromStart && data.isFirstTag()) {
            spanWidth += textMargin;
        }
        return spanWidth;
    }

    @Override
    public void draw(Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, @NonNull Paint paint) {
        canvas.save();
        Drawable drawable = getDrawable();
        Paint.FontMetrics fm = paint.getFontMetrics();
        float rawTop = y + fm.ascent;
        float rawBottom = y + fm.descent;
        float transY = rawTop + (rawBottom - rawTop - drawable.getBounds().bottom) / 2F;
        if (!data.fromStart && data.isFirstTag()) {
            canvas.translate(x + textMargin, transY);
        } else {
            canvas.translate(x, transY);
        }
        drawable.draw(canvas);
        canvas.restore();
    }
}
