/*
 * Copyright (c) 2024. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

package lib.component.tag;

/**
 * TagBean
 * 打标数据结构
 * Author ye.xue
 * Date 2019/9/18
 */
public class TagBean {
    //1 代表采用调色板格式，2 代表采用网络图片，3 代表本地绘制镂空边框标签
    public int form;
    //打标名称
    public String tagName;
    //字体颜色
    public String textColor;
    //背景颜色
    public String bgColor;
    //背景颜色2 如果不为0则背景是渐变色
    public String bgColorEnd;
    //边框颜色
    public String borderColor;
    //图片地址
    public String picUrl;
    //开始字符索引
    public int start;
    //结束字符索引
    public int end;
    //标签索引
    public int tagIndex;
    //标签总数
    public int tagCount;
    //标签显示位置,true-拼接在文字前面,false-显示在文字后面
    public boolean fromStart = true;
    //是否有文字
    public boolean hasText;
    //标签尺寸样式
    public TagAppearance appearance;
    //是否启用配置的固定标签高度
    public boolean useFixedTagHeight = false;
    //控制tag是否可以点击，可以点击的tag会带箭头
    public boolean isClick;
    //按件称重标弹窗内容
    public String toast;

    /**
     * 构造方法
     *
     * @param form        标签类型
     * @param tagName     标签文字
     * @param textColor   标签文字颜色
     * @param bgColor     标签背景颜色
     * @param borderColor 标签边框颜色
     * @param picUrl      图片标签url
     */
    public TagBean(int form, String tagName, String textColor, String bgColor, String borderColor, String picUrl) {
        this(form, tagName, textColor, bgColor, "", borderColor, picUrl);
    }

    /**
     * 构造方法
     *
     * @param form        标签类型
     * @param tagName     标签文字
     * @param textColor   标签文字颜色
     * @param bgColor     标签背景颜色
     * @param bgColorEnd  标签背景颜色2,如果有值则背景为渐变色
     * @param borderColor 标签边框颜色
     * @param picUrl      图片标签url
     */
    public TagBean(int form, String tagName, String textColor, String bgColor, String bgColorEnd, String borderColor, String picUrl) {
        this.form = form;
        this.tagName = tagName;
        this.textColor = textColor;
        this.bgColor = bgColor;
        this.bgColorEnd = bgColorEnd;
        this.borderColor = borderColor;
        this.picUrl = picUrl;
    }

    /**
     * 构造方法
     *
     * @param form        标签类型
     * @param tagName     标签文字
     * @param textColor   标签文字颜色
     * @param bgColor     标签背景颜色
     * @param bgColorEnd  标签背景颜色2,如果有值则背景为渐变色
     * @param borderColor 标签边框颜色
     * @param picUrl      图片标签url
     * @param isClick     是否可以点击
     * @param toast     按件称重标弹窗内容
     */
    public TagBean(int form, String tagName, String textColor, String bgColor, String bgColorEnd, String borderColor, String picUrl, boolean isClick, String toast) {
        this.form = form;
        this.tagName = tagName;
        this.textColor = textColor;
        this.bgColor = bgColor;
        this.bgColorEnd = bgColorEnd;
        this.borderColor = borderColor;
        this.picUrl = picUrl;
        this.isClick = isClick;
        this.toast = toast;
    }

    /**
     * 自定义打标样式
     *
     * @param appearance TagAppearance
     */
    public void setTagAppearance(TagAppearance appearance) {
        this.appearance = appearance;
    }

    /**
     * 设置内置属性
     *
     * @param start        开始位置
     * @param end          结束位置
     * @param tagIndex     标签索引
     * @param tagCount     标签总数
     * @param fromStart    是否从头显示
     * @param hasAfterText 标签最后是否有文本
     */
    public void setProperties(int start, int end, int tagIndex, int tagCount, boolean fromStart, boolean hasAfterText) {
        this.start = start;
        this.end = end;
        this.tagIndex = tagIndex;
        this.tagCount = tagCount;
        this.fromStart = fromStart;
        this.hasText = hasAfterText;
    }

    public boolean isFirstTag() {
        return tagIndex == 0;
    }

    public boolean isLastTag() {
        return tagIndex == (tagCount - 1);
    }
}
